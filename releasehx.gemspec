require 'yaml'
begin
  require_relative 'lib/releasehx/generated'
rescue LoadError
  warn 'WARNING: Generated metadata file missing. Run `rake prebuild` first.'
  module ReleaseHx
    ATTRIBUTES = { globals: {} }
  end
end

attrs = ReleaseHx::ATTRIBUTES[:globals]

Gem::Specification.new do |spec|
  spec.name          = 'releasehx'
  spec.version       = attrs['this_prod_vrsn'] || '0.0.0-alpha'
  spec.authors       = ['DocOpsLab']
  spec.email         = ['<EMAIL>']

  spec.summary       = attrs['tagline'] || 'No summary available'
  spec.description   = attrs['description'] || 'No description available'
  spec.homepage      = 'https://github.com/DocOPs/releasehx'
  spec.license       = 'MIT'

  spec.required_ruby_version = '>= 3.0.0'

  spec.metadata['allowed_push_host'] = 'https://rubygems.org'

  spec.files = Dir['lib/**/*.rb'] + Dir['lib/templates/*'] + Dir['bin/*'] + Dir['_mappings/*'] + ['README.adoc'] + ['specs/config-def.yml'] + Dir['build/snippets/*.txt'] + Dir['build/docs/*']

  spec.bindir        = 'bin'
  spec.executables   = ['releasehx', 'rhx']
  spec.require_paths = ['lib']

  spec.add_development_dependency 'bundler',      '~> 2.0'
  spec.add_development_dependency 'rspec',        '~> 3.0'
  spec.add_development_dependency 'rake', '~> 13'

  spec.add_dependency 'thor', '~> 1.3'
  spec.add_dependency 'rugged', '~> 1.7'
  spec.add_dependency 'faraday', '~> 2.9'

  spec.add_dependency 'tilt', '~> 2.3'
  spec.add_dependency 'yaml', '~> 0.2'
  spec.add_dependency 'jsonpath', '~> 1.1'
  spec.add_dependency 'jmespath', '~> 1.6'

  spec.add_dependency 'liquid', '~> 4.0'
  spec.add_dependency 'jekyll', '~> 4.4'
  spec.add_dependency 'jekyll-asciidoc', '~> 3.0.0'
  spec.add_dependency 'kramdown', '~> 2.4'
  spec.add_dependency 'commonmarker', '~> 0.23'
  spec.add_dependency 'kramdown-asciidoc', '~> 2.1'
end
