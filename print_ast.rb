require 'asciidoctor'

# Load the AsciiDoc file into a document object
doc = Asciidoctor.load_file('README.adoc', safe: :safe)

# List all available methods for doc.nodes objects
puts "DOC metods:"
# puts doc.methods.sort


# Recursive function to print the structure of the document
def print_structure(node, indent = 0)
  puts "#{' ' * indent}#{node.node_name}: #{node.title}" unless node.title.empty?
  puts "#{' ' * indent}#{node.roles}" unless node.roles.empty?
  puts "#{' ' * indent}#{node.id}" unless node.id.empty?
  node.blocks.each do |block|
    print_structure(block, indent + 2)
  end
end

# Print the structure of the document starting with the root
print_structure(doc)
