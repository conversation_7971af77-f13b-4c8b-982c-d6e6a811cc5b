This file is a merged representation of the entire codebase, combined into a single document by Repomix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
4. Repository files, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

<additional_info>

</additional_info>

</file_summary>

<directory_structure>
_mappings/
  customfield-note/
    jira.yaml
  description-note/
    jira.yaml
configs/
  jira-customfield.yml
issues/
  jira-customfield-note-1.1.0.json
  jira-description-note-1.1.0.json
src/
  acmedoc-1.1.0.yml
.gitignore
Gemfile
log.txt
README.adoc
test.adoc
yaml
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="configs/jira-customfield.yml">
api:
  from: jira
  version: 2
  label: JIRA v2 API
  format: json

paths:
  drafts_dir: drafts
  payloads_dir: issues
  templates_dir: _templates
  custom_mappings_dir: _mappings

sources:
  summ: issue_heading
  note: custom_field
  note_custom_field: customfield_10010
  markup: markdown
  engine: redcarpet

templates:
  chid: "{{ release.code | replace: '.', '_' | upcase }}-{{ this.tick }}"

parts:
  webui:
    head: Web UI
    icon: desktop # desktop
    text: Web UI
  api:
    head: API
    icon: server
    text: API
  installer:
    head: Installer
    icon: cloud-download
    text: Installer

rhyml:
  pasterize_head: true

history:
  items: 
    empty_groups: |
      No changes in this {{ group_type }} category.

changelog:
  items:
    frame: paragraph
    # metadata_icons: false

notes:
  spot: 2
  sort: ['part:grouping1']
  items:
    frame: table-cols-2
    allow_redundant: true
</file>

<file path="log.txt">
[DEBUG] CustomLiquid::FileSystem defined? constant
INFO: Invoked default task with version/source=1.1.0
INFO: Arguing for (and found) config file configs/jira-customfield.yml
INFO: Loading configuration from: configs/jira-customfield.yml
META for history.head: {"delay"=>true, "default"=>"liquid"}
DEBUG: Loaded user config: {"api"=>{"from"=>"jira", "version"=>2, "label"=>"JIRA v2 API", "format"=>"json"}, "paths"=>{"drafts_dir"=>"drafts", "payloads_dir"=>"issues", "templates_dir"=>"_templates", "custom_mappings_dir"=>"_mappings"}, "sources"=>{"summ"=>"issue_heading", "note"=>"custom_field", "note_custom_field"=>"customfield_10010", "markup"=>"markdown", "engine"=>"redcarpet"}, "templates"=>{"chid"=>"{{ release.code | replace: '.', '_' | upcase }}-{{ this.tick }}"}, "parts"=>{"webui"=>{"head"=>"Web UI", "icon"=>"&#xf109;", "text"=>"Web UI"}, "api"=>{"head"=>"API", "icon"=>"&#xf007;", "text"=>"API"}, "installer"=>{"head"=>"Installer", "icon"=>"&#xf007;", "text"=>"Installer"}}}
[DEBUG] Calling precompile_from_schema! recursively with: path=_meta, key=_meta
[DEBUG] Calling precompile_from_schema! recursively with: path=api, key=api
[DEBUG] Calling precompile_from_schema! recursively with: path=api.auth, key=auth
[DEBUG] Calling precompile_from_schema! recursively with: path=sources, key=sources
[DEBUG] Calling precompile_from_schema! recursively with: path=extensions, key=extensions
[DEBUG] Calling precompile_from_schema! recursively with: path=types, key=types
[DEBUG] Calling precompile_from_schema! recursively with: path=types.feature, key=feature
[DEBUG] Calling precompile_from_schema! recursively with: path=types.bug, key=bug
[DEBUG] Calling precompile_from_schema! recursively with: path=types.improvement, key=improvement
[DEBUG] Calling precompile_from_schema! recursively with: path=types.documentation, key=documentation
[DEBUG] Calling precompile_from_schema! recursively with: path=types.<type_name>, key=<type_name>
[DEBUG] Calling precompile_from_schema! recursively with: path=parts, key=parts
[DEBUG] Calling precompile_from_schema! recursively with: path=parts.<part_name>, key=<part_name>
[DEBUG] Calling precompile_from_schema! recursively with: path=parts.webui, key=webui
[DEBUG] Calling precompile_from_schema! recursively with: path=parts.api, key=api
[DEBUG] Calling precompile_from_schema! recursively with: path=parts.installer, key=installer
[DEBUG] Calling precompile_from_schema! recursively with: path=tags, key=tags
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.highlight, key=highlight
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.deprecation, key=deprecation
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.breaking, key=breaking
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.removal, key=removal
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.security, key=security
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.experimental, key=experimental
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.internal, key=internal
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.changelog, key=changelog
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.release_note_needed, key=release_note_needed
[DEBUG] Calling precompile_from_schema! recursively with: path=tags.<your_tag_name>, key=<your_tag_name>
[DEBUG] Calling precompile_from_schema! recursively with: path=links, key=links
[DEBUG] Calling precompile_from_schema! recursively with: path=paths, key=paths
[DEBUG] Calling precompile_from_schema! recursively with: path=templates, key=templates
[DEBUG] Calling precompile_from_schema! recursively with: path=modes, key=modes
[DEBUG] Calling precompile_from_schema! recursively with: path=elements, key=elements
[DEBUG] Calling precompile_from_schema! recursively with: path=history, key=history
[COMPILE] Compiling templated field at path: history.head
[COMPILE] Precompiling field: history.head
[COMPILE] Raw value: Release History -- {{ release.code }} - {{ release.date }}
[COMPILE] Engine: liquid
[COMPILE] Delay? true
[DEBUG] Calling precompile_from_schema! recursively with: path=history.policies, key=policies
[DEBUG] Calling precompile_from_schema! recursively with: path=changelog, key=changelog
[DEBUG] Calling precompile_from_schema! recursively with: path=changelog.policies, key=policies
[DEBUG] Calling precompile_from_schema! recursively with: path=notes, key=notes
[DEBUG] Calling precompile_from_schema! recursively with: path=notes.policies, key=policies
[TEST] history.head is templated? true
config.history.head = Release History --  - 
class: SchemaGraphy::Templating::TemplatedField
[TEST] class of config.settings['history']['head']: SchemaGraphy::Templating::TemplatedField
INFO: Source type is: version
INFO: API is: jira
INFO: Generating draft only.
INFO: Loading JSON issues from issues/jira-customfield-note-1.1.0.json
DEBUG: About to do adapter.to_release
DEBUG: [SCAN] Adapter.to_release called (scan = false)
DEBUG: Extracted raw_items (9) from path 'issues'
DEBUG: First raw item: {"id"=>"1001", "key"=>"PROJ-310", "fields"=>{"issueType"=>{"name"=>"Improvement"}, "summary"=>"Add inline editing to dashboard widgets", "description"=>"Implements interactive dashboard widgets using JavaScript hooks and async save.", "customfield_10010"=>"Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.", "components"=>[{"name"=>"webui"}], "labels"=>["highlighted", "changelog", "release_note_needed"], "assignee"=>{"displayName"=>"julie"}, "development"=>{"commits"=>[{"type"=>"commit", "sha"=>"a1b2c3d4e5f60718293a4b5c6d7e8f9012345678"}]}}}
DEBUG: Release initialized with changes (post-compact):
DEBUG: [SCAN] Mapping 9 raw items...
DEBUG: Mapped field 'tick': "PROJ-310"
DEBUG: Mapped field 'type': "improvement"
DEBUG: Mapped field 'part': "webui"
DEBUG: Mapped field 'hash': "a1b2c3d4e5f60718293a4b5c6d7e8f9012345678"
DEBUG: Mapped field 'summ': "Add inline editing to dashboard widgets"
DEBUG: Mapped field 'note': "Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks."
DEBUG: Mapped field 'tags': ["highlighted", "changelog", "release_note_needed"]
DEBUG: Mapped field 'lead': "julie"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-310", "chid"=>"1_1_0-PROJ-310", "type"=>"improvement", "part"=>"webui", "hash"=>"a1b2c3d4e5f60718293a4b5c6d7e8f9012345678", "summ"=>"Add inline editing to dashboard widgets", "note"=>"Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.", "tags"=>["highlighted", "changelog", "release_note_needed"], "lead"=>"julie"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-310", "chid"=>"1_1_0-PROJ-310", "type"=>"improvement", "part"=>"webui", "hash"=>"a1b2c3d4e5f60718293a4b5c6d7e8f9012345678", "summ"=>"Add inline editing to dashboard widgets", "note"=>"Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.", "tags"=>["highlight", "changelog", "release_note_needed"], "lead"=>"julie"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-310", "chid"=>"1_1_0-PROJ-310", "type"=>"improvement", "part"=>"webui", "hash"=>"a1b2c3d4e5f60718293a4b5c6d7e8f9012345678", "summ"=>"Add inline editing to dashboard widgets", "note"=>"Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.", "tags"=>["highlight", "changelog", "release_note_needed"], "lead"=>"julie"}
DEBUG: Initialized Change: PROJ-310 – Add inline editing to dashboard widgets
DEBUG: Mapped field 'tick': "PROJ-467"
DEBUG: Mapped field 'type': "improvement"
DEBUG: Mapped field 'part': "api"
DEBUG: Mapped field 'hash': "abc123abc123abc123abc123abc123abc123abcd"
DEBUG: Mapped field 'summ': "Add rate limiting headers to public API"
DEBUG: Mapped field 'note': "The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged."
DEBUG: Mapped field 'tags': ["breaking", "changelog", "release_note_needed"]
DEBUG: Mapped field 'lead': "devin"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-467", "chid"=>"1_1_0-PROJ-467", "type"=>"improvement", "part"=>"api", "hash"=>"abc123abc123abc123abc123abc123abc123abcd", "summ"=>"Add rate limiting headers to public API", "note"=>"The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.", "tags"=>["breaking", "changelog", "release_note_needed"], "lead"=>"devin"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-467", "chid"=>"1_1_0-PROJ-467", "type"=>"improvement", "part"=>"api", "hash"=>"abc123abc123abc123abc123abc123abc123abcd", "summ"=>"Add rate limiting headers to public API", "note"=>"The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.", "tags"=>["breaking", "changelog", "release_note_needed"], "lead"=>"devin"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-467", "chid"=>"1_1_0-PROJ-467", "type"=>"improvement", "part"=>"api", "hash"=>"abc123abc123abc123abc123abc123abc123abcd", "summ"=>"Add rate limiting headers to public API", "note"=>"The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.", "tags"=>["breaking", "changelog", "release_note_needed"], "lead"=>"devin"}
DEBUG: Initialized Change: PROJ-467 – Add rate limiting headers to public API
DEBUG: Mapped field 'tick': "PROJ-983"
DEBUG: Mapped field 'type': "bug"
DEBUG: Mapped field 'part': "webui"
DEBUG: Mapped field 'hash': "deadbeef0000000000000000000000000000feed"
DEBUG: Mapped field 'summ': "Fix modal overflow on small screens"
DEBUG: Mapped field 'note': "Modals now scroll correctly on devices with < 600px screen width."
DEBUG: Mapped field 'tags': ["changelog", "release_note_needed"]
DEBUG: Mapped field 'lead': "sam"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-983", "chid"=>"1_1_0-PROJ-983", "type"=>"bug", "part"=>"webui", "hash"=>"deadbeef0000000000000000000000000000feed", "summ"=>"Fix modal overflow on small screens", "note"=>"Modals now scroll correctly on devices with < 600px screen width.", "tags"=>["changelog", "release_note_needed"], "lead"=>"sam"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-983", "chid"=>"1_1_0-PROJ-983", "type"=>"bug", "part"=>"webui", "hash"=>"deadbeef0000000000000000000000000000feed", "summ"=>"Fix modal overflow on small screens", "note"=>"Modals now scroll correctly on devices with < 600px screen width.", "tags"=>["changelog", "release_note_needed"], "lead"=>"sam"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-983", "chid"=>"1_1_0-PROJ-983", "type"=>"bug", "part"=>"webui", "hash"=>"deadbeef0000000000000000000000000000feed", "summ"=>"Fix modal overflow on small screens", "note"=>"Modals now scroll correctly on devices with < 600px screen width.", "tags"=>["changelog", "release_note_needed"], "lead"=>"sam"}
DEBUG: Initialized Change: PROJ-983 – Fix modal overflow on small screens
DEBUG: Mapped field 'tick': "PROJ-987"
DEBUG: Mapped field 'type': "bug"
DEBUG: Mapped field 'part': "api"
DEBUG: Mapped field 'hash': "facefeed1234567890abcdef1234567890abcdef"
DEBUG: Mapped field 'summ': "Fix JSON serialization issue with null values"
DEBUG: Mapped field 'note': "Previously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified."
DEBUG: Mapped field 'tags': ["release_note_needed"]
DEBUG: Mapped field 'lead': "cory"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-987", "chid"=>"1_1_0-PROJ-987", "type"=>"bug", "part"=>"api", "hash"=>"facefeed1234567890abcdef1234567890abcdef", "summ"=>"Fix JSON serialization issue with null values", "note"=>"Previously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.", "tags"=>["release_note_needed"], "lead"=>"cory"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-987", "chid"=>"1_1_0-PROJ-987", "type"=>"bug", "part"=>"api", "hash"=>"facefeed1234567890abcdef1234567890abcdef", "summ"=>"Fix JSON serialization issue with null values", "note"=>"Previously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.", "tags"=>["release_note_needed"], "lead"=>"cory"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-987", "chid"=>"1_1_0-PROJ-987", "type"=>"bug", "part"=>"api", "hash"=>"facefeed1234567890abcdef1234567890abcdef", "summ"=>"Fix JSON serialization issue with null values", "note"=>"Previously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.", "tags"=>["release_note_needed"], "lead"=>"cory"}
DEBUG: Initialized Change: PROJ-987 – Fix JSON serialization issue with null values
DEBUG: Mapped field 'tick': "PROJ-399"
DEBUG: Mapped field 'type': "improvement"
DEBUG: Mapped field 'part': "webui"
DEBUG: Mapped field 'hash': "ffeeddbbaa99887766554433221100aa00bbccdd"
DEBUG: Mapped field 'summ': "Add missing keyboard shortcuts to Help page"
DEBUG: Mapped field 'note': "The Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal"
DEBUG: Mapped field 'tags': ["release_note_needed"]
DEBUG: Mapped field 'lead': "robin"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-399", "chid"=>"1_1_0-PROJ-399", "type"=>"improvement", "part"=>"webui", "hash"=>"ffeeddbbaa99887766554433221100aa00bbccdd", "summ"=>"Add missing keyboard shortcuts to Help page", "note"=>"The Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal", "tags"=>["release_note_needed"], "lead"=>"robin"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-399", "chid"=>"1_1_0-PROJ-399", "type"=>"improvement", "part"=>"webui", "hash"=>"ffeeddbbaa99887766554433221100aa00bbccdd", "summ"=>"Add missing keyboard shortcuts to Help page", "note"=>"The Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal", "tags"=>["release_note_needed"], "lead"=>"robin"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-399", "chid"=>"1_1_0-PROJ-399", "type"=>"improvement", "part"=>"webui", "hash"=>"ffeeddbbaa99887766554433221100aa00bbccdd", "summ"=>"Add missing keyboard shortcuts to Help page", "note"=>"The Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal", "tags"=>["release_note_needed"], "lead"=>"robin"}
DEBUG: Initialized Change: PROJ-399 – Add missing keyboard shortcuts to Help page
DEBUG: Mapped field 'tick': "PROJ-200"
DEBUG: Mapped field 'type': "improvement"
DEBUG: Mapped field 'part': "api"
DEBUG: Mapped field 'hash': "55556666777788889999aaaabbbbccccddddeeee"
DEBUG: Mapped field 'summ': "Deprecate v1 endpoints for user settings"
DEBUG: Mapped field 'note': "The following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead."
DEBUG: Mapped field 'tags': ["deprecation", "release_note_needed"]
DEBUG: Mapped field 'lead': "amir"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-200", "chid"=>"1_1_0-PROJ-200", "type"=>"improvement", "part"=>"api", "hash"=>"55556666777788889999aaaabbbbccccddddeeee", "summ"=>"Deprecate v1 endpoints for user settings", "note"=>"The following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.", "tags"=>["deprecation", "release_note_needed"], "lead"=>"amir"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-200", "chid"=>"1_1_0-PROJ-200", "type"=>"improvement", "part"=>"api", "hash"=>"55556666777788889999aaaabbbbccccddddeeee", "summ"=>"Deprecate v1 endpoints for user settings", "note"=>"The following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.", "tags"=>["deprecation", "release_note_needed"], "lead"=>"amir"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-200", "chid"=>"1_1_0-PROJ-200", "type"=>"improvement", "part"=>"api", "hash"=>"55556666777788889999aaaabbbbccccddddeeee", "summ"=>"Deprecate v1 endpoints for user settings", "note"=>"The following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.", "tags"=>["deprecation", "release_note_needed"], "lead"=>"amir"}
DEBUG: Initialized Change: PROJ-200 – Deprecate v1 endpoints for user settings
DEBUG: Mapped field 'tick': "PROJ-1224"
DEBUG: Mapped field 'type': "improvement"
DEBUG: Mapped field 'part': "api"
DEBUG: Mapped field 'hash': "0000111122223333444455556666777788889999"
DEBUG: Mapped field 'summ': "Add support for custom fields"
DEBUG: Mapped field 'note': nil
DEBUG: Mapped field 'tags': ["release_note_needed"]
DEBUG: Mapped field 'lead': "devin"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-1224", "chid"=>"1_1_0-PROJ-1224", "type"=>"improvement", "part"=>"api", "hash"=>"0000111122223333444455556666777788889999", "summ"=>"Add support for custom fields", "note"=>nil, "tags"=>["release_note_needed"], "lead"=>"devin"}
DEBUG: [SCAN] Skipping change due to release_note_needed and empty_notes policy
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-1224", "chid"=>"1_1_0-PROJ-1224", "type"=>"improvement", "part"=>"api", "hash"=>"0000111122223333444455556666777788889999", "summ"=>"Add support for custom fields", "tags"=>["release_note_needed"], "lead"=>"devin"}
DEBUG: [SCAN] Postprocessed = nil
DEBUG: Change dropped after postprocess: {"tick"=>"PROJ-1224", "chid"=>"1_1_0-PROJ-1224", "type"=>"improvement", "part"=>"api", "hash"=>"0000111122223333444455556666777788889999", "summ"=>"Add support for custom fields", "tags"=>["release_note_needed"], "lead"=>"devin"}
DEBUG: Mapped field 'tick': "PROJ-1234"
DEBUG: Mapped field 'type': "improvement"
DEBUG: Mapped field 'part': "installer"
DEBUG: Mapped field 'hash': "0000111122223333444455556666777788889999"
DEBUG: Mapped field 'summ': "Speed up dependency resolution during install"
DEBUG: Mapped field 'note': nil
DEBUG: Mapped field 'tags': ["changelog"]
DEBUG: Mapped field 'lead': "jorge"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-1234", "chid"=>"1_1_0-PROJ-1234", "type"=>"improvement", "part"=>"installer", "hash"=>"0000111122223333444455556666777788889999", "summ"=>"Speed up dependency resolution during install", "note"=>nil, "tags"=>["changelog"], "lead"=>"jorge"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-1234", "chid"=>"1_1_0-PROJ-1234", "type"=>"improvement", "part"=>"installer", "hash"=>"0000111122223333444455556666777788889999", "summ"=>"Speed up dependency resolution during install", "tags"=>["changelog"], "lead"=>"jorge"}
DEBUG: [SCAN] Postprocessed = {"tick"=>"PROJ-1234", "chid"=>"1_1_0-PROJ-1234", "type"=>"improvement", "part"=>"installer", "hash"=>"0000111122223333444455556666777788889999", "summ"=>"Speed up dependency resolution during install", "tags"=>["changelog"], "lead"=>"jorge"}
DEBUG: Initialized Change: PROJ-1234 – Speed up dependency resolution during install
DEBUG: Mapped field 'tick': "PROJ-2323"
DEBUG: Mapped field 'type': "task"
DEBUG: Mapped field 'part': nil
DEBUG: Mapped field 'hash': "abcd1234abcd1234abcd1234abcd1234abcd1234"
DEBUG: Mapped field 'summ': "Refactor internal logging mechanism"
DEBUG: Mapped field 'note': nil
DEBUG: Mapped field 'tags': []
DEBUG: Mapped field 'lead': "harry"
DEBUG: [SCAN] Entering postprocess with scan=false
DEBUG: [SCAN] Data before compact: {"tick"=>"PROJ-2323", "chid"=>"1_1_0-PROJ-2323", "type"=>"task", "part"=>nil, "hash"=>"abcd1234abcd1234abcd1234abcd1234abcd1234", "summ"=>"Refactor internal logging mechanism", "note"=>nil, "tags"=>[], "lead"=>"harry"}
DEBUG: [SCAN] Mapped = {"tick"=>"PROJ-2323", "chid"=>"1_1_0-PROJ-2323", "type"=>"task", "hash"=>"abcd1234abcd1234abcd1234abcd1234abcd1234", "summ"=>"Refactor internal logging mechanism", "tags"=>[], "lead"=>"harry"}
DEBUG: [SCAN] Postprocessed = nil
DEBUG: Change dropped after postprocess: {"tick"=>"PROJ-2323", "chid"=>"1_1_0-PROJ-2323", "type"=>"task", "hash"=>"abcd1234abcd1234abcd1234abcd1234abcd1234", "summ"=>"Refactor internal logging mechanism", "tags"=>[], "lead"=>"harry"}
DEBUG: Transformed 7 changes for release 1.1.0
DEBUG: [DEBUG] Release object: #<ReleaseHx::RHYML::Release:0x00007f606cc719a0 @code="1.1.0", @date=#<Date: 2025-04-12 ((2460778j,0s,0n),+0s,2299161j)>, @hash=nil, @memo=nil, @changes=[#<ReleaseHx::RHYML::Change:0x00007f606c937c80 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-310", @tick="PROJ-310", @hash="a1b2c3d4e5f60718293a4b5c6d7e8f9012345678", @type="improvement", @part="webui", @summ="Add inline editing to dashboard widgets", @head=nil, @note="Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.", @tags=["highlight", "changelog", "release_note_needed"], @lead="julie", @auths=[], @links=[]>, #<ReleaseHx::RHYML::Change:0x00007f606c935b60 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-467", @tick="PROJ-467", @hash="abc123abc123abc123abc123abc123abc123abcd", @type="improvement", @part="api", @summ="Add rate limiting headers to public API", @head=nil, @note="The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.", @tags=["breaking", "changelog", "release_note_needed"], @lead="devin", @auths=[], @links=[]>, #<ReleaseHx::RHYML::Change:0x00007f606c9337c0 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-983", @tick="PROJ-983", @hash="deadbeef0000000000000000000000000000feed", @type="bug", @part="webui", @summ="Fix modal overflow on small screens", @head=nil, @note="Modals now scroll correctly on devices with < 600px screen width.", @tags=["changelog", "release_note_needed"], @lead="sam", @auths=[], @links=[]>, #<ReleaseHx::RHYML::Change:0x00007f606c931880 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-987", @tick="PROJ-987", @hash="facefeed1234567890abcdef1234567890abcdef", @type="bug", @part="api", @summ="Fix JSON serialization issue with null values", @head=nil, @note="Previously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.", @tags=["release_note_needed"], @lead="cory", @auths=[], @links=[]>, #<ReleaseHx::RHYML::Change:0x00007f606c96f798 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-399", @tick="PROJ-399", @hash="ffeeddbbaa99887766554433221100aa00bbccdd", @type="improvement", @part="webui", @summ="Add missing keyboard shortcuts to Help page", @head=nil, @note="The Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal", @tags=["release_note_needed"], @lead="robin", @auths=[], @links=[]>, #<ReleaseHx::RHYML::Change:0x00007f606c96cdb8 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-200", @tick="PROJ-200", @hash="55556666777788889999aaaabbbbccccddddeeee", @type="improvement", @part="api", @summ="Deprecate v1 endpoints for user settings", @head=nil, @note="The following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.", @tags=["deprecation", "release_note_needed"], @lead="amir", @auths=[], @links=[]>, #<ReleaseHx::RHYML::Change:0x00007f606c967638 @release=#<ReleaseHx::RHYML::Release:0x00007f606cc719a0 ...>, @vrsn="1.1.0", @chid="1_1_0-PROJ-1234", @tick="PROJ-1234", @hash="0000111122223333444455556666777788889999", @type="improvement", @part="installer", @summ="Speed up dependency resolution during install", @head=nil, @note=nil, @tags=["changelog"], @lead="jorge", @auths=[], @links=[]>]>
[DEBUG] release is a ReleaseHx::RHYML::Release
</file>

<file path="test.adoc">
:icons: font
= Release History -- 1.1.0 - 2025-04-23


== Changelog




=== Web UI

[[entry-1_1_0-PROJ-310]]
Added inline editing to dashboard widgets

icon:wrench[][.type]*Improvement*
  icon:star[]
[.tag-label]*highlight*
[<<note-1_1_0-PROJ-310,NOTE>>]


[[entry-1_1_0-PROJ-983]]
Fixed modal overflow on small screens

icon:bug[][.type]*Bug fix*
[<<note-1_1_0-PROJ-983,NOTE>>]


[[entry-1_1_0-PROJ-399]]
Added missing keyboard shortcuts to Help page

icon:wrench[][.type]*Improvement*
[<<note-1_1_0-PROJ-399,NOTE>>]


=== API

[[entry-1_1_0-PROJ-467]]
Added rate limiting headers to public API

icon:wrench[][.type]*Improvement*
  icon:exclamation-triangle[]
[.tag-label]*Breaking*
[<<note-1_1_0-PROJ-467,NOTE>>]


[[entry-1_1_0-PROJ-987]]
Fixed JSON serialization issue with null values

icon:bug[][.type]*Bug fix*
[<<note-1_1_0-PROJ-987,NOTE>>]


[[entry-1_1_0-PROJ-200]]
Deprecated v1 endpoints for user settings

icon:wrench[][.type]*Improvement*
  icon:exclamation-triangle[]
[.tag-label]*Deprecated*
[<<note-1_1_0-PROJ-200,NOTE>>]


=== Installer

[[entry-1_1_0-PROJ-1234]]
Speed up dependency resolution during install

icon:wrench[][.type]*Improvement*


== Release Notes




=== Web UI

[[note-1_1_0-PROJ-310]]
[.release-note]
[cols="5,2"]
|===
2+a| *Added inline editing to dashboard widgets*
a| Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.

This significantly streamlines common tasks.

a| 
icon:wrench[][.type]*Improvement*
  icon:star[]
[.tag-label]*highlight*
|===


[[note-1_1_0-PROJ-983]]
[.release-note]
[cols="5,2"]
|===
2+a| *Fixed modal overflow on small screens*
a| Modals now scroll correctly on devices with < 600px screen width.

a| 
icon:bug[][.type]*Bug fix*
|===


[[note-1_1_0-PROJ-399]]
[.release-note]
[cols="5,2"]
|===
2+a| *Added missing keyboard shortcuts to Help page*
a| The Help page now includes a full list of supported keyboard shortcuts, including:

* `/` to focus search
* `?` to show help modal

a| 
icon:wrench[][.type]*Improvement*
|===


=== API

[[note-1_1_0-PROJ-467]]
[.release-note]
[cols="5,2"]
|===
2+a| *Added rate limiting headers to public API*
a| The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.

NOTE: The default rate limit remains unchanged.

a| 
icon:wrench[][.type]*Improvement*
  icon:exclamation-triangle[]
[.tag-label]*Breaking*
|===


[[note-1_1_0-PROJ-987]]
[.release-note]
[cols="5,2"]
|===
2+a| *Fixed JSON serialization issue with null values*
a| Previously, null values were omitted from JSON payloads.
This has been corrected.

*Impact:* Clients expecting explicit nulls will now receive them as specified.

a| 
icon:bug[][.type]*Bug fix*
|===


[[note-1_1_0-PROJ-200]]
[.release-note]
[cols="5,2"]
|===
2+a| *Deprecated v1 endpoints for user settings*
a| The following v1 endpoints are now deprecated:

* `GET /api/v1/settings/user`
* `PUT /api/v1/settings/user`

Use `/api/v2/settings/profile` instead.

a| 
icon:wrench[][.type]*Improvement*
  icon:exclamation-triangle[]
[.tag-label]*Deprecated*
|===
</file>

<file path="yaml">
code: "1.1.0"
date: 2025-04-07
memo: |
  
changes:

  - chid: 
    type: improvement
    part: webuisumm: Add inline editing to dashboard widgets
    note: |
      Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.

This significantly streamlines common tasks.
    tags:
      - "featured"
      - "changelog"
      - "release_note_needed"
    lead: julie
  - chid: 
    type: improvement
    part: apisumm: Add rate limiting headers to public API
    note: |
      The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.

> **Note:** The default rate limit remains unchanged.
    tags:
      - "breaking"
      - "changelog"
      - "release_note_needed"
    lead: devin
  - chid: 
    type: bug
    part: webuisumm: Fix modal overflow on small screens
    note: |
      Modals now scroll correctly on devices with < 600px screen width.
    tags:
      - "changelog"
      - "release_note_needed"
    lead: sam
  - chid: 
    type: improvement
    part: apisumm: Deprecate v1 endpoints for user settings
    note: |
      The following v1 endpoints are now deprecated:

- `GET /api/v1/settings/user`
- `PUT /api/v1/settings/user`

Use `/api/v2/settings/profile` instead.
    tags:
      - "deprecation"
      - "release_note_needed"
    lead: amir
</file>

<file path="_mappings/customfield-note/jira.yaml">
_config:
  path_lang: jmespath
  tplt_lang: liquid
  desc: JIRA API to RHYML (customfield-based notes)
  note: Requires config.sources.note_custom_field

changes_array_path: issues

tick:
  path: key

type:
  path: fields.issueType.name
  tplt: '{{ path | downcase }}'

part:
  path: fields.components[0].name

hash:
  path: fields.development.commits[0].sha

summ:
  path: fields.summary

head:
  path: fields.summary

note:
  path: fields.{{ config.sources.note_custom_field }}

part:
  path: fields.components[0].name

tags:
  path: fields.labels

lead:
  path: fields.assignee.displayName
</file>

<file path="_mappings/description-note/jira.yaml">
_config:
  path_lang: jmespath
  tplt_lang: liquid
  desc: JIRA API to RHYML (customfield-based notes)
  note: Requires config.sources.note_custom_field

changes_array_path: issues

tick:
  path: key

type:
  path: fields.issueType.name
  tplt: '{{ path | downcase }}'

hash:
  path: fields.development.commits[0].sha

part:
  path: fields.components[0].name

summ:
  path: fields.summary

note:
  path: "fields.description"

part:
  path: fields.components[0].name

tags:
  path: fields.labels

lead:
  path: fields.assignee.displayName
</file>

<file path="issues/jira-customfield-note-1.1.0.json">
{
  "issues": [
    {
      "id": "1001",
      "key": "PROJ-310",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add inline editing to dashboard widgets",
        "description": "Implements interactive dashboard widgets using JavaScript hooks and async save.",
        "customfield_10010": "Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.",
        "components": [ { "name": "webui" } ],
        "labels": ["highlighted", "changelog", "release_note_needed"],
        "assignee": { "displayName": "julie" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "a1b2c3d4e5f60718293a4b5c6d7e8f9012345678"
            }
          ]
        }
      }
    },
    {
      "id": "1002",
      "key": "PROJ-467",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add rate limiting headers to public API",
        "description": "Implements middleware to attach X-RateLimit headers.",
        "customfield_10010": "The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.",
        "components": [ { "name": "api" } ],
        "labels": ["breaking", "changelog", "release_note_needed"],
        "assignee": { "displayName": "devin" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "abc123abc123abc123abc123abc123abc123abcd"
            }
          ]
        }
      }
    },
    {
      "id": "1003",
      "key": "PROJ-983",
      "fields": {
        "issueType": {
          "name": "Bug"
        },
        "summary": "Fix modal overflow on small screens",
        "description": "Bugfix: modal height exceeded viewport height.",
        "customfield_10010": "Modals now scroll correctly on devices with < 600px screen width.",
        "components": [ { "name": "webui" } ],
        "labels": ["changelog", "release_note_needed"],
        "assignee": { "displayName": "sam" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "deadbeef0000000000000000000000000000feed"
            }
          ]
        }
      }
    },
    {
      "id": "1004",
      "key": "PROJ-987",
      "fields": {
        "issueType": {
          "name": "Bug"
        },
        "summary": "Fix JSON serialization issue with null values",
        "description": "Previously, null values were omitted from JSON payloads.",
        "customfield_10010": "Previously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.",
        "components": [ { "name": "api" } ],
        "labels": ["release_note_needed"],
        "assignee": { "displayName": "cory" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "facefeed1234567890abcdef1234567890abcdef"
            }
          ]
        }
      }
    },
    {
      "id": "1005",
      "key": "PROJ-399",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add missing keyboard shortcuts to Help page",
        "description": "User doc update requested by QA lead.",
        "customfield_10010": "The Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal",
        "components": [ { "name": "webui" } ],
        "labels": ["release_note_needed"],
        "assignee": { "displayName": "robin" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "ffeeddbbaa99887766554433221100aa00bbccdd"
            }
          ]
        }
      }
    },
    {
      "id": "1006",
      "key": "PROJ-200",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Deprecate v1 endpoints for user settings",
        "description": "Phase-out plan for v1 settings endpoints.",
        "customfield_10010": "The following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.",
        "components": [ { "name": "api" } ],
        "labels": ["deprecation", "release_note_needed"],
        "assignee": { "displayName": "amir" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "55556666777788889999aaaabbbbccccddddeeee"
            }
          ]
        }
      }
    },
    {
      "id": "1007",
      "key": "PROJ-1224",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add support for custom fields",
        "description": "Adds support for custom fields in the API.",
        "components": [ { "name": "api" } ],
        "labels": ["release_note_needed"],
        "assignee": { "displayName": "devin" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "0000111122223333444455556666777788889999"
            }
          ]
        }
      }
    },
    {
      "id": "1009",
      "key": "PROJ-1234",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Speed up dependency resolution during install",
        "description": "Performance improvement during CLI install.\n\nThis issue is intended for changelog only.",
        "components": [ { "name": "installer" } ],
        "labels": ["changelog"],
        "assignee": { "displayName": "jorge" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "0000111122223333444455556666777788889999"
            }
          ]
        }
      }
    },
    {
      "id": "1010",
      "key": "PROJ-2323",
      "fields": {
        "issueType": { "name": "Task" },
        "summary": "Refactor internal logging mechanism",
        "description": "This task involves no user-facing change.",
        "components": [],
        "labels": [],
        "assignee": { "displayName": "harry" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "abcd1234abcd1234abcd1234abcd1234abcd1234"
            }
          ]
        }
      }
    }    
  ]
}
</file>

<file path="issues/jira-description-note-1.1.0.json">
{
  "issues": [
    {
      "id": "1001",
      "key": "PROJ-310",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add inline editing to dashboard widgets",
        "description": "Implements interactive dashboard widgets using JavaScript hooks and async save.\n\n## Draft Release Note\n\nIntroduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.",
        "components": [ { "name": "webui" } ],
        "labels": ["highlighted", "changelog", "release_note_needed"],
        "assignee": { "displayName": "julie" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "a1b2c3d4e5f60718293a4b5c6d7e8f9012345678"
            }
          ]
        }
      }
    },
    {
      "id": "1002",
      "key": "PROJ-467",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add rate limiting headers to public API",
        "description": "Implements middleware to attach X-RateLimit headers.\n\n## Draft Release Note\n\nThe API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.",
        "components": [ { "name": "api" } ],
        "labels": ["breaking", "changelog", "release_note_needed"],
        "assignee": { "displayName": "devin" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "abc123abc123abc123abc123abc123abc123abcd"
            }
          ]
        }
      }
    },
    {
      "id": "1003",
      "key": "PROJ-983",
      "fields": {
        "issueType": {
          "name": "Bug"
        },
        "summary": "Fix modal overflow on small screens",
        "description": "Bugfix: modal height exceeded viewport height.\n\n## Draft Release Note\n\nModals now scroll correctly on devices with < 600px screen width.",
        "components": [ { "name": "webui" } ],
        "labels": ["changelog", "release_note_needed"],
        "assignee": { "displayName": "sam" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "deadbeef0000000000000000000000000000feed"
            }
          ]
        }
      }
    },
    {
      "id": "1004",
      "key": "PROJ-987",
      "fields": {
        "issueType": {
          "name": "Bug"
        },
        "summary": "Fix JSON serialization issue with null values",
        "description": "Previously, null values were omitted from JSON payloads.\n\n## Draft Release Note\n\nPreviously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.",
        "components": [ { "name": "api" } ],
        "labels": ["release_note_needed"],
        "assignee": { "displayName": "cory" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "facefeed1234567890abcdef1234567890abcdef"
            }
          ]
        }
      }
    },
    {
      "id": "1005",
      "key": "PROJ-399",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Add missing keyboard shortcuts to Help page",
        "description": "User doc update requested by QA lead.\n\n## Draft Release Note\n\nThe Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal",
        "components": [ { "name": "webui" } ],
        "labels": ["release_note_needed"],
        "assignee": { "displayName": "robin" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "ffeeddbbaa99887766554433221100aa00bbccdd"
            }
          ]
        }
      }
    },
    {
      "id": "1006",
      "key": "PROJ-200",
      "fields": {
        "issueType": {
          "name": "Documentation"
        },
        "summary": "Deprecate v1 endpoints for user settings",
        "description": "Phase-out plan for v1 settings endpoints.\n\n## Draft Release Note\n\nThe following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.",
        "components": [ { "name": "api" } ],
        "labels": ["deprecation", "release_note_needed"],
        "assignee": { "displayName": "amir" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "55556666777788889999aaaabbbbccccddddeeee"
            }
          ]
        }
      }
    },
    {
      "id": "1007",
      "key": "PROJ-1234",
      "fields": {
        "issueType": {
          "name": "Improvement"
        },
        "summary": "Speed up dependency resolution during install",
        "description": "Performance improvement during CLI install.\n\nThis issue is intended for changelog only.",
        "components": [ { "name": "installer" } ],
        "labels": ["changelog"],
        "assignee": { "displayName": "jorge" },
        "development": {
          "commits": [
            {
              "type": "commit",
              "sha": "0000111122223333444455556666777788889999"
            }
          ]
        }
      }
    }
  ]
}
</file>

<file path="src/acmedoc-1.1.0.yml">
code: '1.1.0'
date: 2024-11-01
hash: 'd34db33fabc1234567890feedface000000abcd1'
memo: |
  This release introduces several important features and bug fixes, along with one deprecation.
changes:
  # NOTE:
  # This file is a source for generating and testing demo payloads for using with ReleaseHx.
  # This file is proper RHYML; we use it to reverse engineer API payloads from JIRA, GH Issues, etc.
  # All change records except IMP-999 should have a release_note_needed indicator in their
  #  JSON payload, depending on whether the IMS-side setup is to use labels or checkboxes in the 
  #  body/description field.
  # If release notes are sourced in the description/body, they should contain developer-oriented 
  #  content from the original issue text, followed by demarcation like:
  #  '\n\n## Draft Release Note\n\n' followed by the content of note
  - chid: FEA-101
    tick: PROJ-310
    hash: 'a1b2c3d4e5f60718293a4b5c6d7e8f9012345678'
    type: feature
    part: webui
    summ: 'Add inline editing to dashboard widgets'
    head: 'Inline Editing for Widgets'
    tags: [highlighted, changelog]
    note: |
      Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.

      This significantly streamlines common tasks.
    lead: julie
    auths:
      - user: julie
        memo: frontend lead
      - user: robin
        memo: UX review

  - chid: FEA-102
    tick: PROJ-467
    hash: 'abc123abc123abc123abc123abc123abc123abcd'
    type: feature
    part: api
    summ: 'Add rate limiting headers to public API'
    head: 'API Rate Limiting Headers'
    tags: [breaking, changelog]
    note: |
      The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.

      > **Note:** The default rate limit remains unchanged.
    lead: devin

  - chid: BUG-303
    tick: PROJ-983
    hash: 'deadbeef0000000000000000000000000000feed'
    type: bug
    part: webui
    summ: 'Fix modal overflow on small screens'
    tags: [changelog]
    note: |
      Modals now scroll correctly on devices with < 600px screen width.
    lead: sam
    auths:
      - user: sam
        memo: implementation
      - user: tracy
        memo: QA

  - chid: BUG-304
    tick: PROJ-987
    hash: 'facefeed1234567890abcdef1234567890abcdef'
    type: bug
    part: api
    summ: 'Fix JSON serialization issue with null values'
    tags: []
    note: |
      Previously, null values were omitted from JSON payloads. This has been corrected.

      **Impact:** Clients expecting explicit nulls will now receive them as specified.
    lead: cory

  - chid: DOC-205
    tick: PROJ-399
    hash: 'ffeeddbbaa99887766554433221100aa00bbccdd'
    type: documentation
    part: webui
    summ: 'Add missing keyboard shortcuts to Help page'
    tags: []
    note: |
      The Help page now includes a full list of supported keyboard shortcuts, including:

      - `/` to focus search
      - `?` to show help modal
    lead: robin

  - chid: DOC-410
    tick: PROJ-200
    hash: '55556666777788889999aaaabbbbccccddddeeee'
    type: documentation
    part: api
    summ: 'Deprecate v1 endpoints for user settings'
    tags: [deprecation]
    note: |
      The following v1 endpoints are now deprecated:

      - `GET /api/v1/settings/user`
      - `PUT /api/v1/settings/user`

      Use `/api/v2/settings/profile` instead.
    lead: amir

  # only appears in changelog, not release notes
  - chid: IMP-999
    tick: PROJ-1234
    hash: '0000111122223333444455556666777788889999'
    type: improvement
    part: installer
    summ: 'Speed up dependency resolution during install'
    tags: [] # this will have had a changelog label in IMS
    # its presencce in this file designates it as belonging in the changelog
    lead: jorge
</file>

<file path=".gitignore">
.ruby-version
drafts
publish
</file>

<file path="Gemfile">
source "https://rubygems.org"

# Rails framework
gem "releasehx", path: "../releasehx"
</file>

<file path="README.adoc">
= ReleaseHx Demo Project

This repo contains demo and test files for the ReleaseHx gem.
</file>

</files>
