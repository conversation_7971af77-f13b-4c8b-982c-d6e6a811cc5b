module SchemaGraphy

  # This part of the API is basically an interface between SchemaGraphy's definition language and the native code supporting a given API instance.
  # Configure the API using app-wide or project-scoped config files or overwrite these constants in your own code to customize the API's behavior.

  # Finalize and augment data-related API config settings

  all_syntaxes = {
    json: {
      validator: method(:valid_json?),
      parser: JSON.method(:parse),
    },
    yaml: {
      validator: method(:valid_yaml?),
      parser: YAML.method(:safe_load),
    },
    xml: {
      validator: method(:valid_xml?),
      parser: Crack::XML.method(:parse),
    },
    csv: {
      validator: method(:valid_csv?),
      parser: CSV.method(:parse),
    },
    toml: {
      validator: method(:valid_toml?),
      parser: TOML.method(:parse),
    },
    ini: {
      validator: method(:valid_ini?),
      parser: lambda { |string| IniFile.new(:content => string) },
    },
    asciidoc: {
      validator: method(:valid_asciidoc?),
      parser: Asciidoctor.method(:load),
    },
    markdown: {
      validator: method(:valid_markdown?),
      parser: Kramdown.method(:parse),
    },
    txt: {
      validator: method(:valid_txt?),
      parser: '',
    },
    html: {
      validator: method(:valid_html?),
      parser: '',
    }
  }

  if API_SETTINGS.api.syntaxes
    select_syntaxes = data_syntaxes.select { |syntax, _| syntaxes.include? syntax }
  end

  SUBJECT_SYNTAXES = select_syntaxes ? select_syntaxes : all_syntaxes

  all_domains = {
    data: {
      syntaxes: [:json, :yaml, :xml, :csv, :toml, :ini],
    } ,
    text: {
      syntaxes: [:asciidoc, :markdown, :txt, :html],
    }
  }

  if API_SETTINGS.api.syntaxes
    select_domains = all_subject_domains.select { |domain, _| domains.include? domain }
  end

  SUBJECT_DOMAINS = select_domains ? select_domains : all_domains

  DATA_SYNTAXES = SUBJECT_SYNTAXES.select { |syntax, _| SUBJECT_DOMAINS[:data][:syntaxes].include? syntax }

  TEXT_SYNTAXES = SUBJECT_SYNTAXES.select { |syntax, _| SUBJECT_DOMAINS[:text][:syntaxes].include? syntax }

  syntax_domains = {}
  SUBJECT_DOMAINS.each do |domain, info|
    info[:syntaxes].each do |syntax|
      syntax_domains[syntax] = domain
    end
  end
  
  SYNTAX_DOMAINS = syntax_domains

  SUPPPORTED_SDLS = [:schemagraphy, :jsonschema, :graphql]

  SCHEMA_SYNTAXES = [:yaml, :json]

end