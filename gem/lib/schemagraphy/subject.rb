require 'open-uri'

module SchemaGraphy

  class SubjectMatter
    attr_accessor :schema

    def initialize subject, syntax=String
      if syntax
        @domain = SYNTAX_DOMAINS[format.to_sym]
      else
        if subject.is_a?(String)
          data_syntax = subject.data_syntax
          unless data_syntax == 'unknown' 
            @domain = SYNTAX_DOMAINS[data_syntax.to_sym]
            @syntax = data_syntax
          else
            raise DataSyntaxTypeError, "subject", data_syntax, DATA_SYNTAXES, "Text document subjects require declared syntax argument."
          end
        else
          @domain = 'data'
        end
      end
      if @domain == 'data'
        if subject.is_a?(String)
          @schema = schema_from_string(subject)
          @payload = payload_from_string(subject)
        else
          @schema = schema_from_object(subject)
        end
        if @schema
          payload = document['$payload']
        else
          payload = document
        end
      end
    end

    def syntax
      @syntax
    end

    def domain
      @domain
    end

    def schema
      @schema
    end

    def payload
      @payload
    end

    def is_data?
      false
      true if self.domain == 'data'
    end

    def is_text?
      false
      true if self.domain == 'text'
    end

    def valid? schema=nil
      schema = schema ? schema : self.schema
      Validator.valid?(self, schema)
    end

    def raw
      @subject
    end

    def parse
      Parser.parse(self.raw, self.schema)
    end

    private

    # @return [Hash] includes payload and any schema found in data object
    def inspect_data_object object
      raise ArgumentError, "Schema must be a data object" unless object.is_a?(Hash) or object.is_a?(Array)
      if object.is_a?(Array)
        if object.length == 2 # imported from YAML file with --- seperator or JSON file with 2 objects
          schema = object[0]['$schema'] ? object[0]['$schema'] : object[1]['$schema']
          payload = object[0]['$schema'] ? object[1] : object[0]
        end
      elsif object.key?('$schema')
        schema = object['$schema']
        payload = object['$payload']
      end
      # Create new Schema instance
      if schema.key?('$ref')
        schema = get_remote_schema(schema['$ref'], schema, true)
      else 
        schema = Schema.new(schema)
      end
      schema
    end

    # return [Hash] includes payload and any schema found in data string
    def inspect_data_string string
      # check if the first line of the object is # or // followed by $schema:
      schema_comment = parse_schema_comments(string)
      # parse schema with Liquid
      if schema_comment.contains_liquid
        schema = Liquid::Template.parse(schema_comment).render(vars)
      end
      schema
    end

    def inspect_document

    end

    def payload_from_string subject
      
    end


    def parse_schema_comments string
      schema_lines = []
      in_schema_comment = false
    
      string.each_line do |line|
        stripped_line = line.strip
    
        if in_schema_comment
          if stripped_line.empty?
            # blank line, end of schema comment
            in_schema_comment = false
          else
            # remove the comment character but retain whitespace
            schema_lines << line.sub(/\A[\/#]\s?/, '')
          end
        else
          if stripped_line =~ /\A[\/#]\s?\$?schema:/
            # start of schema comment
            in_schema_comment = true
            schema_lines << line.sub(/\A[\/#]\s?/, '')
          end
        end
      end
    
      schema_lines.join if schema_lines.any?
    end

    def valid_yaml_with_schema? string
      begin
        documents = YAML.load_stream(string)
        return false unless objects.length == 2
    
        # Check that the first document contains a '$schema:' key with a Hash value
        schema = documents.first['$schema:']
        return false unless schema.is_a?(Hash)
    
        # If all checks pass, it's valid
        true
      rescue Psych::SyntaxError
        # If the YAML can't be parsed, it's not valid
        false
      end
    end

  end

  class DataObject < SubjectMatter
    def initialize subject=Subject
      @subject = subject
      @domain = 'data'
    end
  end

  class TextDocument < SubjectMatter
    def initialize subject=Subject
      @subject = subject
      @domain = 'text'
    end
  end

end