module SchemaGraphy

  

  class SubjectParser

    def parse subject, schema=Schema
      if subject.is_a?(String)
        TextParser.parse(subject, schema)
      else
        DataParser.parse(subject, schema)
      end
    end
      
  end

  class DataParser < SubjectParser

    def parse object, schema=Schema
      
    end

  end


  class TextParser < SubjectParser

    def parse document, schema=Schema
      
    end

    # Reads the YAML content from a file and navigates to a given property path.
    # Extracts the raw YAML string for that path.
    def extract_yaml_segment(file_path, property_path)
      yaml_content = File.read(file_path)
      lines = yaml_content.split("\n")
      target_lines = []
      indent_level = nil

      property_path_segments = property_path.split('.')
      current_segment = 0
      capturing = false

      lines.each do |line|
        current_indent = line.match(/^(\s*)/)[1].length

        if line.strip.start_with?("#{property_path_segments[current_segment]}:")
          if current_segment == property_path_segments.length - 1
            # Start capturing if this is the target property
            capturing = true
            indent_level = current_indent
          else
            # Move to the next segment
            current_segment += 1
          end
        elsif capturing && current_indent <= indent_level
          # Stop capturing if the current line's indentation returns to the base level or higher
          break
        end

        target_lines << line if capturing
      end

      target_lines.join("\n")
    end

  end

end
