require 'safe_yaml'
require 'date'
require 'uri'
require 'semantic'
require 'semantic/core_ext'
require 'json'

module SchemaGraphy

  # module-wide constants for regexp patterns
  regexps = {}
  regexps['email'] = /^[\w+\-.]+@[a-z\d\-.]+\.[a-z]+$/i
  regexps['html5class'] = /^-?[_a-zA-Z]+[_a-zA-Z0-9-]*$/i
  regexps['html5id'] = /^[_a-zA-Z]+[_a-zA-Z0-9-]*$/i
  regexps['ipv4'] = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/
  REGEXPS = regexps

  class TypesMeta

    def initialize types=[]
      type_defs = SDL_SPECS.data_types
      types = []
      type_defs.each do |type|
        thistype = type.dup
        thistype['parent'] = type['subs'] ? "self" : thistype['type']
        thistype.delete('subs')
        types << thistype
        if type['subs']
          type['subs'].each do |sub|
            sub['kind'] = type['kind']
            types << sub
          end
        end
      end
      @types = types
    end

    def types
      @types
    end

    def enumerable
      @types.select { |t| t['kind'] == 'enumerable' }
    end

    def scalar
      @types.select { |t| t['kind'] == 'scalar' }
    end

  end

  class ObjectTypeMeta
    
    def initialize datum=Object, kind="object"
      types =
        case kind
        when 'scalar'
          TypesMeta.new().scalar
        when 'enumerable'
          TypesMeta.new().enumerable
        else
          TypesMeta.new().types
        end
      @native = datum.class.name
      types.reverse.each do |type_meta|
        if eval(type_meta['rules']['ruby']['detect'].to_s)
          @type = type_meta ? type_meta['type'] : 'Unknown'
          @parent = type_meta ? type_meta['parent'] : 'Unknown'
          @kind = type_meta ? type_meta['kind'] : 'Unknown'
          break
        end
      end
    end

    def type
      @type
    end

    def parent
      @parent
    end

    def kind
      @kind
    end

    def native
      @native
    end

  end

  # @param [String] types Comma-separated list of types to print 
  # @return [nil]
  def self.types_explainer types="all"
    types_list = TypesMeta.new().types
    unless types == "all"
      types = types.split(",")
      print_types = types_list.select { |t| types.include? t.type }
    else
      print_types = types_list
    end
    print_types.each do |type_meta|
      puts "#{type_meta['type']}::\n  #{type_meta['desc']}"
      puts "#{type_meta['long']}" if type_meta['long'] and types != "all"
      puts
    end
  end

  # Monkey patch Object class to enable type and kind evaluations universally
  class Object

    def schemagraphy_domain
      if self.is_a? String && self.match(/\n/)
        'text'
      else
        'data'
      end 
    end

    def schemagraphy_meta
      unless self.is_a? Enumerable
        DataTypes::ObjectTypeMeta.new(self, 'scalar')
      else
        DataTypes::ObjectTypeMeta.new(self, 'enumerable')
      end  
    end
  
    def schemagraphy_type
      self.schemagraphy_meta.type
    end
  end

  # def self.typer(datum)
  #   unless datum.is_a? Enumerable
  #     ObjectTypeMeta.new(datum, 'scalar')
  #   else
  #     ObjectTypeMeta.new(datum, 'enumerable')
  #   end        
  # end

end