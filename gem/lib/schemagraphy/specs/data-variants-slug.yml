# referenced in data-types.yml
- type: KeyNameSGYML
  desc: A keyname supported by SGYML YAML documents.
  rules:
    ruby:
      class: KeyNameSGYMLType
      detect: |
        datum.is_a? String and datum =~ /^[a-z\$_][a-zA-Z0-9_]{1,24}$/
      insist: |
        datum.is_a? String and datum =~ /^[a-z\$\_][a-zA-Z0-9]{1,24}$/
- type: KebabCase
  desc: A Slug formatted as a kebab-case String.
  rules:
    ruby:
      class: KebabCaseSlugType
      detect: |
        datum.is_a? String and datum =~ /^[a-z0-9](?:[a-zA-Z0-9\-]{0,23}[a-zA-Z0-9]?)?$/
      insist: |
        datum.is_a? String and datum =~ /^[a-zA-Z0-9\-\_]+$/
      convert: |
        datum.to_s.downcase.gsub(/[\s\-\_]/, '-')
- type: SnakeCase
  desc: A Slug formatted as a snake_case String.
  rules:
    ruby:
      class: SnakeCaseSlugType
      detect: |
        datum.is_a? String and datum =~ /^[a-zA-Z0-9](?:[a-zA-Z0-9_]{0,23}[a-zA-Z0-9])?$/
      insist: |
        datum.is_a? String and datum =~ /^[a-zA-Z0-9](?:[a-zA-Z0-9_]{0,23}[a-zA-Z0-9])?$/
      convert: |
        datum.to_s.downcase.gsub(/[\s\-\_]/, '_')
- type: CamelCase
  desc: A Slug formatted as a camelCase String.
  rules:
    ruby:
      class: CamelCaseSlugType
      detect: |
        datum.is_a? String and datum =~ /^[a-z][a-zA-Z0-9]{2,24}$/
      insist: |
        datum.is_a? String and datum =~ /^[a-z][a-zA-Z0-9]{2,24}$/
      convert: |
        datum.to_s.downcase.gsub(/[\s\-\_]/, '_').camelize
- type: PascalCase
  desc: A Slug formatted as a PascalCase String.
  rules:
    ruby:
      class: PascalCaseSlugType
      detect: |
        datum.is_a? String and datum =~ /^[A-Z][a-zA-Z0-9]{2,24}$/
      insist: |
        datum.is_a? String and datum =~ /^[A-Z][a-zA-Z0-9]{2,24}$/
      convert: |
        datum.to_s.downcase.gsub(/[\s\-\_]/, '_').camelize(:upper)
- type: LowerCase
  desc: A Slug formatted as a lowercase alphanumeric String
  rules:
    ruby:
      class: LowerCaseSlugType
      detect: |
        datum.is_a? String and datum =~ /^[a-z0-9]{1,25}$/
      insist: |
        datum.is_a? String and datum =~ /^[a-z0-9]{1,25}$/
      convert: |
        datum.to_s.downcase
- type: UpperCase
  desc: A Slug formatted as an uppercase alphanumeric String.
  rules:
    ruby:
      class: UpperCaseSlugType
      detect: |
        datum.is_a? String and datum =~ /^[A-Z0-9]{1,25}$/
      insist: |
        datum.is_a? String and datum =~ /^[A-Z0-9]{1,25}$/
      convert: |
        datum.to_s.upcase
- type: LowerAlpha
  desc: A Slug formatted as a lowercase alphabetic String.
  rules:
    ruby:
      class: LowerAlphaSlugType
      detect: |
        datum.is_a? String and datum =~ /^[a-z]{1,25}$/
      insist: |
        datum.is_a? String and datum =~ /^[a-z]{1,25}$/
      convert: |
        datum.to_s.downcase
- type: UpperAlpha
  desc: A Slug formatted as an uppercase alphabetic String.
  rules:
    ruby:
      class: UpperAlphaSlugType
      detect: |
        datum.is_a? String and datum =~ /^[A-Z]{1,25}$/
      insist: |
        datum.is_a? String and datum =~ /^[A-Z]{1,25}$/
      convert: |
        datum.to_s.upcase
- type: HashTag
  desc: A hashtag, typically used in social media, consisting of a `#` followed by alphanumeric characters and symbols.
  rules:
    ruby:
      class: HashtagType
      detect: |
        datum.class == String and datum =~ /^#[a-zA-Z0-9]+$/"
      insist: |
        datum.class == String and datum =~ /^#[a-zA-Z0-9]+$/"