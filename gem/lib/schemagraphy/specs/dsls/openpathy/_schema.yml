$schema:
  anchors:
    meta_property: &meta_property
      type: Map
      properties:
        summ:
          type: String
        desc:
          type: String
        reqd:
          type: Boolean
        alts:
          type: Array
          items:
            type: Map
        regx:
          type: RegExp
    directory: &directory_object
      type: Map
      properties:
        _meta:
          arb: true
        key:
        __meta: # the actual _meta property of a directory definition Map
          
  properties:
    <root_path>:
      key:
        regexp: /[a-z-_]+\//
      properties:
        _meta:
          arb: true
        
    
        
      
    # _meta: # properties of this directory
    #   summ: Root directory for the project.
    #   desc: |
    #     This is the root directory for the project. It contains the configuration
    #     files and directories for the project.
    # # after _meta, the rest of the keys are the names of files and directories
    # src/: # source code directory
    #   _meta:
    #     summ: Source code directory.
    #     reqd: true # path is required
    #     desc: |
    #       This directory contains the source code for the project. It may contain
    #       subdirectories for different parts of the project.
    #   lib/: # application library files
    #     _meta:
    #       summ: Application library files.
    # _docs/: # documentation directory
    #   _meta:
    #     summ: Documentation directory.
    #     reqd: true
    #     desc: |
    #       This directory contains the documentation for the project.
    #   content/: # topics and other content
    #     topics/: # topics directory
    #       <topic>.adoc: # topic file
    #         regx: /[a-z][a-z-_]+\.adoc/
    #         desc: As many AsciiDoc files as needed.
    #     common/: # common content directory
    #       snippets/: # code samples and other literals
    #         _meta:
    #           summ: Code samples and other literals.
    #       partials/: # reusable content directory
    #         _meta:
    #           summ: Collection of reusable content.
    #         <partial>.adoc: # partial file
    #           regx: /[a-z][a-z-_]+\.adoc/
    #         index.adoc: # index file
    #           desc: AsciiDOc index file.
    #   data/: # data directory
    #     manifest.yml: # manifest file
    #       type: config
    #       with: yaml
    #       summ: Documentation portals manifest.
    # Gemfile: # Gemfile
    #   reqd: true
    #   type: config
    #   with: ruby
    #   summ: Ruby gem dependency listings.
    #   desc: |
    #     This file lists the Ruby gems required for the project.
    #     Sometimes it also lists specified versions of the gems.
    # Gemfile.lock: # Gemfile.lock
    #   reqd: true
    #   type: config
    #   with: ruby
    #   summ: Fixed Ruby dependency versions.
    # .gitignore: # .gitignore
    #   desc: |
    #     This file lists the files and directories that should be ignored by Git.
    #     It is used to prevent certain files from