- type: LiteralBlock
  desc: A multi-line String with line-breaks retained.
  rules:
    ruby:
      class: LiteralBlockType
      insist: |
        datum.class == String
# ALL of the following are specific formats of LiteralBlock
- type: JSON
  desc: A valid JSON data string for embedding complex data in a single field.
  rules:
    ruby:
      class: JsonType
      detect: |
        datum.class == String and datum.match?(/\A[\[\{]\s*"\w[\w\-]*"\s*:.*[\}\]]\s*\z/m) and datum.valid_json?
      insist: |
        datum.class == String and datum.valid_json?
- type: YAML
  desc: A valid YAML data string for embedding complex data in a single field.
  rules:
    ruby:
      class: YamlType
      detect: |
        datum.class == String and datum.match?(/\A\s*\w[\w\-]*\s*:\s*.*\s*\z/m) and datum.valid_yaml?
      insist: |
        datum.class == String and datum.valid_yaml?
- type: XML
  desc: A valid XML data string for embedding complex data in a single field.
  rules:
    ruby:
      class: XmlType
      detect: |
        datum.class == String and datum.match?(/\A<\?xml.*\?>\s*<\w[\w\-]*>.*<\/\w[\w\-]*>\s*\z/m) and datum.valid_xml?
      insist: |
        datum.class == String and datum.valid_xml?
- type: AsciiDoc
  desc: |
    Valid AsciiDoc syntax for formatting textual content.
    Does not support headings, macros, or other advanced features.
  rules:
    ruby:
      class: AsciiDocType
      detect: |
        datum.class == String and datum.match?(/\A= \w[\w\-]*\s*\n.*\n\z/m) and datum.valid_asciidoc?
      insist: |
        datum.class == String and datum.valid_asciidoc?
- type: Markdown
  desc: A valid Markdown data string for embedding marked-up text in a single field.
  rules:
    ruby:
      class: MarkdownType
      detect: |
        datum.class == String and datum.match?(/\A#\s*\w[\w\-]*\s*\n.*\n\z/m) and datum.valid_markdown?
      insist: |
        datum.class == String and datum.valid_markdown?
- type: HTML
  desc: A valid HTML data string for embedding tagged text in a single field.
  rules:
    ruby:
      class: HtmlType
      detect: |
        datum.class == String and datum.match?(/\A<!DOCTYPE html>.*<\/html>\s*\z/m) and datum.valid_html?
      insist: |
        datum.class == String and datum.valid_html?
- type: CSV
  desc: A valid CSV data string for embedding tabular data in a single field.
  rules:
    ruby:
      class: CsvType
      detect: |
        datum.class == String and datum.match?(/\A[\w\W]*\n[\w\W]*\n\z/m) and datum.valid_csv?
      insist: |
        datum.class == String and datum.valid_csv?
- type: SGYML
  desc: A valid SGYML _document_.
  note: |
    This is a single String, not a data object.
    For designating an actual SGYML data object, use the `Sgyml` type.
  rules:
    ruby:
      class: SgymlType
      detect: |
        datum.class == String and datum.match?(/\A\$\schema:\n/m) and datum.valid_sgyml?
      insist: |
        datum.class == String and datum.valid_sgyml?
- type: SGYMLSchema
  desc: A valid SGYML _schema document_.
  note: |
    This is a single String, not a data object.
    For designating an actual SGYML schema object, use the `SgymlSchema` type.
  rules:
    ruby:
      class: SgymlSchemaType
      detect: |
        datum.class == String and datum.match?(/\A\$\schema:\n/m) and datum.valid_sgyml_schema?
      insist: |
        datum.class == String and datum.valid_sgyml_schema?
- type: JSONSchema
  desc: A valid JSON Schema definition _document_.
  rules:
    ruby:
      class: JsonSchemaType
      detect: |
        datum.class == String and datum.match?(/\A\{\s*"\$schema"\s*:\s*".*"\s*\}\s*\z/m) and datum.valid_json_schema?
      insist: |
        datum.class == String and datum.valid_json_schema?
- type: Template
  desc: A string that may contain template placeholders or logic in a given syntax, such as Liquid, ERB, platonic, Mustache, Jinja2, and so forth.
  note: |
    A field designated as accepting Template data will accept a String formatted as a template.

    Graphy does not parse or evaluate templates.
    
    The following would all be valid:
    
    [horizontal]
    liquid:: "This is a {{ placeholder }} in Liquid, and this is a {% tag %} in Liquid."
    erb:: "This is a <%= placeholder %> in ERB, and this is a <% tag %> in ERB."
    platonic:: "This is a <placeholder> in platonic."
    Mustache:: "This is a {{ placeholder }} in Moustache, and this is a {{# tag }} in Mustache:."
    jinja2:: "This is a {{ placeholder }} in Jinja2, and this is a {% tag %} in Jinja2."
    slim: "This is a {{ placeholder }} in Slim, and this is a {{# tag }} in Slim."
  rules:
    ruby:
      class: TemplateType
      detect: |
        datum.class == String and datum =~ /(\{\{(\=?\#)?[^}]*\}\}|\{\%[^%]*\%\}|\{[^}]+\}|<%=?[^%]*%>|<\#[^>]*>|<\$[^>]*>|<\[[^\]]*\]>|<[a-zA-Z0-9](?:[a-zA-Z0-9_\-]{0,23})[a-zA-Z0-9]>|\$\{[^}]*\})/gm
      insist: |
        datum.class == String
  variants: # TEMPLATE FORMATS
    - type: Platonic
      desc: A string that may contain a generic placeholder.
      rules:
        ruby:
          class: PlatonicTemplateType
          detect: |
            datum.is_a? String and datum =~ /<[a-zA-Z0-9](?:[a-zA-Z0-9_-]{0,23}[a-zA-Z0-9])?>/
          insist: |
            datum.is_a? String
    - type: AsciiDocument
      desc: A full AsciiDoc document, with attributes, macros, headings, and so forth permitted.
      rules:
        ruby:
          class: AsciiDocumentType
          detect: |
            datum.class == String and datum.match?(/\A= \w[\w\-]*\s*\n.*\n\z/m) and datum.valid_asciidoc_document?
          insist: |
            datum.class == String and datum.valid_asciidoc_document?
    - type: Liquid
      desc: A string that may contain Liquid placeholders or logic.
      rules:
        ruby:
          class: LiquidTemplateType
          detect: |
            datum.is_a? String and datum =~ /\{\{.*\}\}|\{\%.*\%\}/"
          insist: |
            datum.is_a? String
    - type: ERB
      desc: A string that may contain ERB placeholders or logic.
      rules:
        ruby:
          class: ERBTemplateType
          detect: |
            datum.is_a? String and datum =~ /<%=?[^%]*%>/"
          insist: |
            datum.is_a? String
    - type: Mustache
      desc: A string that may contain Mustache placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\{\{[^}]+\}\}/"
          insist: |
            datum.is_a? String
    - type: Jinja2
      desc: A string that may contain Jinja2 placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\{\{.*\}\}|\{\%.*\%\}/"
          insist: |
            datum.is_a? String
    - type: Handlebars
      desc: A string that may contain Handlebars placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\{\{[^}]+\}\}/"
          insist: |
            datum.is_a? String
    - type: Twig
      desc: A string that may contain Twig placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\{\{.*\}\}|\{\%.*\%\}/"
          insist: |
            datum.is_a? String
    - type: Smarty
      desc: A string that may contain Smarty placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\{[^}]+\}/"
          insist: |
            datum.is_a? String
    - type: Velocity
      desc: A string that may contain Velocity placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\$\{.*\}/"
          insist: |
            datum.is_a? String
    - type: Freemarker
      desc: A string that may contain Freemarker placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /<\#.*>|<\$.*\>/"
          insist: |
            datum.is_a? String
    - type: Thymeleaf
      desc: A string that may contain Thymeleaf placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\[\[.*\]\]/"
          insist: |
            datum.is_a? String
    - type: Django
      desc: A string that may contain Django placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /\{\%.*\%\}|\{\{.*\}\}/"
          insist: |
            datum.is_a? String
    - type: Pug
      desc: A string that may contain Pug placeholders or logic.
      rules:
        ruby:
          detect: |
            datum.is_a? String and datum =~ /-?\s*\{.*\}/"
          insist: |
            datum.is_a? String
