terms:
  - slug: datum
    term: datum
    desc: |
      A single instance of data, typically value or a parameterized (named/keyed) object.
    apps: [sgyml,data-schema,urix]document

  - slug: object
    term: object
    desc: |
      A named or specified instance of a [.term]*datum*.
      Data of any class or type, in either native or document source format, as long as it is part of a named parameter or identifiable as a sub-element of a known/named datum, and seen as [.term]*data* at the point of relevance to discussion.
      
      The capitalized term [.term]*Object* as used in JSON Schema is considered a [.term]*Map* in SchemaGraphy/SGYML.
      SchemaGraphy has no concept of an [.fterm]*object*/[.term]*Object* as a type or class of data; it's just a generic reference to a piece of data that can be referenced.
    apps: [sgyml,data-schema,text-schema,urix]
  
  - slug: object-json
    term: Object (JSON)
    desc: |
      In JSON, an "`object`" is one or more key-value pairs, where the key is a string and the value is a scalar, array, or another object.

      To avoid confusion, SchemaGraphy always capitalizes "`Object`" when referencing the JSON data type, but in SchemaGraphy parlance, this is typically a [.term]*Map*.
    apps: [sgyml,data-schema,urix]
  
  - slug: object-path-urix
    term: object path
    desc: |
      A reference to the location of a specific [.term]*object* or [.term]*datum* in a data document.
      Should return either a serialized ([.term]*Composite*) object or an atomic ([.term]*Scalar*) value.
      
      If the path is to a [.term]*Map* with only one key-value pair as its value, it will return that key-value pair.
    apps: [urix]
  
  - slug: object-path-sgyml
    term: object path
    desc: |
      A reference to the location of a specific [.term.trm-object]*data object* or [.term]*datum*.
      Should return either a serialized ([.term]*Composite*) object or an atomic ([.term]*Scalar*) value.

      In SGYML, this can either be a [.term]*JSON Pointer* or a [.term.trm-urix]*URIx Fragment*.
    apps: [sgyml,data-schema]

  - slug: subject
    term: subject
    desc: |
      Schema-governed content, in either form, [.term.trm-subject-text]*text* or [.term.trm-subject-data]*data*.
      Content may be [.term]*subject to* governance by a schema.
    apps: [sgyml,data-schema,text-schema]

  - slug: subject-data
    term: subject data
    desc: |
      An object or objects governed by a schema, either (1) native to a programming language or other platform or (2) in String form.
    apps: [sgyml,data-schema,text-schema]
  
  - slug: subject-text
    term: subject text
    desc: |
      Textual matter governed by a schema, usually in the form of a document.
    apps: [sgyml,text-schema,data-schema]
  
  - slug: document
    term: document
    desc: |
      A String that is either in the form of a flat-file or in memory, typically marked up as text matter or a data objet.
    apps: [sgyml,text-schema,urix]
    also: [data-document,text-matter]

  - slug: data-document
    term: data document
    desc: |
      Data stored as a String, possibly across multiple newlines (`\n`), in a recognized markup that enables ingestion into and interpretation by computer programs.
      Commonly JSON, XML, YAML, CSV, and so forth.
      Usually stored as a [.term]*flat-file* in [.term]*plaintext* format, but conceivably also stored in computer memory, so long as it is in String form rather than [.term]*native* format.
    apps: [sgyml,data-schema,urix]

  - slug: native-data
    term: native data
    desc: |
      Data that is already in the form used by a programming language or other computer system.
      The data might be a String, but it is not strictly in String format _unless the content itself is only a string_. 
    apps: [sgyml,data-schema]
  
  - slug: text-matter
    term: text matter
    akas: [text document,text object]
    desc: |
      Referencable (named) textual content, whether subject to a schema or just being referenced as a static cluster of words, usually but not necessarily containing markup syntax.
    apps: [sgyml,data-schema,text-schema]
  
  - slug: markup
    term: markup
    desc: |
      A set of symbols or characters that are used to define the structure of a data document or text matter.
      Markup is mainly used to indicate [.term]*structure* and [.term]*semantics* in the content of a document.
    apps: [sgyml,text-schema]

  - slug: structure
    term: structure
    desc: |
      The [.term]*arrangement* of the contents of a text document or text matter, as well as the relationship of the parts of a data object.
    apps: [sgyml,data-schema,text-schema,urix]
  
  - slug: semantics-text
    term: semantics
    desc: |
      The meaning or type of elements of text in documents or of data in structured objects.
      In SchemaGraphy/SGYML terms, almost exclusively referring to marked-up text or data, where elements of [.term]*content* are designated or implied to have [.term]*types* (or [.term]*classes*).

      Semantic is divided into built-in ("`systemic`") semantics and custom ("`user-defined`") semantics.
      
      Systemic semantics come in the form of pre-defined elements, like [.term]*section*, [.term]*paragraph*, [.term]*table*, [.term]*admonition*, [.term]*strong*, [.term]*emphasized*, [.term]*code*, and so forth.

      User-defined semantics are generally established as arbitrary [.term]*classes* or [.term]*roles* assigned to textual elements that already have a systemic meaning.
    apps: [text-schema]
  
  - slug: semantics-data
    term: semantics
    desc: |
      The meaning or type of elements of data in structured objects.
      In SchemaGraphy/SGYML terms, almost exclusively referring to marked-up text or data, where elements of [.term]*content* are designated or implied to have [.term]*types* (or [.term]*classes*).

      Semantic is divided into built-in ("`systemic`") semantics and custom ("`user-defined`") semantics.

      In native and document data formats alike, systemic semantics are the globally designated data types or classes applied across most or all applications that support the system.
      
      User-defined semantics may refer to the implications of property names (parameter keys) associated with data objects.
      After all, data objects are keyed so the user or supporting systems know what the datum represents or is intended for.

      In some data-document syntaxes, such as XML and YAML, semantics can further be established with annotation, such as by using "`attributes`" in XML or "`node tags`" in YAML.
    apps: [sgyml,data-schema]

  - slug: schema-data
    term: schema
    desc: |
      A specially structured document that constrains and further defines the structure and content of subject data objects or textual matter.
      
      SchemaGraphy data schemas establish limitations and requirements of governed data objects, as well as _implied properties[.term]* and their *default values_.
      
      Data schemas may also include information about pre-processing and parsing of SGYML data documents.
    apps: [sgyml,data-schema]

  - slug: schema-text
    term: schema
    desc: |
      A specially structured document that constrains and further defines the structure and content of subject data objects or textual matter.
      
      SchemaGraphy text schemas establish limitations and requirements of text matter, as well as rules for how text matter is to be structured, formatted, and syntactically expressed.
    apps: [sgyml,text-schema]

  - slug: sgyml
    term: SGYML
    desc: |
      The YAML-extending syntax defined by SchemaGraphy.
      This is the format typically [.term]*governed by* SchemaGraphy data schemas, though such schemas can govern plain YAML and JSON, or even native data formats.
      
      Additionally, SchemaGraphs are [.term]*written in* SGYML, typically with dynamic features enabled, though all SGYML dynamic extensions are disabled for non-YAML sources and also can be disamled for YAML as well, using either a SchemaGraph that disables extensions in YAML-formatted subject data, or else by disabling them in the validation and parsing engine.
    apps: [sgyml,data-schema,text-schema]

  - slug: validation
    term: validation
    desc: |
      The process of checking [.term]*subject data* or [.trm.trm-subject-text][.term]*text* against a schema to ensure that it conforms to the schema's constraints.
    apps: [sgyml,data-schema,text-schema]

  - slug: parsing-data
    term: parsing
    desc: |
      The process [.term]*validating* and [.term.trm-expansion][.term]*expanding* a schema-governed [.term]*data object* ([.term]*subject data*).

      In the data context, this the [.term]*application* of a schema to an instance of its [.term]*subject data*.
    apps: [sgyml,data-schema]
  
  - slug: directive
    term: directive
    desc: |
      A special instruction used to indicate a dynamic parsing behavior.
      Directives are typically used in SGYML data documents to indicate [.term]*dynamic content* or [.term]*references*.
  
  - slug: data-expansion
    term: data expansion
    desc: |
      The process of adding _implied properties[.term]* and their *default values_ to a schema-governed data object.
      When a data schema establishes default values for defined properties, data is expanded during the _data-parsing_ stage.

      Data[.term]*expansion does *not_ refer to substituting variable placeholders in templates or text or data documents.
      This is a particular step of the SchemaGraph parsing process.
    apps: [sgyml,data-schema]

  - slug: parsing-text
    term: parsing
    desc: |
      The process of performing dynamic content resolution by substituting variable placeholders, performing logical flow, iterating through loops, and so forth.
    apps: [sgyml,text-schema]

  - slug: pre-processing
    term: pre-processing
    desc: |
      The process of preparing subject data or text for validation or rendering.

      This stage includes [.term]*parsing* and [.term]*normalization* of data or text, as well as [.term]*resolution* of most [.term]*dynamic content*.
    apps: [sgyml,data-schema,text-schema]

  - slug: resolving
    term: resolving
    desc: |
      The process of replacing [.term]*dynamic content* with its resolved value in a [.term]*document*.
      This is typically done during _pre-processing_.
      This does include [.term]*dereferencing* and _variable substitution_ but does not include [.term]*data expansion*.
    apps: [sgyml,data-schema,text-schema]

  - slug: dereferencing
    term: dereferencing
    desc: |
      The process of replacing [.term]*references* and [.term]*includes* with the referenced or transcluded target/contents.
      This is typically done during _pre-processing_.

      For SGYML data documents, this means [.term]*resolving* data indicated with `$ref`, `$refs`, or `$include` directives.
      For AsciiDoc text documents, this means [.term]*resolving* `include::` directives.
    apps: [sgyml,data-schema,text-schema]
  
  - slug: substitution
    term: variable substitution
    desc: |
      The process of replacing tokenized placeholders with values associated with their keys.
      In YAML, this is swapping [.term]*aliases* for the values of their pre-defined [.term]*anchors*.
      In AsciiDoc, this is replacing attribute [.term]*references* for their built-in or pre-defined _document attributes_.

  - slug: rendering-data
    term: rendering
    desc: |
      The process of readying a document for delivery to its next phase.
      
      A rendered template is in its final form, typically with markup that can be consumed by the next converter.
      We typically use Liquid to render into HTML markup, AsciiDoc, or SGYML/YAML.
  
      An SGYML data document is typically rendered to JSON or a native data format.
    apps: [sgyml,data-schema]

  - slug: rendering-text
    term: rendering
    desc: |
      The process of readying a document for delivery to its next phase.
      
      A rendered template is in its final form, typically with markup that can be consumed by the next converter.
      We typically use Liquid to render into HTML markup, AsciiDoc, or SGYML/YAML.
  
      An AsciiDoc text document is typically rendered to HTML5, PDF, ePub, or another format.
    apps: [sgyml,text-schema]

  - slug: conversion
    term: conversion
    desc: |
      The overall process of transforming a document from one format to another.
      This includes [.term]*parsing*, [.term]*rendering*, and _post-processing_, such as saving to a given file format.

  - slug: data-type
    term: data type
    desc: |
      A classification of a data object that determines broad constraints and implications.

      Types include [.term]*String*, [.term]*Number*, [.term]*Boolean*, [.term]*Array*, and so forth, but also variants or "`forms`" such as [.term]*URL*, [.term]*Multiline*, [.term]*Date*, Integer_, [.term]*Float*, [.term]*Dictionary*, and many more.
    apps: [sgyml,data-schema]
  
  - slug: data-format
    term: data format
    desc: |
      A document syntax such as JSON, XML, YAML, or else "`native`" or _programming-language specific_.

      Not to be confused with a data [.term]*variant*, which is sometimes represented as a [.term]*data form* in SGYML semantics.
    apps: [sgyml,data-schema]
  
  - slug: data-kind
    term: data kind
    desc: |
      Data types of the highest categorization.
      Kinds include [.term]*Scalar* and [.term]*Composite*, each of which contain two distinct [.term]*classes* of data.

  - slug: data-class
    term: data class
    desc: |
      The main tier of data types, which closely parallel or match YAML's typing system.

      The valid classes are: [.term]*String*, [.term]*Number*, [.term]*DateTime*, Boolean, Array, Map, and Null.
  
  - slug: data-variant
    term: data variant
    akas: [form]
    desc: |
      A sub-class of a data type, such as [.term]*URL* or [.term]*Email* for a [.term]*String* type.
    apps: [sgyml,data-schema]
  
  - slug: block
    term: block
    desc: |
      A Composite data object represented in YAML's block style, with the property keyname followed by a colon followed by the value or values on subsequent lines.
      Blocks are referenced by their property keyname.
    apps: [sgyml,data-schema,text-schema]
  
  - slug: node
    term: node
    desc: |
      In a YAML document, everything following the `:` in a property setting

$cfg:
  volumes:
    - slug: sgyml-glossary
      name: SGYML Glossary
      collects:
        apps: [sgyml]
    - slug: data-schema-glossary
      name: Data Schema Glossary
      collects:
        apps: [data-schema]
    - slug: text-schema-glossary
      name: Text Schema Glossary
      collects:
        apps: [text-schema]
    - slug: urix-glossary
      name: URIx Terminology
      collects:
        apps: [urix]