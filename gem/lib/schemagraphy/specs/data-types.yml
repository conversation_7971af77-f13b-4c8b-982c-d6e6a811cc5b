# SCALAR TYPES
components:
  data-type-rules:
    $ref: './native/data-type-rules.yml'
types:
- type: String
  akas: [str]
  kind: Scalar
  desc: An object consisting of alphanumeric characters and symbols, or some constrained subset thereof.
  yaml: string (`str`)
  rules:
    $ref: '#components/data-type-rules/String'
  variants:
    - type: Line
      desc: Any content of any length not containing newlines or returns.
      rules:
        $ref: '#components/data-type-rules/Line'
    - type: Multiline
      desc: A potentially multi-line string, whether literal or force-folded.
      alias: [Block]
      rules:
        $ref: '#components/data-type-rules/Multiline'
      variants: # LiteralBlock + Markup and Templating variants
        $ref: ./data-variants-multiline.yml
    - type: DataType
      desc: |
        A valid SGYML data type or format name.
        DataType simply governs a field that asks for a DataType name.
        Functionality related to that field will have to handle the DataType validation.
      rules:
        $ref: '#components/data-type-rules/DataType'
      variants:
        - type: ScalarDataType
          desc: |
            Any of the _scalar_ (atomic) SGYML data types or their variant formats.
            Must be one of `String`, `Number`, `Boolean`, `DateTime`, or `Null`.
          rules:
            $ref: '#components/data-type-rules/ScalarDataType'
        - type: CompositeDataTYpe
          desc: |
            Any of the _Composite_ data types or their variant formats.
            Basically `Map` or `Array`.
          rules:
            $ref: '#components/data-type-rules/CompositeDataType'
    - type: SemVer
      desc: Semantic Versioning-formatted version/release "`number`" indicator.
      rules:
        ruby:
          $ref: '#components/data-type-rules/SemVer'
    - type: Hostname
      desc: A proper hostname according to RFCs 1034 and 1123.
      rules:
        $ref: '#components/data-type-rules/Hostname'
    - type: Path
      desc: A local path, either absolute (`/some/root/path/file.txt`) or relative (`./nearby/file.txt`), but not remote.
      akas: [uri-reference]
      rules:
        ruby:
          $ref: '#components/data-type-rules/Path'
    - type: DirPath
      desc: A path that ends in a directory.
      rules:
        $ref: '#components/data-type-rules/DirPath'
    - type: FilePath
      desc: A path that ends in a file.
      rules:
        $ref: '#components/data-type-rules/FilePath'
    - type: URI
      desc: A local path or a fully-qualified remote URI (URL).
      rules:
        $ref: '#components/data-type-rules/URI'
    - type: URL
      desc: A fully-qualified URI that includes the schema/protocol prefix.
      rules:
        $ref: '#components/data-type-rules/URL'
    - type: Glob
      desc: A file/path-matching pattern with wildcards, such as `*.txt` or `**/*.txt`.
      rules:
        $ref: '#components/data-type-rules/Glob'
    - type: JMESPath
      desc: A JMESPath expression.
      rules:
        $ref: '#components/data-type-rules/JMESPath'
    - type: JSONPath
      desc: A JSONPath expression.
      rules:
        $ref: '#components/data-type-rules/JSONPath'
    - type: JSONPointer
      desc: A JSON Pointer expression.
      rules:
        $ref: '#components/data-type-rules/JSONPointer'
    - type: Email
      desc: A valid email address.
      rules:
        $ref: '#components/data-type-rules/Email'
    - type: ClassesString
      desc: |
        One or more valid HTML5 classes delimited by one of: dots, commas, or spaces.
      rules:
        $ref: '#components/data-type-rules/ClassesString'
    - type: Selector
      desc: A CSS or XPath selector.
      rules:
        $ref: '#components/data-type-rules/Selector'
    - type: Slug
      desc: An unbroken alphanumeric String with `_` and/or `-` also permitted.
      rules:
        $ref: '#components/data-type-rules/Slug'
      variants:
          $ref: ./data-variants-slug.yml
    - type: RegExp
      desc: A regular expression pattern stored as a standard String or Multiline, prepended by `/` and appended with `/` and optionally regex scope/mode designators.
      akas: [Regex]
      docs: |
        Interpreters must read one-line YAML strings wrapped in slashes and prepended with `r` and optionally appended by a mode string
         `r/expression/g`, as a regular expression.
        This provides a clean way for passing literal RegEx patterns to the application.
        If the pattern includes both kinds of quotes, use a YAML block style and ensure the interpreter will strip surrounding whitespace or in any case detect the wrapper chars.
      rules:
        $ref: '#components/data-type-rules/RegExp'
    - type: StringList
      desc: A List (series of scalars), but optionally formatted as a comma-delimited String.
      rules:
        $ref: '#components/data-type-rules/StringList'
      docs: |
        Typically, a StringList is a comma-delimited string of scalar values, but a schema can designate a delimiter under `<prop>:rules:delims`.
        A valid StringList is convertable to an ArrayList.
- type: Number
  kind: Scalar
  desc: A number, either whole or floating-point.
  yaml: number (`int` or `float`)
  rules:
    $ref: '#components/data-type-rules/Number'
  variants:
    - type: Integer
      desc: A whole-number integer
      yaml: int
      rules:
        $ref: '#components/data-type-rules/Integer'
      variants:
        - type: UnixEpoch
          desc: A Unix Epoch timestamp, either in seconds or milliseconds.
          akas: [Epoch]
          rules:
            $ref: '#components/data-type-rules/UnixEpoch'
    - type: Float
      desc: A floating-point number
      yaml: float
      rules:
        $ref: '#components/data-type-rules/Float'
      variants:
        - type: Money
          desc: A floating-point number representing a monetary value.
          rules:
            $ref: '#components/data-type-rules/Money'
- type: DateTime
  kind: Scalar
  desc: |
    A date, time, or datetime value that corresponds to a calendar or a relative point in time.
    Includes any link:https://yaml.org/type/timestamp.html[YAML-supported "`timestamp`" format].
  yaml: timestamp
  rules:
    $ref: '#components/data-type-rules/DateTime'
  variants:
    - type: DateTimeISO
      desc: |
        A calendar date, including a time reference with optional UTC indicator.
        Conforms to ISO-8601 format.
      rules:
        $ref: '#components/data-type-rules/DateTimeISO'
    - type: Date
      desc: |
        A calendar date in YYYY-MM-DD format.
      rules:
        $ref: '#components/data-type-rules/Date'
    - type: Time
      desc: |
        Time of day on a 24-hour clock, with optional UTC indicator.
      rules:
        $ref: '#components/data-type-rules/Time'
    - type: DateUSA
      desc: |
        A calendar date in MM/DD/YYYY format.
      rules:
        $ref: '#components/data-type-rules/DateUSA'
    - type: DateTimeCanonical
      desc: |
        A date and time with optional and timezone offset.
      rules:
        $ref: '#components/data-type-rules/DateTimeCanonical'
    - type: DateTimeSpaced
      desc: |
        A date and time with optional timezone offset.
      rules:
        $ref: '#components/data-type-rules/DateTimeSpaced'

- type: "Null"
  akas: ['none','nil']
  desc: A null or nil value.
  yaml: "null"
  docs: |
    Literally any form of `Null`/`null`/`NULL` in YAML or specifically `null` in JSON.
    
    Forms of `nil` and `none` will be treated as Strings in YAML but interpreted as forms of Null if so-configured.

    Null usually implies _falsiness_ while not failing property existence checks.

    A Null datum is something you permit or deny the use of but cannot further define.

- type: Boolean
  kind: Scalar
  desc: A binary value (`false` or `true`) even as a String. A nil value reports as `false` but detects as `Null`.
  yaml: boolean (`bool`)
  side: |
    A Boolean is not traditionally considered a Scalar, but we categorize it as such because our fundamental differentiation is the complexity of the value.
    That is to say, our main concern is whether it is Composite or not, so _not Composite_ is effectively our definition of _Scalar_.
  rules:
    ruby:
      class: BooleanType
      detect: |
        datum.class.in?([TrueClass, FalseClass]) or (datum.is_a?(String) and datum =~ /^(true|false)$/i)
      insist: |
        datum.class.in?([TrueClass, FalseClass])
  variants:
    ref: ./data-variants-boolean.yml

#
# COMPOSITE TYPES #########
#

- type: Map
  akas: [mapping,object]
  kind: Composite
  desc: A set of parameterized objects.
  yaml: mapping (`map`)
  rules:
    ruby:
      class: Hash
      detect: |
        datum.class == Hash
      insist: |
        datum.class == Hash
  docs: |
    In SGYML, a Map is a key-mapped object, sometimes in other languages called an "`Object`", "`Dictionary,`", "`Hash,`" "`HashMap`", or a "`Mapping`".
    A Map may be nested, where the value of a key-value pair is another Map.

    Any key-value pair is actually a Map, but some times Maps take more compound forms, such as a MapTable.
  variants:
    - type: Dictionary
      desc: A Map of parameters with scalar values (no nesting).
      rules:
        ruby:
          class: DictionaryType
          detect: |
            datum.class == Hash and datum.size > 1 and datum.keys.all? { |k| k.class == String } and datum.values.all? { |v| v.class != Hash and v.class != Array }
          insist: |
            datum.class == Hash and datum.keys.all? { |k| k.class == String } and datum.values.all? { |v| v.class != Hash and v.class != Array }
    - type: MapTable
      desc: |
        A Map consisting of named nodes made up of commonly keyed properties.
        This is the Map version of an ArrayTable, where the index is named in the key itself.
      rules:
        ruby:
          class: MapTableType
          detect: |
            datum.class == Hash and datum.keys.all? { |k| k.class == String } and datum.values.all? { |v| v.class == Hash }
          insist: |
            datum.class == Hash and datum.keys.all? { |k| k.class == String } and datum.values.all? { |v| v.class == Hash }
      docs: |
        A MapTable is similar to an ArrayTable, except each node is named by a key.

        [source,yaml]
        ----
        map_table:
          node_1: # key
            prop1: Some value
            propB: false
          node_2: # key
            prop1: Another value
            propB: true
        ----

        Nodes are arbitrarily named, where a valid YAML keyname replaces the `-` character and the ID property in an Array-style ArrayTable.
        There is effectively no difference between (1) a ArrayTable with a unique-identifying parameter in each node and (2) a MapTable, where the node's keyname serves as the unique identifier.
        Thus, the above example is effectively the same as this ArrayTable:

        [source,yaml]
        ----
        - id: node_1
          prop1: Some value
          propB: false
        - id: node_2
          prop1: Another value
          propB: true
        ----
    - type: Schema
      desc: |
        Parent category of Map object that dictates the expected shape and values of serialized data, either an SGYML 'SchemaGraph' or a JSON Schema object is valid.
        
        XML Schema types may be added eventually, but they would be Multiline String variants rather than Map variants.
      rules:
        ruby:
          detect: |
            datum.class == Hash and datum['$schema']
          insist: |
            datum.class == SchemaGraphType || datum.class == JSONSchemaType
      variants:
        # the 2 supported Schema subtypes
        - type: SchemaGraphType
          akas: [GraphySchema,SchemaGraphy]
          desc: An explicit instance of a SchemaGraph document.
          rules:
            ruby:
              class: SchemaGraph
              detect: |
                datum.class == Hash and datum['$schema'] and (datum['$payload'] or datum.keys.size == 1)
              insist: |
                SchemaGraph.validate(datum)
          note: |
            This refers to an actual data Map, not an String that happens to be an SGYML Schema.
            Use `type: SGYML` to insist that a String entry be in SGYML format.
        - type: JSONSchema
          desc: An explicit instance of a JSON Schema document.
          rules:
            ruby:
              class: JSONSchemaType
              detect: |
                datum.class == Hash and datum['$schema'].is_a? String
              insist: |
                datum.class == JSONSchemaType
    - type: Set
      desc: A Map of valueless keys, where properties are Null.
      rules:
        ruby:
          class: SetType
          detect: |
            datum.class == Hash and datum.values.all? { |v| v.nil? }
          insist: |
            datum.class == Hash and datum.values.all? { |v| v.nil? }
      docs: |
        Sets can be represented in YAML using a few different syntaxes, but the basic syntax is straightforward:

        [source,yaml]
        ----
        setname:
          key1:
          key2:
          key3:
        flow-style_set: {key1, key2, key3}
        ---

- type: Array
  akas: [sequence,seq]
  kind: Composite
  desc: Objects (items) in a single series.
  yaml: sequence (`seq`)
  rules:
    ruby:
      class: Array
      detect: |
        datum.class == Array
      insist: |
        datum.class == Array
  docs: |
    An object designated as an Array can accept either a scalar object or a Composite in each item slot (node).
    An object designated as a List or ArrayTable must contain either Scalars (List) or Maps ArrayTable).
    Arrays may have a required property defined in their `_meta` table, which will serve as the key name for any scalar items in the Array.
  variants:
    - type: ArrayList
      akas: [List]
      desc: An array of scalar objects (Strings, Numbers, Booleans) in a single series.
      rules:
        ruby:
          class: ArrayListType
          detect: |
            datum.class == Array and datum.all? { |v| v.class == String || v.class == Integer || v.class == Float || v.class == TrueClass || v.class == FalseClass }
          insist: |
            datum.class == Array and datum.all? { |v| v.class == String || v.class == Integer || v.class == Float || v.class == TrueClass || v.class == FalseClass }
      docs: |
        A List is an Array of scalar objects.
        
        [source,yaml]
        ----
        listname:
          - Some value
          - Another value
          - false
          - 5
        ----

        An alternative way of expressing the above:

        [source,yaml]
        ----
        listname: [Some value, Another value, false, 5]
        ----
    - type: ArrayTable
      desc: |
        An un-keyed array of Maps with one or more consistent params per Array item ('`node`').
        At least one parameter should be designated as the unique identifier for each node.
      rules:
        ruby:
          class: ArrayTableType
          detect: |
            datum.class == Array and datum.all? { |v| v.class == Hash }
          insist: |
            datum.class == Array and datum.all? { |v| v.class == Hash }
      docs: |
        A ArrayTable is a Collection with unnamed nodes formatted as Array objects.
        
        [source,yaml]
        ----
        array_table:
          - name: node_1
            prop1: Some value
            propB: false
          - name: node_2
            prop1: Another value
            propB: true
        ----
    - type: OMap
      desc: |
        An Array of single-key Maps, or _ordered Map_, with one key-value pair per item.
      docs: |
        Here is an example OMap:

        [source,yaml]
        ----
        omap_object:
          - key1: value1
          - key2: value2
          - key3: value3
        ----
      rules:
        ruby:
          class: OMapType
          detect: |
            datum.class == Array and datum.all? { |v| v.class == Hash and v.keys.size == 1 }
          insist: |
            datum.class == Array and datum.all? { |v| v.class == Hash and v.keys.size == 1 }