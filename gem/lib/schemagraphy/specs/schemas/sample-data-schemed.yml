# $schema:
#   $ref:
#     file: _schemas/products.yml
# Commented-out YAML such as the above will be read when your data file is
#  ingested into a supported platform.
# Rather than embedding your schema definition in the data file,
#  just reference the schema metadata at the top of your file.
# This # $schema: block can contain an of the properties found in a main $schema:
#  block, including .
#
# Alternatively, a file lacking no embedded or frontmatter-referenced schema
#  can be manually assigned a schema when ingesting or writing a data file.
# For instance:
#  liquidoc -d mydata.yml --schema _schemas/main-objects.yaml -v env=live
#     vars:
#       env: live
#
# In either case, you will not need a `payload:` property.
# The root of your data file will serve as the root of your payload.
- id: 1
  name: whatever
- id: 2
  name: whatever 2
