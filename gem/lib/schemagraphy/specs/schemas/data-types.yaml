$schema:
  type: ArrayTable
  desc: |
    A means of documenting a collection of data types.
    The prototype instance of this model is the SGYML data-typing system.
    See the `specs/data-types.yml` file and the `specs/data-variants-*` files
     in this repo for more.
    See this data rendered at:
     https://schemagraphy.docopslab.org/schemagraphy/specs/schemas/data-types
  properties:
    _meta:
      req: [type,desc,kind]
      acc: [akas,note,subs,rules]
    kind:
      desc: The kind (Scalar or Enumerable) of data type.
      type: String
      options:
        scalar:
          desc: A single value.
        composite:
          desc: A collection of values.
    type: &type_type_property
      desc: The type of data.
      type: String
    desc: &desc_property
      desc: A description of the data type.
      type: String
    akas:
      desc: Alternate keynames for the data type.
      type: Array
      items:
        type: String
    note: &note_property
      desc: A note about the data type.
      type: String
    rules: &rules_property
      desc: Collections of language-specific rules for the data type.
      type: Array
      items:
        type: String
    variants: # nested variants
      type: ArrayTable
      properties:
        _meta:
          req:
            <<: *required_fields
            type:
          acc: [desc,note]
        type: *type_type_property
        kind: &variant_kind_property
          default:
            code: "this.parent.properties['kind'].value"
        desc: *desc_property
        note: *note_property
        rules: *rules_property
        variants: # secondary nested variants
          type: ArrayTable
          properties:
            _meta:
              req:
                <<: *required_fields
                - type
              acc: [desc,note]
            type: *type_type_property
            kind: *variant_kind_property
            desc: *desc_property
            note: *note_property
            rules: *rules_property



