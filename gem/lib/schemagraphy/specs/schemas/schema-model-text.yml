# The contents of this file are _documentation_ of the content schema model.
# None of the data/content in this file is used to _define_ the structure or
#  functionality of the content/schema handling system.
# These data objects exist to help _describe_ the data objects at work in
#  LiquiDoc.
# See sibling file schema-model-data.yml for further context.
$schema:            # <1>
  $import:          # <2>
    source:           (String) A Schema to import, possibly containing Liquid markup
    vars:           # <3>
      <key>:        Arbitrary data of any type to pass to schema template as `var.<key>`; unlimited
    lang:           (String) Templating language of the imported source, if any.
  elements:         # <4>
    _meta:          # <5> (namespace reserved)
    <elem>:         # <6> (arbitrary element label)
      target:       (String) The keyname of an element to define
      usage:        # <7>
        txt:        (String) A description of how this element is to be used
        not:        (String) A description of how this element is NOT to be used
      context:      # <8>
        ancestors:
          acc:      (Array of Strings) Keynames of elements that _may_ reside in a superior context to this element
          req:      (Array of Strings) Keynames of elements that _must_ reside in a superior context to this element
          rej:      (Array of Strings) Keynames of elements that _must not_ reside in a superior context to this element
        parents:
          acc:      (Array of Strings) Keynames of elements (blocks) that this element _may_ reside in
          req:      (Array of Strings) Keynames of elements (blocks) that this element _must_ reside in
          rej:      (Array of Strings) Keynames of elements (blocks) that this element _must not_ reside in
        descendents:
          acc:      (Array of Strings) Keynames of elements that _may_ reside somewhere in the context of this element
          req:      (Array of Strings) Keynames of elements that _must_ reside somewhere in the context of this element
          rej:      (Array of Strings) Keynames of elements that _must not_ reside anywhere in the context of this element
        children:
          acc:      (Array of Strings) Keynames of elements that _may_ reside inside instances of this block element
          req:      (Array of Strings) Keynames of elements that _must_ reside inside instances of this block element
          rej:      (Array of Strings) Keynames of elements that this element _must not_ reside in
        siblings:
          acc:      (Array of Strings) Keynames of elements that _may_ reside alongside this element
          req:      (Array of Strings) Keynames of elements that _must_ reside alongside this element
          rej:      (Array of Strings) Keynames of elements that _must not_ reside alongside this element
      ui:           # <9>
        label:      (String) A formal, user-facing label; usually the element's id or key field, title-cased
        hint:       (String) Something to help the user figure out what this field is for
        error:      (String) A custom error message to report invalid entry for the defined parameter.
      format:       # <10>
        <layer>:    # <11>
          det:      (Regex) Pattern to trigger element instance found (begin validation)
          rej:      (Regex) Pattern to reject during validation
          acc:      (Regex) Pattern to accept during valudation
          sev:      (String) Indicates severity of format violations (none|warn|fail); [defaults to `warn`]
          msg:      (String) A custom error message for format violations

# tag::block-defs[]
Maps:
  1:
    key: "$schema"
    txt: |
      Every schema definition file, whether complete or partial, must have a `schema:` block.
      For inline schemas, this block can go on the bottom, beneath the `nodes:` block.

  2:
    key: "$schema.$import"
    txt: |
      Optionally cross-reference an external schema template to parse and “`transclude`” it at this point.
      The referenced schema's data will pass to the current schema in which it is newly “embedded”.
      This “cascading” data will be overwritten by any inline schema settings entered beneath it.
      See <<schema-data-import>>.

  3:
    key: "schema.$import.vars"
    txt: |
      Schemas can be dynamic templates containing Liquid markup.
      If this is the case, you may wish to pass variables in, just as you would with a LiquiDoc parse operation.
      They will be accessible by the imported schema template as Liquid variables under the `vars.` scope (`vars.<key>`).

  4:
    key: schema.elements
    txt: |
      Everything contained in `elements:` refers to the contents of a data record -- a single Map, whether alone or in an array.
      If the defined object is an Array, the schema will apply equally to all contained records except as defined in the `array:` section, a peer of `elements:`.

  5:
    key: schema.elements._meta
    txt: |
      Schema settings that apply to all elements in governed objects.
      (The leading underscore [`_`] character indicates the key is not an element name designator like its siblings, which follow.)

  6:
    key: schema.elements.<elem>
    txt: |
      Named using the literal keyname of the parameter to which it applies in the dataset.
      Add up to as many elements as your governed data object could have parameters within it.
      All of the Maps in each `<elem>:` block define that element across all nodes in any governed dataset (unless any are explicitly excepted under `schema.array.except`).

  7:
    key: schema.elements.<elem>.usage
    txt: |
      This block contains technical information about the purpose of the element.
      One use might be indicating to reviewers how to evaluate the value of the field.

  8:
    key: schema.elements.<elem>.context
    txt: |
      These parameters constrain or impose upon values of instances of the element in a governed object.

  9:
    key: schema.elements.<elem>.context
    txt: |
      Data object elements can reference (point to) one another by noting the `<key>` element of another data record.
      If the currently defined element (`<elem>`) is one such field, it is helpful to define the relationship here.
      This will help populate UI entry options and prepare GraphQL queries to merge external data with primary records on retrieval.
      See <<schema-element-context>> and <<graphql-queries>> for more.

  10:
    key: schema.elements.<elem>.ui
    txt: |
      The `ui:` block details what might be reflected of this element in user interfaces.
      These interfaces may be boilerplate datafiles or else CLI prompts or contextualized/inline form-fill help.

  11:
    key: schema.elements.<elem>.format
    txt: |
      If this element contains a Map or Array, you can schematize that object here.
      New Maps and Arrays can be nested recursively this way, to an untested depth, and they may use explicit schema definitions or a reference to an external schema, (which risks infinite loop recursion).
      Child objects can each be schematized or not.
      Child schemas _do_ effectively become part of the parent Schema for a given instance in the case of stored schemas (the only kind for which it matters).

  12:
    key: schema.elements.<elem>.format.<layer>
    txt: |
      Any or all of source, text, or html
# end::block-defs[]
