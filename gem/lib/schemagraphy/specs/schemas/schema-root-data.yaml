# The uber-meta-meta root schema
# The schema that defines all SchemaGraphy (SGYML) data schemas
# Also serves as the core source for the SGYML specification
#
$schema: &schema_root # indicates this schema governs a complex data object
  # Required for root-level schema, but optional for sub-schemas,
  #  though conventional and recommended for ArrayTable and MapTable
  #  sub-schemas.

  meta: # the actual META data for the SchemaGraphy root schema specification document
    version: # the version of the SchemaGraphy specification
      code: V1b
      date: 2024-08-31
    license: MIT
    spec: https://schemagraphy.docops.org/specs/V1b/schema-root-data.yaml

  anchors:
    data_types: &data_types
      # SCALAR TYPES
      # Basic String Types
      - String
      - Line
      - Multiline
      - LiteralBlock
      - DataType
      - SemVer
      - Path
      - URI
      - URIx
      - URL
      - Email
      - ClassesString
      - Selector
      - Slug
      - UUID
      # Slug variants
      - KeyNameSGYML
      - KebabCase
      - SnakeCase
      - CamelCase
      - PascalCase
      - LowerCase
      - UpperCase
      - LowerAlpha
      - UpperAlpha
      - HashTag
      # Complex Strings
      - RegExp
      - StringList
      - Template
      - JSON
      - YAML
      - SGYML
      - SGYMLSchema
      - AsciiDoc
      - Markdown
      - CSV
      - HTML
      - XML
      # Template variants
      - Platonic
      - Liquid
      - ERB
      - Haml
      - Mustache
      - Jinja2
      - Handlebars
      - Twig
      - Smarty
      - Velocity
      - Freemarker
      - Thymeleaf
      - Django
      - Pug
      # DateTime
      - DateTime # ISO8601
      - Date # YYYY-MM-DD
      - Time # HH:MM:SS -
      - DateUSA # MM/DD/YYYY
      - DateTimeCanonical
      - DateTimeSpaced
      # Numbers
      - Number
      - Integer
      - Float
      - Money
      # Booleans
      - Boolean
      - BooleanExt
      - BooleanYesNo
      - BooleanYN
      - BooleanBit
      - BooleanBinary
      - BooleanNull
      # COMPOSITE TYPES
      # Maps
      - Map
      - MapTable
      - Dictionary
      - Set
      - SchemaGraph
      - JSONSchema
      # Arrays
      - Array
      - ArrayList
      - ArrayTable
      - OMap
    template_types: &template_types
      - Platonic
      - Liquid
      - ERB
      - Haml
      - Moustache
      - Jinja2
      - Handlebars
      - Twig
      - Smarty
      - Velocity
      - Freemarker
      - Thymeleaf
      - Django
      - Pug
    supported_template_types: &supported_template_types []

  # We are NOT importing or inheriting from a superior schema
  # The unusual point of this particular schema definition is explicit hard-coding
  #  of the root data schema.
  type: Map # All schemas are Maps
  properties: # We will now describe base properties of a standard data schema
    # Tip: We are not yet here describing this (uber-)schema's `properties:` param
    #      Find that below at LITERAL
    _meta: # now we will constrain the params available to the `properties:`
      req: [type]
      acc: [anchors,meta,_include,desc,alias,docs,properties,dataset] # 1st-tier schema params
    # Now, on to defining those 1st-tier params directly
    anchors: # the actual anchors property
      type: Map
      desc: An arbitrary block for adding reusable YAML anchors. 
    meta: # Definition of the 1st-tier $schema.meta property
      type: Map
      desc: Metadata about the schema itself.
      docs:
        text: |
          These settings are inherited by sub-schemas, unless overridden in the sub-schema's own root-level `meta` property.

          Settings do not apply to schemas called using the `$schema.$include` property.
          Such schemas will use global (specification-wide) defaults or their own `meta` property settings, as they will be parsed prior to transclusion.
        spec: |
          The `meta` block is used to define metadata about the schema itself, including how it is to be parsed and applied.
          This is information about the schema, not any governed data objects.
          This block is inherited by sub-schemas, unless overridden in the sub-schema's own root-level `meta` property.
      properties:
        _meta:
          acc: [spec,version,rules,functions,license]
        spec:
          alias: [specification]
          desc: |
            The URI of the SchemaGraphy specification and version.
            Must be a "`schema root document`" in valid $schema format.
          type: URI
          default: "https://schemagraphy.docops.org/specs/V1b/schema-root-data.yaml"
          docs:
            notes:
              - Typically this very file, or some version of it.
              - This property differs from `spec` properties nested under `docs` properties.
          spec: | # this property provides the DOCUMENTATION FOR the $schema.meta.spec property
            The `spec` property MUST be a URI to a valid SchemaGraph _specification definition_ file in YAML format, _not_ a rendered text specification document.
            The source will be used to validate the schema itself, and to provide tooling with the necessary information to parse and apply the schema.
            
            If no `spec` property is present, tooling SHALL assume the schema is to be governed by the latest supported version of the SchemaGraphy specification.
        version:
          type: Map
          desc: Version info about the schema itself.
          properties:
            code:
              desc: The version number of the schema.
              type: [SemVer, String, Float, Integer]
              docs:
                spec: |
                  Schemas can use any established or arbitrary versioning approach, even though Semantic Versioning (`SemVer`) is preferred.
            date:
              desc: The date the schema was last updated.
              type: Date
        sgyml:
          alias: [rules]
          type: Map
          desc: |
            Policies for parsing and validating the schema object itself.
            This is where you can standardize and constrain how SGYML is used in the schema.
          properties:
            maps:
              desc: The structure of mapping to enforce in the schema definition.
              type: String
              options: [nested,foldable,flat]
              default: nested
              docs:
                text: |
                  The structure of mapping to enforce in the schema definition.

                  * `nested` indicates a normal YAML structure, with sub-properties indented.
                  * `foldable` indicates a structure that can be folded, with sub-properties indented or delimited with `_` characters.
                  * `flat` indicates a flat structure, with sub-properties at the same level as the parent.
            subs:
              desc: The policy for using substitutions and dereferencing in the schema definition.
              type: ArrayList
              items:
                type: String
                options: ['alias','$ref','$refs','sassy']
              default: ['alias','$ref','$refs']
              docs:
                text: |
                  Specifies which, if any, forms of variable substitution to honor in the schema definition.

                  * `alias` is the native YAML substitution method, with `&anchors` and `*aliases` syntax.
                  * `$ref` transcludes content from object paths in this or external files
                  * `$refs` an alias for `$ref` that you may disallow
                  * `sassy` is the Sass-like substitution method, with `$var` syntax. (Not Yet Implemented)
                  
                  Use an empty Array (`[]`) to disable substitutions in the schema.
        preprocessing:
          desc: Instructions for pre-processing the data document as a text file with templating markup.
          types:
          - type: Boolean
            desc: Whether to preprocess the governed data document at all.
            default: false
            rules:
              regexp: /true|false/
            
          - type: Map
            desc: The preprocessing to be performed on the governed data document purely as a text file.
            docs:
              text: |
                If your subject document includes templating markup, indicate the format (language) and 
              spec: |
                Supportive tooling SHOULD support one or more templating language popular with their runtime platform or developer community.
            properties:
              _meta: [vars,lang]
              vars:
                alias: [variables]
                type: Map
                desc: Variables to pass to the subject document for template processing.
              lang:
                alias: [language]
                type: String
                desc: The language of the preprocessing functions.
                options: *supported_template_types
                default: liquid
        functions:
          type: Map
          desc: Functions to use in the schema definition.
          docs:
            text: |
              If your schema supports dynamic determination of defaults or other arbitrary internal parsing, set the language and pass variables or preliminary commands to all such functions.

              This uses Ruby, Bash, or a templating system to process aspects of the schema based on context.
            notes:
              - This feature is only available to schemas, not to governed data, at this point.
          properties:
            lang:
              desc: The language of the functions used in the schema definition.
              type: String
              options: [ruby,liquid,bash,erb]
              default: erb
            env:
              desc: Environmental commands to pass to all functions in the schema definition.
              type: LiteralBlock
            vars:
              desc: Variables to pass to all functions in the schema definition.
              type: Map
        
        license:
          desc: The license under which the schema is released.
          type: String
          docs:
            text: |
              Schemas are assumed to fall under the license of a parent codebase/repository.
              Otherwise, assign a specific license here.

              If the value matches a shortcode registered at https://aylstack.docops.org/foss-licenses.yml, it will be replaced with the official name of that license and a link to the license text.

              If the value is a URI, the URL serve as the linked text.

              Use something like "ALL RIGHTS RESERVED" to deny reuse of the schema by unauthorized parties.
            
    _include: # settings for the special $schema.$include property
      alias: [$include]
      docs:
        key: "$include"
      types: # this property can be any of 4 types
      - type: String
        desc: Path to the schema file/fragment to import.
        type: URIX
      - type: Map # this block only available in $schema.$import properties
        properties:
          strategy:
            desc: |
                The policy to use when combining the imported schema/s with the current schema.
            docs:
              text:
                The default (`BOTH`) indicates the calling schema must ALSO match.
                Use `EITHER` to indicate the calling schema is optional and can excuse the rules set forth in this block -- the governed data need only match the imported schema/s or the one defined herein.
            type: String
            default: both
              options: [both,either]
          all_of:
            desc: The governed object must validate against ALL these imported schemas.
            $schema:
              type: ArrayTable
              properties: *ref_properties
          any_of:
            desc: The governed object must validate against ANY one of these imported schemas.
            $schema:
              type: ArrayTable
              properties: *ref_properties
          one_of:
            desc: The governed object must validate against EXACTLY ONE of these imported schemas.
            $schema:
              type: ArrayTable
              properties: *ref_properties
      - type: ArrayList
        desc: A list of paths to schema files/fragments to import.
        items:
          type: URIX
      - type: Dictionary
        desc: A source to import, with optional sibling properties.
        properties: &ref_properties # first tier 'properties' key indicates a schema
          _meta:
            acc: [source,vars,lang]
          source: &source_type
            type: URIX
            desc: Path to the schema file/fragment to import
          vars:
            type: Map
            desc: |
                Arbitrary data of any valid type to pass to remote YAML file if it is a template.
          lang:
            type: String
            options: *template_types
            desc: The syntax used if the remote YAML document is templated.
    
    type: # settings for the schema-tier type param
      alias: [types]
      desc: The type or types of data object this schema governs.
      docs:
        text: |
          Typically, each tier of a schema governs one or more _properties_, each of which can accept one or more SGYML _data type_.
          The `type` or `types` keys can be used to directly indicate the data type for any property.

          In the case of a scalar value (String, Number, Boolean) or a simple Array, the `value` property can be used to further define the type.
          
          For scalar types, the `type` property is used to further define the possible values.
          For Arrays, the `items.type` property is used to define the type/s of items allowed in the Array.
      types: # the 'type' property of a schema can be a DataType String or an Array of potentially mixed item types
      - type: DataType
        desc: The `type` property of any schema or governed-object property may simply be a DataType (String) expression.
      - type: Array # items may be DataTypes and/or Maps
        # An Array of types can list simple datatypes or whole schemas
        desc: |
          If a given property can have a value of more than one type, either the `type` or `types` keywords may be used, but the value of that key should itself be an Array.
          The contents of that Array are handled using a *any_of* logic, meaning the contents must match one or more of the listed types, which may be further described.
        items:
          one_of:
          - type: DataType
            desc: A DataType Scalar value that is a valid type for a Schema.
          - type: Schema
            desc: A Schema that further defines the type of the schema/property.
            
        docs:
          text: |
            For a property, the value of which may be _any one_ of two or more designated types,

            By convention, `types` is a semantic indicator that more than one type is accommodated, but whether the `type`/`types` node itself is a String or an Array actually determines how the given `type`/`types` property will be handled.

            If any of the items in an Array-formatted `type` property is a Map, that Map can further define the accompanying type's other attributes.
            
            Otherwise, any item in a multi-type property is simply a DataType (String), that type definition is expected to be the whole specifier for property instances of that type.
          examples:
            - code: |
                $schema:
                  type: Map
                  properties:
                    website:
                      desc: A couple of ways to record a URL
                      type:
                        - URL
                        - type: Map
                          desc: A complex way of recording a URL.
                          properties:
                            href:
                              desc: The actual URL we are recording.
                              type: URL
                            text:
                              desc: A descriptor of this URL.
                    updated_at:
                      desc: When the website was last amended.
                      type: Date
              lang: yaml
              desc: |
                A schema governing an object with a property called `website`, which can 
                  either accept a simple URL String or a Map that contains a URL and some text
                  to accompany that URL.
      
    alias: &alias_property
      desc: |
        One or more alternate keynames for the property.
      alias: [aliases]
      types:
      - type: String
        desc: A single alternate keyname for this property.
      - type: ArrayList
        desc: A list of alternate keynames for this property.
        items:
          type: Slug

    desc: # settings for the schema description param
      alias: [description]
      type: Multiline
      desc: A description of the governed data object.
    
    docs:
      type: Map
      desc: Documentation content/metadata about using the schema or the governed data object.
      properties:
        _meta:
          acc: [key,text,examples,notes,xref,spec]
        key:
          desc: The keyname of the property to use in documentation.
          type: String
          default: this.parent.key
          docs:
            text: |
              Must be an existing alias (listed in `<prop>.alias`) of the parent property.
              Defaults to the keyname of the parent property, which is typically what you would want representing that property in all documentation.
              This parameter is useful when a non-standard keyname is used in the definition but an alias is actually preferred.  
        text:
          type: Multiline
          desc: A detailed description of the schema or the governed data object.
        examples:
          alias: [exes]
          type: ArrayTable
          properties:
            _meta:
              req: [code]
              acc: [desc,lang]
            code:
              desc: The code example to use in documentation.
              type: LiteralBlock
            desc:
              desc: A description of the code example.
              type: String
            lang:
              desc: The language of the code example.
              type: String
        notes:
          type: ArrayList
          items:
            type: Multiline
          desc: Additional notes about the governed object.
          docs:
            text: |
              Notes are intended to be presented as admonition blocks.
              If the documentation system supports tagging, items in this list will be tagged as `!warning`, `!tip`, and so forth.
            examples:
              - code: |
                  notes:
                    - !tip "Some handy info about using this property."
                desc: |
                  A tip admonition block will be generated in the documentation.
        xref:
          type: Line
          desc: |
            A reference to a topic/section ID elsewhere in the docs.
        spec: &docs_spec_property
          type: String
          desc: Text to be used in a specification for the schema.
          docs:
            text: |
              Used when your schema is to be described by a single-sourced specification.
              This is typically technical language for engineers implementing tooling that will support the schema.

    # The ACTUAL `properties:`/`props:` param/key definition...
    properties: # settings for the LITERAL 1st-tier `properties:` param
      _meta:
        acc: [alias,schema]
      alias: [props] # optionally users can define this block with `props:`
      properties: # now the sub-params of the 1st-tier `properties:` param
        _meta: # the properties field permits _any_ keynames, in addition to _meta
          arb: true
        __meta: # the _meta property's own schema
                # note the actual indentation
          desc: |
            Metadata about a property's contents.
            Only applies to properties containing Maps or Dictionaries.
          type: Dictionary # the `_meta` property is a flat Map of Scalars and Arrays
                          # it contains no nested hashes by design
          alias: [_meta] # the actual preferred property name everyone should use
          docs: {as: '_meta'} # appears in the docs as _meta
          properties:
            _meta:
              acc: [key,idx,acc,res,rej,req,dep,arb,msg,map,doc]
            key:
              desc: For MapTables, the name for referencing the key field
              type: String
              default: slug
            idx:
              desc: For ArrayTables, the property to use as a key identifier for join references from other properties.
              docs: 
                notes:
                  - Only useful for MapTable and ArrayTable types.
              type: String
            req:
              alias: [required]
              type: ArrayList
              desc: |
                  A list of properties that must be present in the governed object.
                  These properties are required for the object to be valid.
              docs:
                notes:
                  - Indicate to your users that this property of the governed object is required.
            acc:
              alias: [accepted]
              type: ArrayList
              desc: |
                  A list of properties that are accepted by the governed object.
                  Appends the local `req` property list.
              docs:
                notes:
                  - Indicate to your users that this property of the governed object is accepted.
                  - If a property is not listed here or in `req`, it is rejected.
            res:
              alias: [reserved]
              type: ArrayList
            rej:
              alias: [rejected]
              type: ArrayList
            dep:
              alias: [deprecated]
              type: ArrayList
              desc: |
                  A list of properties marked for deprecation: still supported and accepted, but designated for removal.
                  These properties are likely to be removed in a future version.
                  
                  Generated documentation flags the listed properties as "`deprecated`" and list them among properties flagged for removal.
              docs:
                notes:
                  - Indicate to your users that this property of the governed object is flagged for removal.
                  - May include aliases of properties which wll remain supported under a preferred keyname.
                examples:
                  - code: |
                      properties:
                        _meta:
                          dep: [propA,prop2]
                        prop1:
                          desc: The main property for this object.
                          alias: [propA]
                        prop2:
                          desc: A secondary property for this object.
                          alias: [propB]
                    desc: |
                      In this example, we indicate to users that `prop1` will remain supported indefinitely, but its alias `propA` should no longer be used for that property.
                      Additionally, `prop2` and its alias `propB` are deprecated and should not be used.
                spec: |
                  This schema property flags properties that are still supported but are designated for removal in a future version.
                  Generated documentation MUST tag or otherwise note these properties as "`deprecated`" and list them among properties flagged for removal.
            arb:
              type: Boolean
              desc: |
                  Whether the governed object can contain arbitrary properties.
                  If `true`, the governed object can contain any properties not explicitly defined in the schema.
              docs:
                notes:
                  - Indicate to your users that this property of the governed object is open-ended.
              default: false
            doc:
              type: ArrayList
              desc: Property keynames in the order they should appear in documentation.
              docs:
                text: |
                  Defaults to the order in which they are listed under `properties` in schema.

                  If this property exists, unlisted properties _will not appear_ in documentation of a given schema definition.
        <prop>: # also pretty meta, to be honest...
          # Writing the key as `<prop>` designates it an arbitrary (open) parameter name
          # Native code must constrain the arbitrary name of this key
          # the <prop> wildcard param's own schema
          type: Map
          desc: The key and defnition of an arbitrarily named property.
          properties:
            _meta:
              acc: [_alias,_desc,_type,_default,_items,_rules,_options,_context,_ui,_docs,_sgyml]
            # These keys are oddly named in order to escape the fact that
            #  they would conflict with themselves at this tier
            #  The acc: key above designates the below-listed keynames for
            #   the permitted properties...
            #  ... Their own alias: param's value corrects for this
            _alias: # sub-schema
              alias: [alias,aliases]
              docs: {key: "alias"}
              <<: *alias_property

            _type: # defining the (data)type: schema param
              alias: [types]
              types: # the type property's own value-types definition
                - type: DataType 
                - type: ArrayList
                  items:
                    type: DataType
                - type: Array
                  items:
                    types:
                      - type: DataType
                      - type: Schema # Allows items in a type/types Array to take the form of schemas
              ui:
                error: Must be the name of a permitted data Type
            
            _default:
              alias: [default]
              desc: The implied value of the property when the property is absent, or else how default values are determined from context.
              types:
              - type: Scalar
                desc: A fixed default value for the property.
              - type: Map
                desc: For complex representation or determination of a property's default value
                properties:
                  _meta:
                    acc: [text,spec,code]
                  text:
                    desc: A casual description of the default value and how it is to be derived.
                  spec:
                    desc: Technical specification for how the default is determined, short of writing actual code.
                  code:
                    desc: Actual code/script used to determine the default value based on context.
                    docs:
                      text: |
                        Functional code may be written in Ruby, Bash, or a templating system, as determined by the <<+schema-meta-functions-language` property or the sibling <<_schema-.

                        Available contextual data includes anything established in <<_schema-meta-functions>>.
                        Additionally, use contextual variables.
                        
                        Defined object variables:

                        `this.property` references property being defined.
                        `this.property.parent` references the parent object.

                        Object attribute variables:

                        `this.property.kind` references the kind (`Scalar` or `Composite`) of the property.
                        `this.property.class` references the class of the property.
                        `this.property.type` references the specific class or variant of the property.
                        `this.property.parent.value` for the value this property's parent property.
                        `this.property.key` for the used keyname of this property (used to detect which alias is being used).
                lang:
            
            _items:
              alias: [items]
              desc: Defines the characteristics of valid Array items.
              type: Map
              context:
                rejected_when: "this.property.class != 'Array'"
              properties:
                _meta:
                  acc: [type,one_of,regexp,has_one,has_many]
                type:
                  alias: [types]
                  desc: Type or types of data accepted as items in the Array.
                  types: [DataType,ArrayList,Schema]
                  items:
                    type: DataType
                one_of:
                  desc: For multi-type Arrays, an annotated listing of the accepted item types.
                  type: Array
                  items:
                    type: Schema # allows 2 or more schemas
                regexp:
                  desc: A regular expression to constrain the items in an ArrayList.
                  type: RegExp
                has_one:
                  desc: The path (internal URIX) to a property name with a 1-to-1 relationship.
                  type: URIX
                  rules:
                    regexp: /^#\//
                  context:
                    rejected_when: "this.property.kind != ArrayList"
                has_many:
                  desc: The path (internal URIX) to a property name with a 1-to-many relationship.
                  type: URIX
                  rules:
                    regexp: /^#\//
                  context:
                    rejected_when: "this.property.type != ArrayList"
              
            _rules:
              alias: [rules]
              type: Map
              properties:
                _meta:
                  acc: [delim,'null',regexp,min,max,multiple,semver]
                delim:
                  type: String
                  default:
                    regexp: /.{1,5}/
                    desc: |
                        A String (typically a single symbol character) that separate values in a given StringList
                    docs:
                      notes:
                        - Only available for StringList-typed properties
                  context:
                    rejected_when: "this.property.type != 'StringList'"
                nullable:
                  type: Boolean
                  default: true
                  desc: Whether this property can be _null_ or _nil_ (have no value).
                    notes: 
                      - If `true`, this property will be _nil_ (have no value) when not set
                      - When this property exists, it can be _empty_; nil represents _nonexistence_
                  ui: {hint: "Set `false` to require this property to exist and have a value"}
                regexp:
                  alias: [pattern]
                  desc: A regular expression pattern that the value must match.
                  type: RegExp
                min:
                  desc: The minimum value for the property.
                  type: Number
                  context:
                    rejected_when: "this.property.class != 'Number'"
                max:
                  desc: The maximum value for the property.
                  type: Number
                  context:
                    rejected_when: "this.property.class != 'Number'"
                inc:
                  desc: The unit of increment.
                  type: String
                  options: [second,minute,hour,day,month,year,timezone,major,minor,patch,x*]
                  docs:
                    text:
                      Any supported unit of increment the property must match.
                      
                      [horizontal]
                      `second`:: Values may vary by no less than a second.
                      `minute`:: Values may vary by no less than a minute.
                      `hour`:: Values may vary by no less than an hour.
                      `day`:: Values may vary by no less than a day.
                      `month`:: Values may vary by no less than a month.
                      `year`:: Values may vary by no less than a year.
                      `timezone`:: Values may vary by no less than a timezone.
                      `major`:: Values may vary by no less than a major version.
                      `minor`:: Values may vary by no less than a minor version.
                      `patch`:: Values may vary by no less than a patch version.
                      `x*`:: Where `*` is a integer, values may vary by multiples of that number.
                  context:
                    accepted_when: "['timestamp,number'].contains this.property.class or this.property.type == 'SemVer'"
                multiple:
                  desc: The number by which the value must be divisible.
                  type: Number
                  context:
                    rejected_when: "this.property.class != 'Number'"
                semver:
                  desc: A Ruby-style SemVer constraint pattern (e.g., `~> 1.0`)
                  type: String
                  context:
                    rejected_when: "this.property.type != 'SemVer'"
                scalars:
                  type: ArrayList
                  desc: List any scalar data type that items in an ArrayList must conform to.
                  context:
                    accepted_when: "this.property.type == 'ArrayList'"
                depth:
                  type: Integer
                  desc: The maximum depth of nesting allowed for a Map property.
                  context:
                    rejected_when: "this.property.class != 'Map'"
            _options:
              alias: [options]
              types: # may contain a List of option values or a MapTable
              - type: ArrayList
                desc: A list of possible values for the property
                items:
                  type: Scalar
              - type: MapTable 
                desc: |
                  An annotated listing of possible values for the property.
                  The keyname of each record in the MapTable stands as a given accepted value of the property.
                properties:
                  name:
                    desc: A label for the option
                    type: String
                  desc:
                    desc: A description of the option
                    type: String
                  docs:
                    desc: A more detailed explanation of the option
                    type: Multiline
                  type:
                    desc: A data type for the option
                    type: DataType
                  akas:
                    desc: |
                      Alternative values that equal the option value.
                      For open-entry systems, these will translate to the same value as the option.  
                    alias: [alias,aliases]
                    type: ArrayList
                    context:
                      uniqueness: greedy
            _context:
              alias: [context]
              docs: {key: "context"}
              type: Map
              properties:
                _meta:
                  acc: [uniqueness,has_one,has_many,belongs_to,required_when,rejected_when,meta]
                uniqueness:
                  desc: How unique this property's value must be across records in a Table.
                  type: String
                  default: none
                  options:
                    local:
                      desc: The value must be unique across all properties in this record, including ArrayList items.
                    greedy:
                      desc: The value must be unique across all records in the Table but may be duplicated in the same record.
                    strict:
                      desc: The value must be unique across all records in the Table and in the same record, including ArrayList items.
                    solo:
                      desc: The property keyname must be unique across all records in the Table.
                    none:
                      desc: The value need not be unique.
                  ui:
                    error: "Value must be one of: local, greedy, solo, none"
                has_one:
                  desc: The paths to properties that this property has a one-to-one relationship with.
                  type: ArrayList
                  docs:
                    examples:
                      - code: "#/block3, #/block4"
                        desc: This property joins the record with one record in ach table uniquely keyed with this property's value.
                has_many:
                  desc: The paths to properties that this property has a one-to-many relationship with.
                  type: ArrayList
                required_when:
                  type: Map
                  desc: Conditions under which this property is required.
                  properties:
                    _meta:
                      acc: [all_of,any_of,one_of,none_of]
                    all_of:
                      desc: If all of these statements resolve true, this property is required.
                      type: ArrayList
                    any_of:
                      desc: If any of these statements resolves true, this property is required.
                      type: ArrayList
                    one_of:
                      desc: If one of these statements resolves true, this property is required.
                      type: ArrayList
                    none_of:
                      desc: If none of these statements resolve true, this property is required.
                      type: ArrayList 
                rejected_when:
                  type: Map
                  desc: Conditions under which this property cannot be present.
                  properties:
                    _meta:
                      acc: [all_of,any_of,one_of,none_of]
                    all_of:
                      desc: If all of these statements resolve true, this property is invalid.
                      type: ArrayList
                    any_of:
                      desc: If any of these statements resolves true, this property is invalid.
                      type: ArrayList
                    one_of:
                      desc: If one of these statements resolves true, this property is invalid.
                      type: ArrayList
                    none_of:
                      desc: If none of these statements resolve true, this property is invalid.
                      type: ArrayList
                meta:
                  type: Map
                  desc: Arbitrary metadata about the context of the property, beyond the governance supported in the schema.
            _ui:
              alias: [ui]
              docs: {key: "ui"}
              type: Map
              properties:
                _meta:
                  acc: [label,hint,error]
                label:
                  type: String
                  regexp: /[\w-\s]{2,30}/
                hint:
                  type: String
                  regexp: /.{6,100}/
                error:
                  type: String
                  regexp: /.{10,300}/m
            _docs:
              alias: [docs]
              docs: {key: "docs"}
              type: Map
              properties:
                _meta:
                  acc: [key,text,examples,notes,xref,spec]
                key:
                  desc: The keyname of the property to use in documentation.
                  type: String       
                text:
                  type: Multiline
                  desc: A detailed description of the governed data object.
                examples:
                  alias: [exes]
                  type: ArrayTable
                  properties:
                    _meta:
                      req: [code]
                      acc: [desc,lang]
                    code:
                      desc: The code example to use in documentation.
                      type: LiteralBlock
                    desc:
                      desc: A description of the code example.
                      type: String
                    lang:
                      desc: The language of the code example.
                      type: String
                notes:
                  type: ArrayList
                  items:
                    type: String
                  desc: Additional notes about the governed data object.
                xref:
                  desc: A reference to a topic or section ID elsewhere in the docs. 
                spec: *docs_spec_property
            _sgyml:
              alias: [sgyml]
              docs: {key: "sgyml"}
              type: Map
              desc: |
                Property-specific and optonally _cascading_ settings for determining how SchemaGraphy should interpret the governed property.
                These settings override document or dataset scoped SGYML instructions. 
              properties:
                _meta:
                  acc: [tagging,templating,refs,]

          dataset: ["*"] # indicates to native schema validator that this is
            #  a ROOT terminator
            # Due to this <prop> wildcard param schema being arbitrary --
            #  that is, designated eligible for nearly open-ended naming of
            #  child params -- this schema gets a `dataset: ["*"]` setting.
            # NOTE:
            # All params that are named with leading underscores (`_<key>`)
            #  are excused from schema governance.
            # There is probably nothing to do here; just noting eligibility.
            #  we have reached the end of the graph spelunk for `<prop>:`,
            #  and thus for `properties:`
      # end of LITERAL `properties:`-defining subschema...
    dataset: # settings for the root-tier `dataset:` property
      desc: Attributes of the subject payload as a whole.
      docs:
        text: |
          This is the block that establishes the subject payload's overall attributes, applicable to ArrayTable and MapTable object types.
          
          This block is NOT the dataset Array.
          
          The actual subject (governed property) is called a "`payload`"
          If stored in the same source document, this payload is:
          
          . indicated by the `$payload:` that is sibling to the `$schema:` key, or
          . divided from the `$schema` object by a `---` delimiter.

      alias: [recordset]
      type: Map
      properties:
        _meta:
          acc: [max,fix,lock,skip,sort,sources,sgyml]
        max:
          type: Integer
          desc: The maximum number of records the dataset (payload) can contain
          context:
            rejected_when: "this.dataset.type != 'ArrayTable' and this.dataset.type != 'MapTable'"
        min:
          type: Integer
          desc: The minimum number of records the dataset (payload) must contain
          context:
            rejected_when: "this.dataset.type != 'ArrayTable' and this.dataset.type != 'MapTable'"
        fix:
          type: ArrayList
          desc: IDs of records that may not be coerced
          context:
            rejected_when: "this.dataset.type != 'ArrayTable' and this.dataset.type != 'MapTable'"
        lock:
          type: ArrayList
          desc: IDs of records to indicate should be “frozen” from editing
          context:
            rejected_when: "this.dataset.type != 'ArrayTable' and this.dataset.type != 'MapTable'"
        skip:
          type: ArrayList
          desc: IDs of records to excuse from schema governance
          context:
            rejected_when: "this.dataset.type != 'ArrayTable' and this.dataset.type != 'MapTable'"
        sort:
          type: ArrayList
          desc: A series of sort criteria for the dataset
          context:
            rejected_when: "this.dataset.type != 'ArrayTable' and this.dataset.type != 'MapTable'"
        sgyml:
          type: Map
          desc: Rules to apply to the governed data object in its pre-parsed state.
          properties:
            _meta: 
              acc: [tagging,templating,mapping,prepping,subs,refs,_ref,_refs]
            tags:
              type: MapTable
              desc:  |
                Custom YAML tags and their interpretation.
                Tags are expressed in the governed document as `!!<tagname>`, where `<tagname>` is an valid YAML custom tag name.
              properties:
                <tagname>:
                  key:
                    regexp: /^\<?[a-z0-9_\-]{2,20}>?$/
                  properties:
                    _meta: [desc,spec,code,lang]
                    desc:
                      desc: A plain-English description of the tag's purpose.
                    spec:
                      desc: A technical description of the tag's purpose.
                    code:
                      desc: Functional code for interpreting the tag.
                    lang:
                      desc: Optional specifier of the language to use for processing `code`, when different than <<_schema-meta-functions-language>>.
                      type: String
                      default:
                        code: "$schema.meta.functions.language"
            templates:
              desc: |
                How to handle templated fields.
            maps:
              desc: The structure of mapping to enforce in the governed data object.
              type: String
              options: [nested,foldable,flat]
              default: nested
              docs:
                text: |
                  The structure of mapping to enforce in the governed data.

                  * `nested` indicates a normal YAML structure, with sub-properties indented.
                  * `foldable` indicates a structure that can be folded, with sub-properties indented or delimited with `_` characters.
                  * `flat` indicates a flat structure, with sub-properties at the same level as the parent.
            subs:
              type: ArrayList
              options: [alias,sassy,$ref,$refs]
              default: [alias,$ref,$refs]
              desc: Substitution and dereferencing policy for the governed data document.
              docs:
                text: |
                  Specifies which, if any, forms of variable substitution and dereferencing to validate and parse in the governed data.

                  * `alias` is the native YAML substitution method, with `&anchors` and `*aliases` syntax.
                  * `sassy` is the Sass-like substitution method, with `${var}` syntax. (Not Yet Implemented)
                  
                  Use an empty Array (`[]`) to disable substitutions in the governed data.
                notes:
                  - Lets your users know what dynamic elements they may use in their YAML files.
            refs:
              type: Map
              desc: Metadata about the `$ref`/`$refs` properties.
              docs:
                text: |
                  These settings set the defaults for the `$ref` and `$refs` properties in the governed data object.

                  If you wish to treat `$ref` and `$refs` differently, use the sibling `_ref` and `_refs` properties.
                  There you can set different minimum and maximum constraints for each.

                  Settings like `total` and `depth` are cumulative across/through all `$ref` and `$refs` properties in the governed object.

                  Settings like `formats` and `circular` apply to all `$ref` and `$refs` properties in the governed object and cannot be specifically set for the different properties.
              properties: &refs_properties
                depth:
                  type: Integer
                  default: 5
                  desc: Total nesting depth of document references.
                circular:
                  type: Boolean
                  default: false
                  desc: Whether to allow circular (looping) references.
                total:
                  type: Integer
                  default: 1000
                  desc: The total number of `$ref`/`$refs` properties that can appear in the governed data object.
                formats:
                  type: ArrayList
                  default: [json,yaml]
                  options: [json,yaml]
                  desc: Which data formats are permitted.
                max:
                  type: Integer
                  default: 10
                  desc: The number of references that can appear in one `$ref`/`$refs` param.
                min:
                  type: Integer
                  default: 1
                  desc: Minimum number of references permitted in one `$ref`/`$refs` param.
                  docs:
                    notes:
                      - Use `1` for a single URI/Map or >`1` for an ArrayList/ArrayTable of URIs/Maps.
            _ref:
              type: Map
              desc: Metadata about the `$ref` property specifically.
              properties:
                max:
                  type: Integer
                  default: 10
                  desc: The number of references that can appear in one `$ref` param.
                min:
                  type: Integer
                  default: 1
                  desc: Minimum number of references permitted (Uri/Map vs Array/ArrayTable).
            _refs:
              type: Map
              desc: Metadata about the `$refs` property specifically.
              docs:
                notes:
                  - "To disable `$refs` entirely, set `max: 0` for this property."
              properties:
                max:
                  type: Integer
                  default: 10
                  desc: The number of references that can appear in one `$ref` param.
                min:
                  type: Integer
                  default: 1
                  desc: Minimum number of references permitted (Uri/Map vs Array/ArrayTable).