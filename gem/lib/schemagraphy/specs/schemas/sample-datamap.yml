# Example of a YAML data file that maps other data files into a coherent YAML object
# When associated with a SchemaGraph, it must validate
# Otherwise it is just a top-level YAML object with some $ref pointers

# docset-manifest.yml
#
# Example of the object
# topics:
#   - slug: some-article
#     title: Some Article Title
#     type: essay
#     desc: This is a description of the article
# 
# categories:
#   - slug: some-category
#     name: Some Category Title
#   - slug: another-category
#     name: Another Category Title

# Example schema for the above
# $schema:
#   type: Map
#   properties:
#     _meta:
#       req: [topics, categories]
#     topics:
#       $schema:
#         type: ArrayTable
#         properties:
#           _meta:
#             req: [slug, title]
#             acc: [type, desc, vols]
#           slug:
#             value: {type: Slug}
#           title:
#             value: {type: String}
#           type:
#             value:
#               default: article
#               type: String
#           desc:
#             value:
#               type: String
#               min: 12
#           vols:
#             context:
#               has_one: volume
#     categories:
#       $schema:
#         type: ArrayTable
#         properties:
#           _meta:
#             req: [slug, name]
#           slug:
#             value: {type: Slug}
#           name:
#             value: {type: String}
#
#     volumes:
#       $schema:
#         type: ArrayTable
#         properties:
#           _meta:
#             req: [slug, name, path]
#             acc: [desc]
#          slug:
#            value: {type: Slug}
#          name:
#            value: {type: String}
#          path:
#            value: {type: Path}
#          desc:
#            value: {type: String}
#            min: 12

# docset-datamap.yml
# actual YAML to map the above object across multiple files, including
#   topics.a-m.yml
#   topics.n-z.yml
#   categories.yml
#   volumes.yml

topics:
  $ref: 
    - topics.a-m.yml
    - topics.n-z.yml
categories:
  $ref: categories.yml
volumes:
  $ref: volumes.yml
