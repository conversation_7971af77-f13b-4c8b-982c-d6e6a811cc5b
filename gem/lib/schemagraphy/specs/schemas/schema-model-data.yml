# The contents of this file are DOCUMENTATION of the data schema model.
# The next line and it's `end::mode[]  follow-up are markers for AsciiDoc `include::[]` directives
# tag::model[]
$schema:            # <1> Always required at schema root, implied for nested schemas with `type` or `types` root property.
  anchors:          # (Map) A series of arbitrarily named YAML anchors to reference in the schema
    a_name: &a_name 'Value of a_name' # example of a YAML anchor
  meta:             # (Map) Metadata for the schema definition itself
    spec:           (URI) A URI to the schema specification (https://schemagraphy.docops.org/specs/V1/schema-root-data.yaml)
    version:        # Map
      code:         (SemVer) The version of the schema
      date:         (Date) The date the schema was last updated
    sgyml:          # (Map) A series of rules to apply to parsing the schema itself
      # THESE PROPERTIES DO NOT APPLY TO THE SUBJECT DATA - SEE $schema.properties._meta.sgyml
      maps:         String (nested, foldable, or flat) The structure of mapping to enforce (NYI)
      subs:         (ArrayList) Types of YAML and other substitution (alias*, sassy, none) allowed in the schema objects (sassy NYI)
      refs:         (String) Resolution policy for `$ref`/`$refs` properties in the schema (remote, local)
      _ref:         # Metadata about the `$ref` property
        max:        (Integer) The number of references that can appear in one `$ref` param
        min:        (Integer) Minimum number of references permitted (Uri vs Array)
      _refs:        # Metadata about the `$refs` property
        max:        (Integer) The number of references that can appear in one `$refs` param
        min:        (Integer) Minimum number of references permitted
    functions:      # (Map) How to process fnc literals in the schema
      lang:         (String) The language of the functions used in the schema definition
                    # Defaults to Ruby but permits Liquid, Bash, and ERB in all cases
      env:          (LiteralBlock) Environmental commands to pass to all such functions
      vars:         (Map) Variables to pass to all such functions
                    # By default, the local property's populated context is made available up to the next $schema key
  $include:         # <2> (Dictionary of ArrayTables, ArrayTable) Paths to external schema templates
    all_of:         # (ArrayList or ArrayTable) A listing of combinable schema templates the governed data must be validated against and parsed by
      - source:     (Uri) A URI to the external schema template
        vars:       (Map) Variables to pass to the external schema template # see $schema.vars below, only per-source template
        lang:       (String) The template engine/syntax used in the external schema template
    any_of:         (ArrayList or ArrayTable) A listing of schema templates _any one_ of which the governed data must match (defaults parsed in order)
    one_of:         (ArrayList or ArrayTable) A listing of schema templates _exactly one_ of which the governed data should match
    strategy:       (String) Import policy type; defaults to `BOTH` (all imported schemas AND present schema must validate); use `EITHER` to require only one validation
  
  # The above properties require a $schema tag, even if the schema is not the root of the document (property subschemas)
  # If none of the above properties is needed, the $schema parent property is not required and any of the
  #  following properties may be used directly under a property key to further define the property

  type:             (DataType) The data type of the object governed by this schema block
  types:            # (Array, ArrayTable) For embedding multiple schemas
    - type:         # ($schema) One schema per type; type keyword implies $schema, 
                    #  so $schema content only required here
  alias:            (String or ArrayList) One or more alternate names for the property
  desc:             (Multiline) A description of this schema and the objects it governs
  docs:             # (Map) Special technical information about the schema/object
    key:            (String) The keyname to use in the documentation for this schema/property
    text:           (Multiline) Specific technical information about the schema
    examples:       # (ArrayTable) A series of examples of valid examples for this schema
      - code:       (LiteralBlock) A code snippet of a valid example
        desc:       (String) A description or caption for the example (optional)
        lang:       (String) The language of the code snippet (defaults to `yaml`)
    notes:          (Array of Strings) Additional notices about the schema (tag with admonition type, e.g., !!warning)
    xref:           (Slug) A valid cross-reference to a documentation topic that expands on or relates to this schema
  properties:       # <4>
    _meta:          # <5>
      key: |        
            (String) For MapTables, the name for referencing the key field (defaults to `slug`); for ArrayTables, the field to constrain as unique across all records
      idx:          (ArrayList) The property keys to use as identifier fields for the local Map (for external joins)
      arb:          (Boolean) Whether properties may be arbitrarily named
      acc:          (ArrayList) A series of _accepted_ property keynames
      req:          (ArrayList) A series of _required_ property keynames
      res:          (ArrayList) A series of _reserved_ property keynames
      rej:          (ArrayList) A series of _rejected_ property keynames
      dep:          (ArrayList) A series of _deprecated_ property keynames
      msg:          (String) A UI message explaining these (_meta) constraints
      map:          (String) The structure of mapping to enforce (nested*, foldable, or flat) (NYI!)
      doc:          (ArrayList) Property keynames in the order they should appear in documentation
      sgyml: &sgyml # Rules that apply to a tier of properties and their descendants
        tagging:    # (Map) How to handle YAML tags in the cascade
          allowed:  (Boolean) whether YAML tags are permitted in the cascade
          tags:     # (Map) Custom tags and their interpretation
            <tag>:  # (Map) he custom tag name
              desc: (String) What this tag is for
              spec: (String) Technical instructions for interpreting this tag
              code: (LiteralBlock) Code for interpreting this tag
              lang: (String) Language for interpreting this tag
        temlating:  # (Map) How templated fields are handled, if alowed
          policy:   (String) Policy for determining how to apply template parsing
          default:  (String) language/engine - 'liquid' or 'erb'
          stage:    (String) 'preload' or 'postload'
          scopes:   (ArrayList) The names of defined scopes of variables
          vars:     (Map) The variables to pass to templates
          
    <prop>:         # <6> (arbitrary property key)
                    # The following properties apply to Scalar and ArrayList properties
                    # Maps and ArrayTables get their own subschema in this space (though $schema key not required)
      key:          # constraints for arbitrary keynames
        type:       (DataType) The data type of the property KEY String (Slug, SnakeCase, CamelCase, etc)
        regexp:     (RegExp) A regular expression to constrain the required keyname
        akas:       (ArrayList) Alternative property keynames; unique across all property keys and aliases in object
      sgyml:        # (Map)  Property-level version of the recurring sgyml block
        tagging:    # (Map) How to handle YAML tags for this property and its descendents
          allowed:  # whether YAML tags are permitted in the cascade    
          tags:
            <tagid>:
        templating: # (Map) For individual templated fields, how to handle values
          which:    # (String) 'all', 'none', 'tagged', 'detect'
          stage:    # (String) 'preload' or 'postload'

          default:  # (String) 'liquid' or 'erb'
          scopes :  # (ArrayList) The names of defined scopes of variables
          vars:     # (Map) The variables to pass to templates
      type:         (DataType, ArrayList) The property's permitted data type/s
      desc:         (Multiline) A description of the property
      docs:         # <7>
        key:        (String) The keyname to use in the documentation for this property
        text:       (String) A description of how this property is to be used
        dont:       (String) A description of how this property is NOT to be used
        notes:      (ArrayList) Additional notes about the property (tag with admonition type, e.g., !!warning
        examples:   # (ArrayTable) A series of examples of valid examples for this property
          - code:   (LiteralBlock) A code snippet of a valid example
            desc:   (String) A description or caption for the example
            lang:   (String) The language of the code snippet (defaults to `yaml`)
      default:      # Scalar value or properties to determine dynamically <8>
        text:       (Multiline) Plain-English explanation of how the default is determined
        spec:       (Multiline) Technical logic for determining the default
        code:       (LiteralBlock) Functional code for establishing the default based on the context
        lang:       (String) The functional language for interpreting sibling `code` property
      items:        # (Map) Details about the scalar items in an ArrayList
        type:       (DataType, Array of DataType Strings or Map Schemas)
        one_of:     (Array) The types and optionally sub-schemas an item must match
        regexp:     (RegExp) A regular expression to constrain the items in the ArrayList
        has_one:    (Slug) The `<obj>.<key>` reference of a property name with a 1-to-1 relationship
        has_many:   (Slug) The `<obj>.<key>` reference of a property name with a 1-to-many relationship
      options:      # (ArrayList or MapTable) Directory of permitted values for this property
        <key>:      # (KeyNameSGYML) Replace <key> with actual keys when instantiating
          name:     (String) A label for the option
          desc:     (String) A description of the option
          docs:     (Multiline) A more detailed explanation of the option
          type:     (DataType) A data type for the option
          akas:     (ArrayList) Alternative entries that equal the listed option, greedy across all options entries
      rules:        # (Map)
        # Rules applicable to properties of ALL types
        nullable:   (Boolean) Mark `true` if the value can be left out or set to `null`, `false` if not (default `true`)
        # Rules applicable to Strings
        regexp:     (RegExp) A regular expression to constrain the value
        minlines:   (Integer) The minimum number of lines in a multi-line String
        maxlines:   (Integer) The maximum number of lines in a multi-line String
        minchars:   (Integer) The minimum number of characters in a String
        maxchars:   (Integer) The maximum number of characters in a String
        # Range and increment Rules applicable to numbers, versions, and timestamps
        min:        (Number, SemVer, DateTime) The minimum length of a String or Array (Integer); value of a Float or Integer (Number); or SemVer
        max:        (Number, SemVer, DateTime) The maximum length of a String or Array (Integer); value of a Float or Integer (Number); or SemVer
        inc:        (String) The unit of increment (e.g., 'second', 'minute', 'hour', 'day', 'month', 'year', 'timezone', 'x1', 'x5', 'major', 'minor', 'patch')
        # Rules applicable to Numbers
        multiple:   (Number) A number by which the value must be divisible
        # Rules applicable to StringLists
        delim:      (String) A delimiter (character pattern) for splitting a string into an array
        # Rules applicable to SemVers
        semver:     (String) A Ruby-style SemVer constraint pattern (e.g., `~> 1.0`)
        # Rules applicable to ArrayLists
        scalars:    (ArrayList) Types of scalar object all items must be
        nesting:    (Integer) The maximum depth of nesting allowed in a Map
      context:      # <9>
        uniqueness: (String) Unique across instances of this property throughout the local Table (unique, greedy, none)
        required_when: # (String, Array, or Map) Conditions under which this property is required
          any_of:   (ArrayList) Any of these conditions must be met for the property to be required
          all_of:   (ArrayList) All of these conditions must be met for the property to be required
          one_of:   (ArrayList) Only one of these conditions must be met for the property to be required
          none_of:  (ArrayList) None of these conditions can be met for the property to be required
        rejected_when: # (String, Array, or Map) Conditions under which this property is rejected
          any_of:   (ArrayList) Any of these conditions must be met for the property to be rejected
          all_of:   (ArrayList) All of these conditions must be met for the property to be rejected
          one_of:   (ArrayList) Only one of these conditions must be met for the property to be rejected
          none_of:  (ArrayList) None of these conditions can be met for the property to be rejected
        has_one:    (String) The `<obj>.<key>` reference of a property name with a 1-to-1 relationship
        has_many:   (String) The `<obj>.<key>` reference of a property name with a 1-to-many relationship
        meta:       (Map) Arbitrary metadata about the conditions under which the property is applied
                    # Include anything like timestamps, author IDs, tags, etc. for a given property
      ui:           # <10>
        label:      (String) A formal, user-facing label; usually the property's id or key field, title-cased
        hint:       (String) Something to help the user figure out what this field is for
        error:      (String) A custom error message to report invalid entry for the defined parameter.
      docs:
        nodocs:     (Boolean) Whether to exclude this property from the documentation
        key:        (String)) Keyname to use in the documentation.
        text:       (Multiline) AsciiDoc-formatted description of the property
        examples:   # (ArrayTable) A series of examples of valid examples for this property
          - code:   (LiteralBlock) A code snippet of a valid example
            desc:   (String) A description or caption for the example
      $schema:      # <11>
  dataset:          # <12>
    max:            (Integer) The maximum number of records the dataset (payload) can contain
    min:            (Integer) The minimum number of records the dataset (payload) must contain
    fix:            (ArrayList) IDs of records that may not be coerced
    lock:           (ArrayList) IDs of records to indicate should be “frozen” from editing
    skip:           (ArrayList) IDs of records to excuse from schema governance
    sort:           (ArrayList) A series of sort criteria for the raw (source) dataset
    sources:        # (ArrayList) A series of sources from which the governed data is drawn
                    # options are: key, key_desc, <prop>, <prop>_desc, <prop>/<prop>, <prop>/<prop>_desc
    sgyml:          # <13>
      tags:         # (Map) Custom tags and their interpretation
        <tagname>:  # The custom tag
          desc:     (string) What this tag is for
          spec:     (String) Technical instructions for interpreting this tag
          code:     (Multiline) Code for interpreting this tag
          lang:     (String) Language for interpreting this tag
      maps:         (String) (nested, foldable, or flat) The structure of mapping to enforce (NYI)
      subs:         (ArrayList) Types of YAML and other substitution (alias*, sassy, none) allowed in the governed objects (sassy NYI)
      merge:        (Boolean) Whether to honor the YAML 1.1 merge key (true by default for all SGYML objects)
      templates:    # How to handle properties designated as 'templated fields'
        which:      # (String) 'all', 'none', 'tagged', 'detect'
        default:    # (String) 'liquid' or 'erb'
        context:    # (Map) The context in which to evaluate template
        vars:       # (Map) The variables to pass to templates
    combine:        # (Map) Data to concatenate with the governed payload
      prepend:      (Uri, Map, ArrayTable) data to require be prepended to the governed payload
      append:       (Uri, Map, ArrayTable) data to require be appended to the governed payload
                    # Above features are same as adding $prepend, $append, $ref, or $refs to the payload
      refs:         # Metadata about the $ref property
        depth:      (Integer) Total nesting depth of document references
        formats:    (ArrayList) Which data formats are permitted (json, yaml)
        total:      (Integer) The total number of `$ref`/`$refs` properties that can appear in the document
        circular:   (Boolean) Whether circular references are permitted
        max:        (Integer) The number of references that can appear in one `$ref`/`$refs` param
        min:        (Integer) Minimum number of references permitted (Uri vs Array)
        resolve:    (String) Default policy for external references (resolve in `remote` or `local` context)
      _ref:         # Metadata about the $ref property
        max:        (Integer) The number of references that can appear in one `$ref` param
        min:        (Integer) Minimum number of references permitted (Uri vs Array)
      _refs:        # Metadata about the $refs property
        max:        (Integer) The number of references that can appear in one `$refs` param
        min:        (Integer) Minimum number of references permitted
# --- YAML base sequence separator
$payload: # The object governed, key `$payload` only needed if data file's root object is a Map of {$schema: ..., $payload:}
# No need for `$payload` key if base sequence separator used
# When payload is inluded, dynamic refs can be used to embed data from other files
# $ref: (Uri or Map) data to require be embedded here
# end::model[]

# tag::block-defs[]
blocks:
  1:
    key: $schema
    txt: |
      Every schema definition file, whether complete or partial, must have a `schema:` block.
      For schemas coded into the governed-object document, this block can go on the bottom, after the `$payload:` block.
      Unless otherwise noted, property arrangement/order is just a matter of preference or convention.

  2:
    key: "$schema.$include"
    txt: |
      Optionally reference an external schema template to parse and “include” at this point.
      The referenced schema's data will pass to the current schema in which it is newly “embedded”.
      This “cascading” data will be overwritten by any inline schema settings entered subsequent to the embed point.
      See <<schema-data-import>>.

  3:
    key: "$schema.$include.vars"
    txt: |
      Schemas can be dynamic templates containing Liquid markup.
      If this is the case, you may wish to pass variables in for parsing.
      They will be accessible by the imported schema template as Liquid variables under the `vars.` scope (`vars.<key>`).

  4:
    key: schema.properties
    txt: |
      Everything contained in `properties:` refers to the contents of a data record -- a single Map, whether alone or in a "`Table`".
      If the defined object is an ArrayTable or MapTable, the schema will apply equally to all contained records except as defined in the `dataset:` section, a peer of `properties:`.

  5:
    key: $schema.properties._meta
    txt: |
      Schema settings that apply to all properties in governed objects.
      (The leading underscore [`_`] character indicates the key is not a property name like its siblings, which follow.)

  6:
    key: $schema.properties.<prop>
    txt: |
      Named using the literal keyname of the parameter to which it applies in the dataset.
      Add up to as many properties as your governed data object could have parameters within it (or within each record of a Table).
      All of the parameters in each `<prop>:` block define that property across all records in any governed dataset (unless any are explicitly excepted under `schema.dataset.skip`).

  7:
    key: $schema.properties.<prop>.usage
    txt: |
      This block contains technical information about the purpose of the property.
      One use might be indicating to reviewers how to evaluate the value of the field.

  8:
    key: $schema.properties.<prop>.value
    txt: |
      These parameters constrain or impose upon values of instances of the property in a governed object.

      Value constraints in the form of a Regular Expression pattern that must be matched, or a list of options.
      A RegEx pattern of `r/option-1|option-2/` is effectively the same as using the `options` property with a simple list of scalars.
      Using a ArrayTable in the `options` property enables additional metadata, such as a label version of the option.
      However, at this point, we might be better off referencing an external object rather than nesting a new ArrayTable here (see `schema.properties.<prop>.context` below and <<schema-data-properties-context>>).

  9:
    key: $schema.properties.<prop>.context
    txt: |
      Data object properties can reference (point to) one another by noting the `<key>` property of another data record.
      If the currently defined property (`<prop>`) is one such field, it is helpful to define the relationship here.
      This will help populate UI entry options and prepare queries to merge external data with primary records on retrieval.
      See <<schema-property-context>> and <<graphql-queries>> for more.

      The other contextual attribute of properties is their uniqueness.
      A property's values can be enforced to be unique among values of this property across sibling records in the dataset (when ArrayTable or MapTable).
      See <<schema-data-property-uniqueness>> for ways the `uniqueness` property can prevent data collisions.

  10:
    key: $schema.properties.<prop>.ui
    txt: |
      The `ui:` block details what might be reflected of this property in user interfaces.
      These interfaces may be boilerplate datafiles or else CLI prompts or contextualized/inline form-fill help.

  11:
    key: $schema.properties.<prop>.$schema
    txt: |
      If this property contains a Map, Dictionary, MapTable, or ArrayTable, you can schematize that object here, though the `$schema` key is only required if certain keys (`$import`, `meta`, `anchors`) are to be used.
      New Maps and Arrays can be nested recursively this way, to an untested depth, and they may use explicit schema definitions or a reference to an external schema (which risks infinite loop recursion).
      Child objects can each be schematized or not.
      Child schemas _do_ effectively become part of the parent Schema for a given instance in the case of stored schemas (the only kind for which it matters).

  12:
    key: $schema.dataset
    txt: |
      Properties in the `dataset` block either (1) apply to the whole recordset (`$payload`) governed by this schema or (2) apply to a subset of records in the payload.
      In the case of Map object types, the "`recordset`" is just the Map itself.
  
  13:
    key: $schema.dataset.sgyml
    txt: |
      Settings around dynamic properties available in the original document of a dataset, where non-standard features are supported by SchemaGraphy.
      This is where to configure the behavior of pre-load parsing of the source document, using _pseudo properties_ standard to SchemaGraphy or provided by extensions.

      These are global settings that can be overridden at the property level with your schema using the `$schema.properties.<prop>.sgyml` Map.

# end::block-defs[]