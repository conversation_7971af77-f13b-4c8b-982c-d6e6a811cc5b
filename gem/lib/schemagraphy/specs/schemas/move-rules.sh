#!/bin/bash

input="data-types.yml"
output="native/rules.yml"

# Read the file into an array
readarray -t lines < "$input"

# Prepare to capture and manipulate data
declare -A type_to_rules
declare -a output_lines
current_type=""
current_indent=""
capture_rules=0
rule_start=0

# Process each line
for i in "${!lines[@]}"; do
    line="${lines[$i]}"

    if [[ "$line" =~ ^([[:space:]]*)-[\ ]type:[\ ](.*)$ ]]; then
        # Found a type line, capture its value and indent
        current_type="${BASH_REMATCH[2]}"
        current_indent="${BASH_REMATCH[1]}"
        output_lines+=("$line")
        capture_rules=0  # Reset capturing rules
    elif [[ "$line" == "$current_indent"rules:* && "$current_type" != "" ]]; then
        # Found rules at the same indent level as the type
        capture_rules=1
        rule_start=$i
        output_lines+=("${BASH_REMATCH[1]}rules:")
        output_lines+=("${BASH_REMATCH[1]}  $ref: './native/rules.yml#$current_type'")
    elif [[ "$capture_rules" == 1 && "$line" =~ ^([[:space:]]+)ruby:[\ ]*$ ]]; then
        # Start capturing ruby block
        ruby_indent="${BASH_REMATCH[1]}"
        continue  # Skip the 'ruby:' line
    elif [[ "$capture_rules" == 1 && "$line" =~ ^"$ruby_indent"(.*)$ ]]; then
        # Capture ruby block content at the correct indent
        type_to_rules["$current_type"]+="${BASH_REMATCH[1]}"$'\n'
    elif [[ "$capture_rules" == 1 && ! "$line" =~ ^"$ruby_indent" ]]; then
        # End of ruby block due to outdent
        capture_rules=0
    else
        # Add other lines to the output as they are
        output_lines+=("$line")
    fi
done

# Write the modified content back to the original file
printf "%s\n" "${output_lines[@]}" > "$input"

# Write the rules content to the separate file
for key in "${!type_to_rules[@]}"; do
    echo "$key:" >> "$output"
    echo "${type_to_rules[$key]}" >> "$output"
done
