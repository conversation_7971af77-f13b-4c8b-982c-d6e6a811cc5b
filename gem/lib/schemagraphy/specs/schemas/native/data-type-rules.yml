# These are the Ruby language rules for SGYML data types
String:
  class: String
  detect: |
    datum.class == String
  insist: |
    datum.class == String
  tests:
    rspec:
      - name: detects a String
        code: 
          - "expect('a string').to be_a(StringType)"
          - "expect('a\n\nb').to be_a(StringType)"
          - "expect('2013-12-12').to be_a(StringType)"
      - name: valid String type
        code:
          - "expect('a string').to be_a(StringType)"
          - "expect('ac ff').to be_a(StringType)"
          - "expect(123).to_not be_a(StringType)"
          - "expect('a\nb\nc').to be_a(StringType)"
Line:
  class: LineType
  detect: |
    datum.class == String and !datum.include?('\n') and !datum.include?('\r')
  insist: |
    datum.class == String
  tests:
    rspec:
      - name: detects a single-line String
        code: "expect('a string').to be_a(LineType)"
      - name: valid Line type
        code:
          - "expect('a string').to be_a(LineType)"
          - "expect('ac ff').to be_a(StringType)"
      - name: invalid Line type
        code:
          - "expect('a\nb\nc').to_not be_a(LineType)"
          - "expect('2013-12-12').to_not be_a(LineType)"
          - "expect(123).to_not be_a(LineType)"
MultiLine:
  class: MultilineType
  detect: |
    datum.class == String and ( datum.include?('\n') or datum.include?('\r') )
  insist: |
    datum.class == String
  tests:
    rspec:
      - name: detects a multi-line String
        code: "expect('a\nb\nc').to be_a(MultilineType)"
      - name: valid Multiline type
        code:
          - "expect('a\nb\nc').to be_a(MultilineType)"
          - "expect('ac ff').to be_a(StringType)"
      - name: invalid Multiline type
        code:
          - "expect('a string').to_not be_a(MultilineType)"
          - "expect('2013-12-12').to_not be_a(MultilineType)"
          - "expect(123).to_not be_a(MultilineType)"
DataType:
  class: DataTypeType
  detect: |
    datum.class == String and DATA_TYPES.include?(datum)
  insist: |
    datum.class == String and DATA_TYPES.include?(datum)
  tests:
    rspec:
      - name: detects a DataType
        code: 
          - "expect('String').to be_a(DataTypeType)"
          - "expect('ArrayTable').to be_a(DataTypeType)"
      - name: valid DataType type
        code:
          - "expect('String').to be_a(DataTypeType)"
          - "expect('Line').to be_a(DataTypeType)"
          - "expect('Multiline').to be_a(DataTypeType)"
      - name: invalid DataType type
        code:
          - "expect('a string').to_not be_a(DataTypeType)"
          - "expect('2013-12-12').to_not be_a(DataTypeType)"
          - "expect(123).to_not be_a(DataTypeType)"
          - "expect('ac ff').to_not be_a(DataTypeType)"
          - "expect('a\nb\nc').to_not be_a(DataTypeType)"
          - "expect('a\n\nb').to_not be_a(DataTypeType)"
          - "expect('2013-12-12').to_not be_a(DataTypeType)"
          - "expect(123).to_not be_a(DataTypeType)"
ScalarDataType:
  class: DataTypeScalarType
  detect: |
    datum.class == String and SCALAR_DATA_TYPES.include?(datum)
  insist: |
    datum.class == String and SCALAR_DATA_TYPES.include?(datum)
  tests:
    rspec:
      - name: detects a ScalarDataType
        code: 
          - "expect('String').to be_a(DataTypeScalarType)"
          - "expect('Integer').to be_a(DataTypeScalarType)"
      - name: valid ScalarDataType type
        code:
          - "expect('String').to be_a(DataTypeScalarType)"
          - "expect('Integer').to be_a(DataTypeScalarType)"
          - "expect('Float').to be_a(DataTypeScalarType)"
          - "expect('Boolean').to be_a(DataTypeScalarType)"
      - name: invalid ScalarDataType type
        code:
          - "expect('a string').to_not be_a(DataTypeScalarType)"
          - "expect('2013-12-12').to_not be_a(DataTypeScalarType)"
          - "expect(123).to_not be_a(DataTypeScalarType)"
          - "expect('ac ff').to_not be_a(DataTypeScalarType)"
          - "expect('a\nb\nc').to_not be_a(DataTypeScalarType)"
          - "expect('a\n\nb').to_not be_a(DataTypeScalarType)"
          - "expect('2013-12-12').to_not be_a(DataTypeScalarType)"
          - "expect(123).to_not be_a(DataTypeScalarType)"
CompositeDataType:
  class: DataTypeCompositeType
  detect: |
    datum.class == String and COMPOSITE_DATA_TYPES.include?(datum)
  insist: |
    datum.class == String and COMPOSITE_DATA_TYPES.include?(datum)
  tests:
    rspec:
      - name: detects a CompositeDataType
        code: 
          - "expect('ArrayTable').to be_a(DataTypeCompositeType)"
          - "expect('HashTable').to be_a(DataTypeCompositeType)"
      - name: valid CompositeDataType type
        code:
          - "expect('ArrayTable').to be_a(DataTypeCompositeType)"
          - "expect('HashTable').to be_a(DataTypeCompositeType)"
      - name: invalid CompositeDataType type
        code:
          - "expect('a string').to_not be_a(DataTypeCompositeType)"
          - "expect('2013-12-12').to_not be_a(DataTypeCompositeType)"
          - "expect(123).to_not be_a(DataTypeCompositeType)"
          - "expect('ac ff').to_not be_a(DataTypeCompositeType)"
          - "expect('a\nb\nc').to_not be_a(DataTypeCompositeType)"
          - "expect('a\n\nb').to_not be_a(DataTypeCompositeType)"
          - "expect('2013-12-12').to_not be_a(DataTypeCompositeType)"
          - "expect(123).to_not be_a(DataTypeCompositeType)"
SemVer:
  class: "Semantic::Version"
  detect: |
    datum.class == Semantic::Version or (datum.class == String and datum.is_version? and datum =~ Semantic::Version::SemVerRegex)
  insist: |
    datum.class == Semantic::Version or (datum.class == String and datum.is_version?)
  import: |
    datum.to_version if datum.class == String
  export: |
    datum.to_s if datum.class == Semantic::Version
  tests:
    rspec:
      - name: detects a Semantic Version
        code: 
          - "expect('2.0.1').to be_a(SemVerType)"
          - "expect('2.0.1').to be_a(SemVerType)"
      - name: valid Semantic Version type
        code:
          - "expect('2.0.1').to be_a(SemVerType)"
          - "expect('2.0.1-alpha.codename').to be_a(SemVerType)"
      - name: invalid Semantic Version type
        code:
          - "expect('1.0').to_not be_a(SemVerType)"
          - "expect('a string').to_not be_a(SemVerType)"
          - "expect('2013-12-12').to_not be_a(SemVerType)"
          - "expect(123).to_not be_a(SemVerType)"
          - "expect('ac ff').to_not be_a(SemVerType)"
          - "expect('a\nb\nc').to_not be_a(SemVerType)"
          - "expect('a\n\nb').to_not be_a(SemVerType)"
          - "expect('2013-12-12').to_not be_a(SemVerType)"
          - "expect(123).to_not be_a(SemVerType)"
HostName:
  class: HostnameType
  regexp: |
    /^([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])(\.([a-zA-Z0-9]|[a-zA-Z0-9][a-zA-Z0-9\-]{0,61}[a-zA-Z0-9]))*$/
  detect: |
    datum.class == String and datum =~ HostNameType::Regexp
  insist: |
    datum.class == String and datum =~ HostNameType::Regexp
  export: |
    datum.to_s if datum.class == HostnameType
  tests:
    rspec:
      - name: detects a Hostname
        code: 
          - "expect('example.com').to be_a(HostNameType)"
          - "expect('www.example.com').to be_a(HostNameType)"
      - name: valid Hostname type
        code:
          - "expect('example.com').to be_a(HostNameType)"
          - "expect('www.example.com').to be_a(HostNameType)"
      - name: invalid Hostname type
        code:
          - "expect('a string').to_not be_a(HostNameType)"
          - "expect('2013-12-12').to_not be_a(HostNameType)"
          - "expect(123).to_not be_a(HostNameType)"
          - "expect('ac ff').to_not be_a(HostNameType)"
          - "expect('a\nb\nc').to_not be_a(HostNameType)"
          - "expect('a\n\nb').to_not be_a(HostNameType)"
          - "expect('2013-12-12').to_not be_a(HostNameType)"
          - "expect(123).to_not be_a(HostNameType)"
Path:
  class: PathType
  regexp: |
    /^(\.\/)?[a-z][a-z0-9\-_\\]+\/|\/[a-z][a-z0-9\-_\.\\]+$/
  detect: |
    datum.class == String and datum =~ PathType::Regexp
  insist: |
    datum.class == String and datum =~ PathType::Regexp
  export: |
    datum.to_s if datum.class == PathType
  tests:
    rspec:
      - name: detects a Path
        code: 
          - "expect('./example').to be_a(PathType)"
          - "expect('/example').to be_a(PathType)"
      - name: valid Path type
        code:
          - "expect('./example').to be_a(PathType)"
          - "expect('/example').to be_a(PathType)"
      - name: invalid Path type
        code:
          - "expect('a string').to_not be_a(PathType)"
          - "expect('2013-12-12').to_not be_a(PathType)"
          - "expect(123).to_not be_a(PathType)"
          - "expect('ac ff').to_not be_a(PathType)"
          - "expect('a\nb\nc').to_not be_a(PathType)"
          - "expect('a\n\nb').to_not be_a(PathType)"
          - "expect('2013-12-12').to_not be_a(PathType)"
          - "expect(123).to_not be_a(PathType)"
URI:
  class: URIType
  detect: |
    datum.class == URI or (datum.class == String and datum == URI.regexp)
  import: |
    URI.new(datum) if datum.class == String
  export: |
    datum.to_s if datum.class == URI
  tests:
    rspec:
      - name: detects a URI
        code: 
          - "expect('http://example.com').to be_a(URIType)"
          - "expect('https://example.com').to be_a(URIType)"
          - "expect('./example/test.txt').to be_a(URIType)"
          - "expect('#/path/path/path').to be_a(URIType)"
      - name: valid URI type
        code:
          - "expect('http://example.com').to be_a(URIType)"
          - "expect('https://example.com').to be_a(URIType)"
      - name: invalid URI type
        code:
          - "expect('a string').to_not be_a(URIType)"
          - "expect('2013-12-12').to_not be_a(URIType)"
          - "expect(123).to_not be_a(URIType)"
          - "expect('ac ff').to_not be_a(URIType)"
          - "expect('a\nb\nc').to_not be_a(URIType)"
          - "expect('a\n\nb').to_not be_a(URIType)"
          - "expect('2013-12-12').to_not be_a(URIType)"
          - "expect(123).to_not be_a(URIType)"
URL:
  class: URLType
  detect: |
    datum.class == URL or (datum.class == String and datum =~ URL.regexp)
  import: |
    URL.new(datum) if datum.class == String
  export: |
    datum.to_s if datum.class == URL
  tests:
    rspec:
      - name: detects a URL
        code: 
          - "expect('http://example.com').to be_a(URLType)"
          - "expect('https://example.com').to be_a(URLType)"
          - "expect('ftp://example.com').to be_a(URLType)"
          - "expect('file://example.com').to be_a(URLType)"
      - name: valid URL type
        code:
          - "expect('http://example.com').to be_a(URLType)"
          - "expect('https://example.com').to be_a(URLType)"
      - name: invalid URL type
        code:
          - "expect('a string').to_not be_a(URLType)"
          - "expect('2013-12-12').to_not be_a(URLType)"
          - "expect(123).to_not be_a(URLType)"
          - "expect('ac ff').to_not be_a(URLType)"
          - "expect('a\nb\nc').to_not be_a(URLType)"
          - "expect('a\n\nb').to_not be_a(URLType)"
          - "expect('2013-12-12').to_not be_a(URLType)"
          - "expect(123).to_not be_a(URLType)"
Glob:
  class: GlobType
  detect: |
    datum.class == String and Dir.glob(datum).any?
  insist: |
    datum.class == String and Dir.glob(datum).any?
  convert: |
    Dir.glob(datum)
  export: |
    datum.to_s if datum.class == GlobType
  tests:
    rspec:
      - name: detects a Glob
        code: 
          - "expect('*.rb').to be_a(GlobType)"
          - "expect('**/*.rb').to be_a(GlobType)"
          - "expect('*/foo/**/*.rb').to be_a(GlobType)"
      - name: valid Glob type
        code:
          - "expect('*.rb').to be_a(GlobType)"
          - "expect('**/*.rb').to be_a(GlobType)"
          - "expect('*/foo/**/*.rb').to be_a(GlobType)"
      - name: invalid Glob type
        code:
          - "expect('a string').to_not be_a(GlobType)"
          - "expect('2013-12-12').to_not be_a(GlobType)"
          - "expect(123).to_not be_a(GlobType)"
          - "expect('ac ff').to_not be_a(GlobType)"
          - "expect('a\nb\nc').to_not be_a(GlobType)"
          - "expect('a\n\nb').to_not be_a(GlobType)"
          - "expect('2013-12-12').to_not be_a(GlobType)"
          - "expect(123).to_not be_a(GlobType)"
# URIx:
#   class: URIxType
#   detect: |
#     datum.class == URIx or (datum.class == String and datum == URI.regexp)
#   import: |
#     URIxType.new(datum) if datum.class == String
#   export: |
#     datum.to_s if datum.class == URIx
#   tests:
#     rspec:
#       - name: detects a URIx
#         code: 
#           - "expect('http://example.com').to be_a(URIxType)"
#           - "expect('https://example.com').to be_a(URIxType)"
#           - "expect('./example/test.txt').to be_a(URIxType)"
#           - "expect('#/path/path/path').to be_a(URIxType)"
#       - name: valid URIx type
#         code:
#           - "expect('http://example.com').to be_a(URIxType)"
#           - "expect('https://example.com').to be_a(URIxType)"
#       - name: invalid URIx type
#         code:
#           - "expect('a string').to_not be_a(URIxType)"
#           - "expect('2013-12-12').to_not be_a(URIxType)"
#           - "expect(123).to_not be_a(URIxType)"
#           - "expect('ac ff').to_not be_a(URIxType)"
#           - "expect('a\nb\nc').to_not be_a(URIxType)"
#           - "expect('a\n\nb').to_not be_a(URIxType)"
#           - "expect('2013-12-12').to_not be_a(URIxType)"
#           - "expect(123).to_not be_a(URIxType)"
JMESPath:
  class: JMESPathType
  insist: |
    datum.class == String and datum =~ JMESPathType::Regexp
  export: |
    datum.to_s if datum.class == JMESPathType
  tests:
    rspec:
      - name: detects a JMESPath
        code: 
          - "expect('foo.bar').to be_a(JMESPathType)"
          - "expect('foo[0].bar').to be_a(JMESPathType)"
      - name: valid JMESPath type
        code:
          - "expect('foo.bar').to be_a(JMESPathType)"
          - "expect('foo[0].bar').to be_a(JMESPathType)"
      - name: invalid JMESPath type
        code:
          - "expect('a string').to_not be_a(JMESPathType)"
          - "expect('2013-12-12').to_not be_a(JMESPathType)"
          - "expect(123).to_not be_a(JMESPathType)"
          - "expect('ac ff').to_not be_a(JMESPathType)"
          - "expect('a\nb\nc').to_not be_a(JMESPathType)"
          - "expect('a\n\nb').to_not be_a(JMESPathType)"
          - "expect('2013-12-12').to_not be_a(JMESPathType)"
          - "expect(123).to_not be_a(JMESPathType)"
JSONPath:
  class: JSONPathType
  detect: |
    datum.class == String and datum =~ /^(${PathType::Regexp})?\$\./
  insist: |
    datum.class == String and datum =~ JSONPathType::Regexp
  export: |
    datum.to_s if datum.class == JSONPathType
  tests:
    rspec:
      - name: detects a JSONPath
        code: 
          - "expect('$.store.book[0].title').to be_a(JSONPathType)"
          - "expect('$.store.book[*].author').to be_a(JSONPathType)"
      - name: valid JSONPath type
        code:
          - "expect('$.store.book[0].title').to be_a(JSONPathType)"
          - "expect('$.store.book[*].author').to be_a(JSONPathType)"
      - name: invalid JSONPath type
        code:
          - "expect('a string').to_not be_a(JSONPathType)"
          - "expect('2013-12-12').to_not be_a(JSONPathType)"
          - "expect(123).to_not be_a(JSONPathType)"
          - "expect('ac ff').to_not be_a(JSONPathType)"
          - "expect('a\nb\nc').to_not be_a(JSONPathType)"
          - "expect('a\n\nb').to_not be_a(JSONPathType)"
          - "expect('2013-12-12').to_not be_a(JSONPathType)"
          - "expect(123).to_not be_a(JSONPathType)"
JSONPointer:
  class: JSONPointerType
  detect: |
    datum.class == String and datum =~ /^(${PathType::Regexp})?#\//
  insist: |
    datum.class == String and datum =~ JSONPointerType::Regexp
  export: |
    datum.to_s if datum.class == JSONPointerType
  tests:
    rspec:
      - name: detects a JSONPointer
        code:
          - "expect('#/foo/bar').to be_a(JSONPointerType)"
          - "expect('./#/foo/bar').to be_a(JSONPointerType)"
          - "expect('/foo/bar').to be_a(JSONPointerType)"
          - "expect('/foo/0/bar').to be_a(JSONPointerType)"
      - name: valid JSONPointer type
        code:
          - "expect('/foo/bar').to be_a(JSONPointerType)"
          - "expect('/foo/0/bar').to be_a(JSONPointerType)"
      - name: invalid JSONPointer type
        code:
          - "expect('a string').to_not be_a(JSONPointerType)"
          - "expect('2013-12-12').to_not be_a(JSONPointerType)"
          - "expect(123).to_not be_a(JSONPointerType)"
          - "expect('ac ff').to_not be_a(JSONPointerType)"
          - "expect('a\nb\nc').to_not be_a(JSONPointerType)"
          - "expect('a\n\nb').to_not be_a(JSONPointerType)"
          - "expect('2013-12-12').to_not be_a(JSONPointerType)"
          - "expect(123).to_not be_a(JSONPointerType)"
Email:
  class: EmailType
  regexp: |
    /\A[\w+\-.]+@[a-z\d\-.]+\.[a-z]+\z/i
  detect: |
    datum.class == String and datum =~ EmailType::Regexp
  insist: |
    datum.class == String and datum =~ EmailType::Regexp
  export: |
    datum.to_s if datum.class == EmailType
  tests:
    rspec:
      - name: detects an Email
        code: 
          - "expect('<EMAIL>').to be_a(EmailType)"
          - "expect('<EMAIL>').to be_a(EmailType)"
      - name: valid Email type
        code:
          - "expect('<EMAIL>').to be_a(EmailType)"
          - "expect('<EMAIL>').to be_a(EmailType)"
      - name: invalid Email type
        code:
          - "expect('a string').to_not be_a(EmailType)"
          - "expect('2013-12-12').to_not be_a(EmailType)"
          - "expect(123).to_not be_a(EmailType)"
          - "expect('ac ff').to_not be_a(EmailType)"
          - "expect('a\nb\nc').to_not be_a(EmailType)"
          - "expect('a\n\nb').to_not be_a(EmailType)"
          - "expect('2013-12-12').to_not be_a(EmailType)"
          - "expect(123).to_not be_a(EmailType)"
ClassesString:
  class: ClassesStringType
  detect: |
    datum.class == String and datum =~ ClassesStringType::Regexp
  insist: |
    datum.class == String
  tests:
    rspec:
      - name: valid ClassesString type
        code:
          - "expect('some classes').to be_a(ClassesStringType)"
          - "expect('some.classes').to be_a(ClassesStringType)"
          - "expect('some,classes').to be_a(ClassesStringType)"
          - "expect('some').to be_a(ClassesStringType)"
          - "expect('some, classes').to be_a(ClassesStringType)"
          - "expect('some, -classes, _another').to be_a(ClassesStringType)"
      - name: invalid ClassesString type
        code:
          # Let's reject anything that includes a string that is not a valid HTML5 class name
          - "expect('a&x').to_not be_a(ClassesStringType)"
          - "expect('a\nb').to_not be_a(ClassesStringType)"
          - "expect('a\n\nb').to_not be_a(ClassesStringType)"
          - "expect('a\|b').to_not be_a(ClassesStringType)"
          - "expect('a\.b').to_not be_a(ClassesStringType)"
          - "expect('a,b').to_not be_a(ClassesStringType)"
Selector:
  class: SelectorType
  regexp: |
    /^(\.|\#)?[a-zA-Z0-9\-\_,\. ]+$/
  insist: |
    datum.class == String and datum =~ SelectorType::Regexp
  export: |
    datum.to_s if datum.class == SelectorType
  tests:
    rspec:
      - name: valid Selector type
        code:
          - "expect('.class').to be_a(SelectorType)"
          - "expect('#id').to be_a(SelectorType)"
          - "expect('A').to be_a(SelectorType)"
          - "expect('A.class').to be_a(SelectorType)"
          - "expect('A#id').to be_a(SelectorType)"
          - "expect('A.class#id').to be_a(SelectorType)"
      - name: invalid Selector type
        code:
          - "expect('a string').to_not be_a(SelectorType)"
          - "expect('2013-12-12').to_not be_a(SelectorType)"
          - "expect(123).to_not be_a(SelectorType)"
          - "expect('ac ff').to_not be_a(SelectorType)"
          - "expect('a\nb\nc').to_not be_a(SelectorType)"
          - "expect('a\n\nb').to_not be_a(SelectorType)"
          - "expect('2013-12-12').to_not be_a(SelectorType)"
          - "expect(123).to_not be_a(SelectorType)"
Slug:
  class: SlugType
  detect: |
    datum.class == String and datum =~ /^[a-zA-Z0-9](?:[a-zA-Z0-9_-]{0,23}[a-zA-Z0-9])?$/
  insist: |
    datum.class == String and datum =~ /^[a-zA-Z0-9\-\_]+$/
  convert: |
    datum.to_s.downcase.gsub(/[\s\-\_]/, '_')
  export: |
    datum.to_s if datum.class == SlugType
  tests:
    rspec:
      - name: detects a Slug
        code: 
          - "expect('slug').to be_a(SlugType)"
          - "expect('slug-1').to be_a(SlugType)"
          - "expect('slug_1').to be_a(SlugType)"
      - name: valid Slug type
        code:
          - "expect('slug').to be_a(SlugType)"
          - "expect('slug-1').to be_a(SlugType)"
          - "expect('slug_1').to be_a(SlugType)"
      - name: invalid Slug type
        code:
          - "expect('-a-string').to_not be_a(SlugType)"
          - "expect('_some-slug').to_not be_a(SlugType)"
          - "expect('a#string').to_not be_a(SlugType)"
          - "expect('a string').to_not be_a(SlugType)"
          - "expect('2013-12-12').to_not be_a(SlugType)"
          - "expect(123).to_not be_a(SlugType)"
          - "expect('ac ff').to_not be_a(SlugType)"
          - "expect('a\nb\nc').to_not be_a(SlugType)"
          - "expect('a\n\nb').to_not be_a(SlugType)"
          - "expect('2013-12-12').to_not be_a(SlugType)"
          - "expect(123).to_not be_a(SlugType)"
RegExp:
  class: RegExpType
  pattern: |
    /^r?\/(?!.*[\r\n]).*\/([gmisxuUAJD]+)?$/
  detect: |
    datum.class == Regexp or (datum.class == String and datum =~ RegExpType::Regexp)
  import: |
    Regexp.new(datum) if datum.class == String
  export: |
    datum.to_s if datum.class == RegExpType
  tests:
    rspec:
      - name: detects a RegExp
        code: 
          - "expect('/[a-z]+/').to be_a(RegExpType)"
          - "expect('/[a-z]+/i').to be_a(RegExpType)"
          - "expect('/[a-z]+/m').to be_a(RegExpType)"
          - "expect('/[a-z]+/g').to be_a(RegExpType)"
          - "expect('/[a-z]+/s').to be_a(RegExpType)"
      - name: valid RegExp type
        code:
          - "expect('/[a-z]+/').to be_a(RegExpType)"
          - "expect('/[a-z]+/i').to be_a(RegExpType)"
          - "expect('/[a-z]+/m').to be_a(RegExpType)"
          - "expect('/[a-z]+/g').to be_a(RegExpType)"
          - "expect('/[a-z]+/s').to be_a(RegExpType)"
      - name: invalid RegExp type
        code:
          - "expect('a string').to_not be_a(RegExpType)"
          - "expect('2013-12-12').to_not be_a(RegExpType)"
          - "expect(123).to_not be_a(RegExpType)"
          - "expect('ac ff').to_not be_a(RegExpType)"
          - "expect('a\nb\nc').to_not be_a(RegExpType)"
          - "expect('a\n\nb').to_not be_a(RegExpType)"
          - "expect('2013-12-12').to_not be_a(RegExpType)"
          - "expect(123).to_not be_a(RegExpType)"
          - "expect('/test').to_not be_a(RegExpType)"
StringList:
  class: StringListType
  detect: |
    datum.class == String and datum =~ /^[a-zA-Z0-9\.\-_]{1,100}\,([\,a-zA-Z0-9\.\-_]+)?$/
  import: |
    datum.split(',') if datum.class == String
  export: |
    datum.split(',') if datum.class == String
  convert: |
    datum.join(', ') if datum.class == Array
  tests:
    rspec:
      - name: detects a StringList
        code: 
          - "expect('a,b,c').to be_a(StringListType)"
          - "expect('a,b,c,d').to be_a(StringListType)"
      - name: valid StringList type
        code:
          - "expect('a,b,c').to be_a(StringListType)"
          - "expect('a,2,c,d').to be_a(StringListType)"
Number:
  class: NumberType
  detect: |
    datum.class == Integer or datum.class == Float
  insist: |
    datum.class == Integer or datum.class == Float
  tests:
    rspec:
      - name: detects a Number
        code: 
          - "expect(123).to be_a(NumberType)"
          - "expect(123.456).to be_a(NumberType)"
      - name: valid Number type
        code:
          - "expect(123).to be_a(NumberType)"
          - "expect(123.456).to be_a(NumberType)"
      - name: invalid Number type
        code:
          - "expect('a string').to_not be_a(NumberType)"
          - "expect('2013-12-12').to_not be_a(NumberType)"
          - "expect('ac ff').to_not be_a(NumberType)"
          - "expect('a\nb\nc').to_not be_a(NumberType)"
          - "expect('a\n\nb').to_not be_a(NumberType)"
Integer:
  class: IntegerType
  detect: |
    datum.class == Integer
  insist: |
    datum.class == Integer
  tests:
    rspec:
      - name: detects an Integer
        code: 
          - "expect(123).to be_a(IntegerType)"
      - name: valid Integer type
        code:
          - "expect(123).to be_a(IntegerType)"
      - name: invalid Integer type
        code:
          - "expect('123').to_not be_a(IntegerType)"
UnixEpoch:
  class: UnixEpochType
  detect: | # range should be between 2020 and 2099
    datum.class == Integer 
      and (datum.to_s.length == 10 or datum.to_s.length == 13)
      and (
        (datum.to_s.to_i > 1577836800 and datum.to_s.to_i < 4102444800)
        or
        (datum.to_s.to_i > 1577836800000 and datum.to_s.to_i < 4102444800000)
      )
Float:
  class: FloatType
  detect: |
    datum.class == Float
  insist: |
    datum.class == Float
Money:
  class: MoneyType
  detect: |
    datum.class == Float and datum.to_s =~ /^\d+\.\d{2}$/
  insist: |
    datum.class == Float and datum.to_s =~ /^\d+\.\d{2}$/
  tests:
    rspec:
      - name: detects a Money
        code: 
          - "expect(123.45).to be_a(MoneyType)"
      - name: valid Money type
        code:
          - "expect(123.45).to be_a(MoneyType)"
      - name: invalid Money type
        code:
          - "expect(123).to_not be_a(MoneyType)"
          - "expect('123.00').to_not be_a(MoneyType)"
DateTime:
  class: DateTimeType
  detect: |
    datum.class == DateTime
  import: |
    datum.to_datetime if datum.class == String
  export: |
    datum.to_s if datum.class == DateTime
  tests:
    rspec:
      - name: detects a DateTime
        code: 
          - "expect('2013-12-12').to be_a(DateTimeType)"
          - "expect('2013-12-12 12:00:00').to be_a(DateTimeType)"
          - "expect('2013-12-12T12:00:00').to be_a(DateTimeType)"
      - name: valid DateTime type
        code:
          - "expect('2013-12-12').to be_a(DateTimeType)"
          - "expect('2013-12-12 12:00:00').to be_a(DateTimeType)"
          - "expect('2013-12-12T12:00:00').to be_a(DateTimeType)"
      - name: invalid DateTime type
        code:
          - "expect('a string').to_not be_a(DateTimeType)"
          - "expect(123).to_not be_a(DateTimeType)"
          - "expect('ac ff').to_not be_a(DateTimeType)"
          - "expect('a\nb\nc').to_not be_a(DateTimeType)"
          - "expect('a\n\nb').to_not be_a(DateTimeType)"
          - "expect(123).to_not be_a(DateTimeType)"
DateTimeIso:
  class: DateTimeIso8601Type
  detect: |
    (datum.class == DateTime or datum.class == String) and datum.to_s =~ /^\d{4}-\d{2}-\d{2}((T| )\d{1,2}:\d{2}:\d{2}( [-|+]\d{1,2})?)?$/
  import: |
    datum.to_datetime if datum.class == String
  export: |
    datum.to_s if datum.class == DateTime
  tests:
    - name: detects a DateTime
      code: 
        - "expect('2013-12-12').to be_a(DateTimeIso8601Type)"
        - "expect('2013-12-12 12:00:00').to be_a(DateTimeIso8601Type)"
        - "expect('2013-12-12T12:00:00').to be_a(DateTimeIso8601Type)"
    - name: valid DateTime type
Date:
  class: DateType
  detect: |
    datum.class == Date or (datum.class == String and (datum.to_s =~ /\d{4}-\d{2}-\d{2}/ or datum.to_s =~ /\d{1,2}\/\d{1,2}\/\d{4}/))
  import: |
    datum.to_date if datum.class == String
  export: |
    datum.to_s if datum.class == Date
  tests:
    rspec:
      - name: detects a Date
        code: 
          - "expect('2013-12-12').to be_a(DateType)"
      - name: valid Date type
        code:
          - "expect('2013-12-12').to be_a(DateType)"
      - name: invalid Date type
        code:
          - "expect('a string').to_not be_a(DateType)"
          - "expect(123).to_not be_a(DateType)"
          - "expect('ac ff').to_not be_a(DateType)"
          - "expect('a\nb\nc').to_not be_a(DateType)"
          - "expect('a\n\nb').to_not be_a(DateType)"
          - "expect(123).to_not be_a(DateType)"
# Time:
#   class: TimeType
#   regexp: |
#     /^\d{1,2}:\d{2}:\d{2}( [-|+]\d{1,2})?$/
#   detect: |
#     datum.class == Time or (datum.class == String and datum.to_s =~ TimeType::Regexp)
#   insist: |
#     datum.class == Time or (datum.class == String and datum.to_s =~ TimeType::Regexp)
#   import: |
#     datum.to_time if datum.class == String
#   export: |
#     datum.to_s if datum.class == Time
#   tests:
#     rspec:
#       - name: detects a Time
#         code: 
#           - "expect('12:00:00').to be_a(TimeType)"
#           - "expect('12:00:00 -5').to be_a(TimeType)"
#       - name: valid Time type
#         code:
#           - "expect('12:00:00').to be_a(TimeType)"
#           - "expect('12:00:00 -5').to be_a(TimeType)"
#       - name: invalid Time type
#         code:
#           - "expect('a string').to_not be_a(TimeType)"
#           - "expect(123).to_not be_a(TimeType)"
#           - "expect('ac ff').to_not be_a(TimeType)"
#           - "expect('a\nb\nc').to_not be_a(TimeType)"
#           - "expect('a\n\nb').to_not be_a(TimeType)"
#           - "expect(123).to_not be_a(TimeType)"
# DateUSA:
#   class: DateUsaType
#   regexp: |
#     /\d\d\/\d\d\/\d{4}/
#   detect: |
#     datum.class == Date or (datum.class == String and datum.to_s =~ DateUSAType::Regexp)
#   export: |
#     datum.strftime('%m/%d/%Y') if datum.class == Date
#   tests:
#     rspec:
#       - name: detects a DateUSA
#         code: 
#           - "expect('12/12/2013').to be_a(DateUSAType)"
#       - name: valid DateUSA type
#         code:
#           - "expect('12/12/2013').to be_a(DateUSAType)"
#       - name: invalid DateUSA type
#         code:
#           - "expect('a string').to_not be_a(DateUSAType)"
#           - "expect(123).to_not be_a(DateUSAType)"
#           - "expect('ac ff').to_not be_a(DateUSAType)"
#           - "expect('a\nb\nc').to_not be_a(DateUSAType)"
#           - "expect('a\n\nb').to_not be_a(DateUSAType)"
#           - "expect(123).to_not be_a(DateUSAType)"
# DateTimeCanonical:
#   class: DateTimeCanonicalType
#   regexp: |
#     /^\d{4}-\d{2}-\d{2}(T|t)\d{2}:\d{2}:\d{2}(\.\d{1,3})?(\.\d\d?Z)$/
#   detect: |
#     datum.class == DateTime and datum.to_s =~ DateTimeSpacedType::Regexp
#   export: |
      
# DateTimeSpaced:
#   class: DateTimeSpacedType
#   regexp: |
#     /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(\.\d{1,3})?( [-|+]\d{1,2})?$/
#   detect: |
#     datum.class == DateTime and datum.to_s =~ DateTimeSpacedType::Regexp

Boolean:
  class: BooleanType
  