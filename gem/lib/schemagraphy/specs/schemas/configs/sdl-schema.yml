$schema:
  properties:
    _meta:
      acc: [version,schema]
      properties:
        version:
          type: String
          pattern: /V\d(beta|alpha)?/
        schema:
          type: Map
          properties:
            _meta:
              acc: [subschemas,core,cache]
              properties:
                subschemas:
                  type: Map
                  properties:
                    maxdepth:
                      type: Integer
                      default: 5
                core:
                  type: Map
                  properties:
                    paths:
                      type: Map
                      properties:
                        base:
                          type: Path
                          default: $GEM_LIB/schemagraphy/specs/schemas/
                        data_root:
                          type: Path
                          default: schema-root-data.yaml
                            pattern: /[^\s]+\.ya?ml/
                        text_root:
                          type: Path
                          default: schema-root-text.yaml
                            pattern: /[^\s]+\.ya?ml/
                cache:
                  type: Map
                  properties:
                    enabled:
                      type: Boolean
                      default: true
                    path:
                      type: Path
                    refresh:
                      type: Boolean
                      default: false
              
            
    

# schema:
#   subschemas:
#     maxdepth: 5
#   core:
#     path: $GEM_LIB/schemagraphy/specs/schemas/schema-root-data.yaml
#   cache:
#     enabled: true
#     path: $PROJECT/.schemagraphy/cache/schemas