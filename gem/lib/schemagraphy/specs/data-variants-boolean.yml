- type: BooleanExt
  desc: A Boolean value that accepts `true`|`false`, `yes`|`no`, `y|n`, or `1`|`0`.
  rules:
    ruby:
      class: BooleanExtType
      detect: |
        datum.class.in?([TrueClass, FalseClass, NilClass]) or (datum.is_a? String and datum =~ /^(true|false|yes|no)$/i)
      insist: |
        datum.class.in?([TrueClass, FalseClass]) or (datum.is_a? String and datum =~ /^(true|false|yes|no)$/i) or datum.nil? or datum.empty?
- type: BooleanYesNo
  desc: A Boolean value that accepts `yes` or `no` (or `Yes`/`No` or `YES`/`NO`).
  spec: Supportive tooling MUST support YAML 1.1's behavior of interpreting these strings as Booleans when indicated.
  rules:
    ruby:
      class: BooleanYesNoType
      detect: |
        datum.class.in?([TrueClass, FalseClass]) or (datum.is_a? String and datum =~ /^(yes|no)$/i)
- type: BooleanYN
  desc: A Boolean value that accepts `y` (truthy) or `n` (falsy).
  spec: Supportive tooling MUST interpret Strings of `y` and `n` as Boolean values when indicated.
  rules:
    ruby:
      class: BooleanYNType
      detect: ''
- type: BooleanBit
  desc: A Boolean value that accepts `1` for truthy or `0` for falsy.
  spec: Supportive tooling MUST validate Integers `1` and `0` as Boolean values when indicated.
  rules:
    ruby:
      class: BooleanBitType
      detect: |
        datum.class == Integer and (dataum == 0 || datum == 1)
      insist: |
        datum.class
- type: BooleanBinary
  desc: A Boolean value that treats `0` as truthy and any positive integer as falsy, similar to error codes.
  spec: Supportive tooling MUST interpret Integers as Boolean when indicated.
  rules:
    ruby:
      class: BooleanBinaryType
      detect: |
        datum.class == Integer
- type: BooleanNull
  desc: A Boolean that treats Null/Nil value as falsy.
  rules:
    ruby:
      class: BooleanNullType
      insist: |
        datum.class == NilClass