- lang: SGYML
  kind: data
  types:
    string: String
    datetime: DateTime
    integer: Integer
    float: Float
    number: Number # inclusive of integer and float
    boolean: Boolean
    _null: "Null"
    array: Array
    map: Map

- lang: YAML
  kind: data
  href: https://yaml.org
  types:
    string: string (`str`)
    datetime: timestamp
    integer: integer (`int`)
    float: float
    number: N/A
    boolean: boolean (`bool`)
    _null: "null"
    array: sequence (`seq`)
    map: mapping (`map`)

- lang: JSON
  kind: data
  href: https://json.org
  types:
    string: string
    datetime: N/A
    integer: integer
    float: number
    number: N/A
    boolean: boolean
    _null: "null"
    array: array
    map: object

- lang: SQL
  kind: data
  href: https://www.iso.org/standard/76583.html
  types:
    string: VARCHAR
    datetime: TIMESTAMP
    integer: INTEGER
    float: FLOAT
    number: DECIMAL
    boolean: BOOLEAN
    _null: "NULL"
    array: TABLE # mapping the concept is more important
    map: ROW # mapping the concept

- lang: XML
  kind: data
  href: https://www.w3.org/XML
  types:
    string: string
    datetime: dateTime
    integer: integer
    float: float
    number: N/A
    boolean: boolean
    _null: N/A
    array: array
    map: element

- lang: TOML
  kind: data
  href: https://toml.io
  types:
    string: String
    datetime: Offset Date-Time, Local Date-Time, Local Date, Local Time
    integer: Integer
    float: Float
    number: N/A
    boolean: Boolean
    _null: N/A
    array: array
    map: table    

- lang: CUE
  kind: data
  href: https://cuelang.org
  types:
    string: string
    datetime: N/A
    integer: int
    float: float
    number: number
    boolean: bool
    _null: "null"
    array: list
    map: struct

- lang: KCL
  kind: data
  href: https://kcl-lang.io
  types:
    string: string
    datetime: N/A
    integer: int
    float: float
    number: number
    boolean: boolean
    _null: "null"
    array: list
    map: dict

- lang: Python
  kind: prog
  types:
    string: str
    datetime: datetime
    integer: int
    float: float
    number: N/A
    boolean: bool
    _null: None
    array: list
    map: dict

- lang: Ruby
  kind: prog
  types:
    string: String
    datetime: DateTime
    integer: Integer
    float: Float
    number: N/A
    boolean: TrueClass, FalseClass
    _null: nil
    array: Array
    map: Hash

- lang: JavaScript
  kind: prog
  types:
    string: string
    datetime: Date
    integer: N/A
    float: N/A
    number: number
    boolean: boolean
    _null: "null"
    array: array
    map: object

- lang: Java
  kind: prog
  types:
    string: String
    datetime: LocalDateTime, ZonedDateTime, OffsetDateTime
    integer: int
    float: float
    number: N/A
    boolean: boolean
    _null: "null"
    array: array
    map: Map

- lang: C#
  kind: prog
  types:
    string: string
    datetime: DateTime
    integer: int
    float: float
    number: N/A
    boolean: bool
    _null: "null"
    array: array
    map: Dictionary

- lang: C++
  kind: prog
  types:
    string: string
    datetime: chrono
    integer: int
    float: float
    number: N/A
    boolean: bool
    _null: "nullptr"
    array: vector
    map: map

- lang: C
  kind: prog
  types:
    string: "char*"
    datetime: time_t
    integer: int
    float: float
    number: N/A
    boolean: bool
    _null: "NULL"
    array: array
    map: struct
  
- lang: Swift
  kind: prog
  types:
    string: String
    datetime: Date
    integer: Int
    float: Float
    number: N/A
    boolean: Bool
    _null: "nil"
    array: Array
    map: Dictionary

- lang: Go
  kind: prog
  types:
    string: string
    datetime: time.Time
    integer: int
    float: float32, float64
    number: N/A
    boolean: bool
    _null: "nil"
    array: slice
    map: map

- lang: Rust
  kind: prog
  types:
    string: String
    datetime: chrono
    integer: i32
    float: f64
    number: N/A
    boolean: bool
    _null: "None"
    array: Vec
    map: HashMap

- lang: PHP
  kind: prog
  types:
    string: string
    datetime: DateTime
    integer: int
    float: float
    number: N/A
    boolean: bool
    _null: "null"
    array: array
    map: array

- lang: Perl
  kind: prog
  types:
    string: string
    datetime: DateTime
    integer: int
    float: float
    number: N/A
    boolean: bool
    _null: "undef"
    array: array
    map: hash