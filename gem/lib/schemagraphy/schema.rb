require 'liquid'
require 'json-schema'
require 'graphql'

module SchemaGraphy

  class Hash
    # test any Hash for status as a valid SchemaGraph
    def valid_schemagraphy_schema? domain=nil
      false
      if domain.nil?
        domain = 'data' if self['$schema']
        domain = 'text' if self['schema']
      end
      if domain == 'data'
        true if self.schemagraphy_validate(SDL_SPECS.root_data_schema)
      elsif domain == 'text'
        true if self.schemagraphy_validate(SDL_SPECS.root_text_schema)
      end
    end
  end

  class Schema
    attr_accessor :schema, :subschemas

    # @param schema [String] A schema definition in a supported syntax and SDL
    # @param sdl [String] The schema definition language (SDL) used to define the schema
    # @param vars [Hash] Variables to be used to parse a $ref schema template
    # @param context [Hash] For $ref schemas, any override properties set beside $ref
    # @param syntax [String] The syntax of the schema definition (YAML or JSON)
    def initialize schema: String, vars: {}, context: {}, sdl: 'schemagraphy', syntax: nil
      raise ArgumentError, "Missing required argument schema" unless schema
      @original = schema
      syntax = syntax || schema.data_syntax
      case sdl
      when 'schemagraphy'
        if schema.contains_liquid
          schema = preparse_schema(schema, vars)
          if SCHEMA_SYNTAXES.contains(schema.data_syntax)
            candidate = load_data(schema, schema.data_syntax)
          else
            raise DataSyntaxTypeError, "schema", schema.data_syntax, SCHEMA_SYNTAXES
          end
        end
      when 'jsonschema'
        candidate = JSONSchemaImporter.new(schema)
      when 'graphql'
        candidate = GraphQLSchemaImporter.new(schema)
      else
        raise ArgumentError, "Unsupported schema definition language (SDL) #{sdl}"
      end
      @subschemas = collect_subschemas(schema, "", subschemas)
      @schema = candidate.valid_schemagraphy_schema? ? candidate : nil
    end

    def subject
      @subject_meta
    end
    
    # @return [Hash] A parsed and validated schema Hash
    # @return [Nil] If the schema is invalid
    # @note This is a simple Hash with no $schema key
    def schema
      @schema
    end
    
    # @param schema_update [Hash] Any parameters to override $ref schema settings after $ref schema has been parsed
    # @return [Hash] New schema based on self.schema with any new properties from the schema argument merged in
    def merge schema_update
      candidate = {'$schema': schema_update}
      raise ValidationError, "schema", "schema", "Invalid update schema, merge aborted" unless candidate.valid_schemagraphy_schema?
      new_schema = deep_merge(self.schema, schema_update, 3)
      # place new_schema into a new Hash nested under the key $schema:
      new_schema = { "$schema": new_schema }.to_yaml
      if new_schema.valid_schemagraphy_schema?
        @schema = new_schema # overwrite the existing schema with the merged schema
      else
        raise ValidationError, "schema", "schema", "Override properties form invalid schema, merge aborted"
      end
    end

    def subschemas
      @subschemas
    end

    def original
      @original
    end

    def documentation
      schema_docs(self.schema)
    end

    # # @return [Object] A draft data object or text document based on the schema
    # def stub express_defaults=true
    #   # Implement stubbing based on required/default properties/elements
    #   # Generates optional properties wtih implied values expressed when express_defaults is true
    # end

    # # @return [Hash] A FormYML definition to collect data based on this Schema
    # def formyml_definition
    #   # Generate FormYML definition to collect data based on this Schema
    # end

    # # @return [String] A Liquid template for converting FormYML data object back to match this schema
    # def formyml_collection
    #   # Generate a Liquid template for converting FormYML data object back to match this schema
    # end

    # # @return [Hash] A proper JSON Schema or GraphQL schema data object
    # def export sdl
    #   # Implement exporting to JSON Schema or GraphQL schema format
    # end
      
  end

  private

  def preparse_schema document, vars={}, format='liquid'
    begin
      object = Liquid::Template.parse(object).render(vars)
    rescue Liquid::SyntaxError => e
      raise TemplateSyntaxError, "Liquid syntax error in schema: #{e.message}"
    end
  end

  # Recursively prepare a hash of all subschemas in a schema
  # @return [Hash] A hash of all subschemas in the schema, keyed by their path in the schema
  def collect_subschemas schema, path="", subschemas={}
    schema.each do |key, value|
      current_path = "#{path}.#{key}"
      if value.is_a?(Hash) && value.key?('schema')
        if value['schema'].key?('$ref')
          begin # get the subschema from the ref path
            subschema = get_remote_schema(value['$ref'], value)
            subschema_vars = value['$ref']['vars'] || {}
          rescue Errno::ENOENT => e
            raise ValidationError, "schema", current_path, e.message
          end
        else
          subschema = value
        end
        subschema_vars = value
        subschema = Schema.new(subschema, subschema_vars)
        subschemas[current_path] = subschema
      end
      current_depth = current_path.split('.').size
      if current_depth < SDL_SPECS.schema.subschemas.maxdepth
        # Continue the recursion for nested properties
        if value.is_a?(Hash)
          collect_subschemas(value, current_path, subschemas)
        elsif value.is_a?(Array)
          value.each_with_index do |val, idx|
            if val.is_a?(Hash)
              collect_subschemas(val, "#{current_path}[#{idx}]", subschemas)
            end
          end
        end
      end
    end
    subschemas
  end

  def schema_docs schema
    # logic for generating schema documentation
  end

  def import_graphql_schema
    # logic for parsing GraphQL schemas into SchemaGraphs

  end

  def import_json_schema
    # logic for parsing JSON schemas into SchemaGraphs

  end

  def infer_schema subject, format
    if subject.is_a?(String)
      raise ArgumentError, "Missing format argument" unless format
      infer_schema_from_text(subject, format)
    else
      infer_schema_from_data(subject)
    end
  end

  def infer_schema_from_data subject
    # logic for inferring a schema from a data object

    end
  end

  def infer_schema_from_text subject, format
    # logic for inferring a schema from a text document

  end

  def infer_sdl

  end

end

