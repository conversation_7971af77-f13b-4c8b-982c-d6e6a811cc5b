module SchemaGraphy

  class Validator

    private

    def valid? subject, schema, domain
      false
      true if self.validate(subject, schema, domain)
    end

    # A handler for instigating the validation of a subject
    def validate subject, schema=nil, domain=nil
      if schema # we are imposing a schema even if the subject contains one
        raise InvalidSchema unless schema.valid_schemagraphy_schema?
        raise ArgumentError, "Missing domain argument" unless domain
        if domain == "data"
          if subject['$payload']
            data = subject['$payload']
          else
            data = subject
          else
            raise ArgumentError, "Invalid domain argument: #{domain}"
          end
        else
          data = subject
        end
      else # we are using the schema in the subject
        begin
          schema = Schema.new(subject['$schema'])
          data = subject['$payload']
        rescue Exception => e
          raise "schema failed validation with error: #{e}"
        end
      else
        raise "No schema found"
      end
      if domain == "data"
        DataObjectValidator.validate(payload, schema)
      elsif domain == "text"
        TextObjectValidator.validate(payload, schema)
      end
    end
  end

  class Hash
    def any_key_from_array self, keynames=Array
      keynames.any? { |keyname| my_hash.keys.include?(keyname) }
    end
  end

  class DataObjectValidator

    def validate payload, schema=Schema, object_path="."
      errors = []

      unless payload.schemagraphy_type == schema['type']
        errors << "Invalid type for data object at #{object_path} Expected: #{schema['type']}, got: #{data.schemagraphy_type}"
      end

      case schema['type']
      when 'ArrayTable'
        payload = object
      when 'MapTable'
        payload = object
      when 'Hash'
        payload = payload = Array.new(payload)
      end
      
      if schema['dataset']
        if schema['dataset']['max']
          unless payload.size <= schema['dataset']['max']
            errors << "Invalid dataset size for object at #{object_path}: #{payload.size}. Expected: #{schema['dataset']['max']}"
          end
        end
        skip_nodes = schema['dataset']['skip'] ? schema['dataset']['skip'] : []
        if skip_nodes.include? "_lastnode"
          skip_nodes.delete("_lastnode")
          skip_nodes << payload.size - 1
        end
      end

      if schema['properties']
        props = schema['properties']
        pmeta = props['_meta']
        props.delete('_meta')
        # now run through the nodes and apply the rules
        node_ct = 0
        payload.each do |node|
          case type
          when 'ArrayTable'
            nodeid = node_ct
            hash = node
          when 'MapTable'
            nodeid = node[0]
            hash = node[1]
          when 'Hash'
            nodeid = nil
            hash = node
          end          
          unless skip_nodes.include?(nodeid)
            if pmeta
              if pmeta['req']
                pmeta['req'].each do |req_prop|
                  req_prop_aliases = props[req_property][alias] ? props[req_property][alias] : []
                  check_for_props = [req_prop].concat(req_prop_aliases)
                  unless hash.any_key_from_array(check_for_props)
                    errors << "Missing required property: #{object_path}.#{req_prop}"
                  end
                end
              end
              if pmeta['idx']
                unless hash.key?(pmeta['idx'])
                  req_prop_aliases = props[req_property][alias] ? props[req_property][alias] : []
                  check_for_props = [req_prop].concat(req_prop_aliases)
                  unless hash.any_key_from_array(check_for_props)
                    errors << "Missing required index property: #{object_path}.#{pmeta['idx']}"
                  end
                end
              end
            end

            # check if any properties have schemas of their own by checking if there's a matching path in subschemas
            props_with_schema = props.select { |key, value| subschemas.has_key?("#{object_path}.#{key}") }
            # iterate through each property in the hash and validate
            props_with_schema.each do |pkey, pval|
              # The matching subschema for this property
              matching_subschema = subschemas["#{object_path}.#{pkey}"]
              # THIS IS WHERE all the heaviest nesting occurs, as we spelunk into nested objects
              subschema_errors = hash[pkey].validate(matching_subschema, "#{object_path}.#{pkey}")
              # index the depth of nesting by counting the dots in the object_path
              errors += subschema_errors if subschema_errors.size > 0
            end

            # iterate through each property in the hash and validate
            hash.each do |ppty|
              # check if property is not in acc or is in rej or res
              pkey = ppty[0]
              if pmeta
                if pmeta['acc']
                  # TODO add alias handling
                  unless pmeta['acc'].include?(pkey)
                    errors << "Property: #{pkey} is not in accepted properties: #{pmeta['acc']}"
                  end
                end
                if pmeta['rej']
                  if pmeta['rej'].include?(pkey)
                    errors << "Property: #{pkey} is in rejected properties: #{pmeta['rej']}"
                  end
                end
                if pmeta['res']
                  if pmeta['res'].include?(pkey)
                    errors << "Property: #{pkey} is in reserved properties: #{pmeta['res']}"
                  end
                end
              end
              if props[pkey]['value']
                if props[pkey]['value']['type'] && ppty.schemagraphy_type != props[pkey]['value']['type']
                  errors << "Invalid type for property: #{pkey}. Expected: #{props[pkey]['value']['type']}, got: #{ppty.schemagraphy_type}"
                end
                if props[pkey]['value']['pattern']
                  if ppty.schemagraphy_type == "List"
                    mismatches = []
                    ppty.each do |item|
                    mismatches << item unless !Regexp.new(props[pkey]['value']['pattern']).match(ppty)
                    if mismatches.size > 0
                      plural = mismatches.size > 1 ? "s" : ""
                      mismatches_list = mismatches.join(", ")
                      errors << "Found #{mismatches.size} mismatched item#{plural} for property: #{pkey} (#{mismatches_list})."
                    end
                  elsif ppty.schemagraphy_kind == "Scalar"
                    unless Regexp.new(props[pkey]['value']['pattern']).match(ppty)
                      errors << "Invalid value for property: #{pkey}. Expected pattern: #{props[pkey]['value']['pattern']}, got: #{ppty}"
                    end
                  else
                    errors << "Invalid value for property: #{pkey}. Expected Scalar or List of Scalars."
                  end
                end
                if props[pkey]['value']['nullable'] && props[pkey]['value']['nullable'] == false && (ppty.nil? || ppty == '' || ppty.empty?)
                  errors << "Property: #{pkey} is not nullable"
                end
                if props[pkey]['value']['options']
                  props[pkey]['value']['options'].each do |opt|
                    # TODO: validate option
                  end
                end
              end # value validation
              if props[pkey]['context']
                if props[pkey]['context']['uniqueness']
                  case props[pkey]['context']['uniqueness']
                  when "local"
                    # check if other properties in this record have this value
                  when "greedy"
                    # check if any parallel properties in other records in this dataset have this value
                    twins = nodes.select { |node| node[pkey] == ppty }
                    errors << "Property: #{pkey} is not unique (greedy)" if twins.size > 1
                  when "solo"
                    # check if any other record has this property keyname
                    siblings = nodes.select { |node| node[pkey] }
                    errors << "Property: #{pkey} is not unique (solo)" unless siblings.empty?
                  end
                end
              end
            end # hash.each
          end # unless skip_node
          node_ct += 1
        end # payload.each
      end
      
      if errors.size > 0
        errors
      else
        exit 0
      end

    end
  end

  class TextDocumentValidator
    def validate document, schema
      
    end
  end

end
