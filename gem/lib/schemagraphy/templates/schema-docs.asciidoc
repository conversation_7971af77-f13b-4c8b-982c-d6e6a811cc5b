{%- assign schema = vars.schema %}
{%- assign model  = vars.model %}
= {{ vars.name }}
{%- if schema.desc %}
{{ schema.desc }}
{%- endif %}
{%- if schema.dataset %}
{%- end %%}
{%- if schema.dataset %}
Dataset Rules::
{%-   for rule in schema.dataset %}
{%-     assign rulename = rule[0] %}
{%-     assign rule = rule[1] %}
{%-     assign model_desc = model.dataset[rulename] | split: ')'%}
{%-     assign desc = model_desc[1] %}
[horizontal]
{{ rulename }}::: {{ rule }} ({{ desc }})
{%-   endfor %}
{%- endif %}
{%- if schema.properties %}
Properties::
{%-   for ppty in schema.proprties %}
{%-     assign ppty_name = ppty[0] %}
{%-     assign prop = ppty[1] %}
[horizontal]
{{ ppty_name }}::: {{ prop }}
[horizontal]
{%- if prop.value %}
{%-   for rule in prop.value %}
{%-     assign rulename = rule[0] %}
{%-     assign rule = rule[1] %}
{{ rulename }}:::: {{ rule }} ({{ model[ppty_name]['value'][rulename] | split: ')' }})
{%-   endfor %}
{%- endif %}
{%- if prop.type %}

{%- endif %}
{%-     if prop.usage %}
{%-       if prop.usage.txt %}
Usage:::: {{ prop.usage.txt }}
{%-       endif %}
{%-       if prop.usage.not %}
+
[WARNING]
====
{{ prop.usage.not }}
====
{%-       endif %}
{%-     endif %}
{%- endif %}
{%- if prop.desc %}
{{ prop.desc }}
{%- endif %}
{%- if prop.type %}

{%-   endfor %}
{%- endif %}
{%- if schema.relationships %}
{%-   endfor %}
{%- endif %}