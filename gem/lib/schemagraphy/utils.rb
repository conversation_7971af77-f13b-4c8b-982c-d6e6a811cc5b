require 'open-uri'

module SchemaGraphy

  private

  # CLASSES

  class String
    def contains_liquid
      false
      true if self.matches(/\{\{.*\}\}|\{%.*%\}/)
    end
  end

  # METHODS

  # @param [String] string The string to parse
  # @param [String, Boolean] arg Either a syntax or a boolean indicating whether to parse
  # @return [Object, String] The parsed object or the syntax label if parsing is disabled
  # @note If no syntax is provided, the method will try to guess the syntax.
  def load_data string, arg=true
    string = string.strip
    parse  = true
    syntax = nil
  
    # Check the class of the arg and assign it to the correct variable
    if arg.is_a?(String)
      syntax = arg
    elsif !!arg == arg # check if arg is boolean
      parse = arg
    end

    # Specific syntax checks
    if string.start_with?('---')
      syntax = 'yaml'
    elsif string.start_with?('{') || string.start_with?('[')
      syntax = 'json'
    elsif string.start_with?('<?xml')
      syntax = 'xml'
    end
  
    # If a syntax is provided, we'll try to use it directly
    if syntax
      syntax_info =  DATA_SYNTAXES[syntax.to_sym]
      
      if syntax_info && syntax_info[:validator].call(string)
        return parse ? syntax_info[:parser].call(string) : syntax
      else
        raise ArgumentError, "Indicated or inferred syntax '#{syntax}' does not match the string provided"
      end
    end
  
    # If no syntax is provided, we'll go through the  DATA_SYNTAXES and try to find a match
    syntax, syntax_info =  DATA_SYNTAXES.find { |_, info| info[:validator].call(string) }
  
    if syntax
      return parse ? syntax_info[:parser].call(string) : syntax.to_s
    else
      "unknown"
    end
  end

  # @param [String] string The string to parse
  # @return [String] The syntax of the string
  # @note Gets just the syntax of the string without parsing it
  def data_syntax string
    load_data(string, false)
  end

  # @param [String] string The full path to a file and optional object subpath
  def object_from_file_path path=String
    path_split = path.split(":")
    file_path = path_split[0]
    object_path = path_split[1]
    file = File.read(file_path)
    data = load_data(file)
    # if the object_path is tier1.tier2.properties, the next block should read the object at data['tier1]['tier2']['properties']
    if object_path
      object_path.split(".").each do |key|
        data = data[key]
      end
    end
    data
  end

  def deep_merge hash1, hash2, maxdepth=3, depth=0
    unless maxdepth.is_a?(Integer) && maxdepth > 0 && maxdepth < 6
      raise ArgumentError, "maxdepth must be an integer greater than 0 and less than 6"
    end
    maxtier = maxdepth - 1
    return hash2 if depth > maxtier
    hash1.merge(hash2) do |key, old_value, new_value|
      if old_value.is_a?(Hash) && new_value.is_a?(Hash)
        deep_merge(old_value, new_value, maxdepth, depth + 1)
      else
        new_value
      end
    end
  end
  
    # @return [Hash] A proper Schema object from remote path or URL
    def get_remote_schema ref: Hash, context: {}, vars={}, refresh: Boolean
      refresh = refresh ? refresh : API_SETTINGS.schema.always_refresh
      if ref['path'].start_with?('https')
        filename = ref['path'].split('/').last
        file_path = File.join(API_SETTINGS.paths.caches.schemas, filename)
        if !File.exists?(file_path) || refresh
          begin
            download = open(ref['path'])
          rescue OpenURI::HTTPError => e
            raise SchemaFileNotFoundError, "Schema", ref['path']
          end
          IO.copy_stream(download, file_path)
        end
      else
        file_path = ref['path']
        begin
          schema_string = File.load(file_path)
        rescue => e
          raise SchemaFileNotFoundError, "Schema", schema['$ref']['path']
        end
      end
      Schema.new(schema_string, vars)
      # if context contains any keys other than $ref, capture them and merge them into Schema
      if context.keys.any? { |key| key != '$ref' }
        context.delete('$ref')
        schema.merge!(context)
      end
    end

end