require 'tilt'

module TemplateParsers
  # Renders a template with given language and variables
  # @param [String] template_content the actual template content
  # @param [String] language the template language (default: 'liquid')
  # @param [Hash] vars variables to be passed to the template
  # @return [String] rendered template
  def self.render(template_content, language: 'liquid', vars: {})
    template_class = case language.downcase
                     when 'liquid'
                       require 'liquid'
                       Tilt::LiquidTemplate
                     when 'erb'
                       require 'erb'
                       Tilt::ERBTemplate
                     when 'haml'
                       require 'haml'
                       Tilt::HamlTemplate
                     when 'slim'
                       require 'slim'
                       Tilt::SlimTemplate
                     when 'handlebars'
                       require 'handlebars'
                       Tilt::HandlebarsTemplate
                     else
                       raise "Unsupported template language: #{language}"
                     end

    template = template_class.new { template_content }
    template.render(Object.new, vars)
  end
end