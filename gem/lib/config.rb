require 'utils'
require 'safe_yaml'
require 'oMap'

module Config

  SafeYAML::OPTIONS[:default_mode] = :safe

  # Override these with SchemaGraphy::Config::PROJECT_CONFIG_PATH etc
  APPWIDE_CONFIG_PATH=File.join('$HOME', '.config', 'schemagraphy')
  PROJECT_CONFIG_PATH=File.join(Dir.pwd, '.schemagraphy', 'config')

  private

  class Config
    attr_accessor :settings

    # Gather settings one component at a time
    def initialize component=String, config_path=nil
      raise ArgumentError unless [COMPONENTS].include?(component.to_sym)
      if component == 'cli'
        if ENV['SCHEMAGRAPHY_APPWIDE_CONFIG_PATH']
          APPWIDE_CONFIG_PATH = ENV['SCHEMAGRAPHY_APPWIDE_CONFIG_PATH']
        end
        if ENV['SCHEMAGRAPHY_PROJECT_CONFIG_PATH']
          PROJECT_CONFIG_PATH = ENV['SCHEMAGRAPHY_PROJECT_CONFIG_PATH']
        end
      end
      PROJECT_CONFIG_PATH = config_path if config_path
      gem = load_gem_data(component)
      app = load_app_data(component)
      component_settings = deep_merge(gem, app) rescue gem
      @component_settings = OpenMap.new(component_settings)
    end

    def settings
      @component_settings
    end

  end

  # gets the baked-in gem defaults
  def load_gem_data component=String
    module_from_component(component)
    path = File.join(File.dirname(__FILE__), module, 'specs', "#{component}.yml")
    file = ERB.new(File.read(path)).result
    YAML.load(file) rescue {}
  end

  # gets the user's app-wide defaults
  def load_app_data component=String
    settings = {}
    appwide_global_path = File.join(APPWIDE_CONFIG_PATH, "config.yml")
    appwide_global_file = ERB.new(File.read(appwide_global_path)).result rescue {}
    appwide_global_load = YAML.load(appwide_globalfile) rescue {}
    appwide_global_data = appwide_global_load[component] rescue {}
    appwide_component_path = File.join(APPWIDE_CONFIG_PATH, "#{component}.yml")
    appwide_component_file = ERB.new(File.read(appwide_component_path)).result rescue {}
    appwide_component_data = YAML.load_file(appwide_component_path) rescue {}
    settings[component] = deep_merge(appwide_global_data, appwide_component_data)
    project_global_path = File.join(PROJECT_CONFIG_PATH, "config.yml")
    project_global_file = ERB.new(File.read(project_global_path)).result rescue {}
    project_global_load = YAML.load(project_global_file) rescue {}
    project_global_data = project_global_load[component] rescue {}
    project_component_path = File.join(PROJECT_CONFIG_PATH, "#{component}.yml")
    project_component_file = ERB.new(File.read(project_component_path)).result rescue {}
    project_component_data = YAML.load_file(project_component_path) rescue {}
    settings[component] = deep_merge(project_global_data, project_component_data)
    settings
  end

end