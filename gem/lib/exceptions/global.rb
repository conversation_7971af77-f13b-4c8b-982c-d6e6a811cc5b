class FileNotFoundError < StandardError
  def initialize file_path, file_type
    file_type = file_type ? file_type : 'File'
    super("{file_type} not found at #{file_path}")
  end
end

class FileSyntaxError < StandardError
  def initialize file_path, file_type
    super("Unsupported #{file_type} file format for file: #{file_path}")
  end
end

class DataSyntaxTypeError < StandardError
  def initialize object_name, object_format, supported_formats, custom=''
    message = "Unsupported data format for object: #{object_name}"
    message += "Expected: #{supported_formats.join(' or ')}" if supported_formats
    message += "Got: #{object_format}" if object_format
    super("Unsupported data format for object: #{object_name} #{custom}")
  end
end

class FileExtensionError < StandardError
  def initialize file_path, domain=''
    super("Unsupported #{domain} file extension for file: #{file_path}")
  end
end