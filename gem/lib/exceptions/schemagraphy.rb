module SchemaGraphy

  class ValidationError < StandardError
    def initialize object_name, error_path, error_message
      message = "Validation error for object: #{object_name} at #{error_path}"
      message =+ "with message: #{error_message}" if error_message
      super(message)
    end
  end

  class TemplateSyntaxError < StandardError
    def initialize message
      super(message)
    end
  end

  class SchemaFileNotFoundError < FileNotFoundError
    def initialize path
      super("Schema", path)
    end
  end

  class SchemaObjectNotFoundError < StandardError
    def initialize
      super("Schema object not found")
    end
  end

end