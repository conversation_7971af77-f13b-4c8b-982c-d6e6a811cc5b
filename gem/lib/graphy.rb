require 'optparse'
require 'logger'
require 'config'
require 'utils'
require 'graphy/utils'
require 'graphy/commandline'
require 'graphy/tests'
require 'exceptions/global'
require 'exceptions/schemagraphy'
require 'schemagraphy'

module GraphyCLI

  SafeYAML::OPTIONS[:default_mode] = :safe

  # Instantiate the main Logger object, which is always running
  @logger = Logger.new(STDOUT)
  @logger.formatter = proc do |severity, datetime, progname, msg|
    "#{severity}: #{msg}\n"
  end
  @logger.level = Logger::INFO # suppresses DEBUG-level messages
  
  Commandline.parser() if ARGV.size > 0

  CLI_SETTINGS = Config.new('cli', @commandline.options[:config_path]).settings
  CLI_SETTINGS.paths.base = @commandline.options[:base] ? @commandline.options[:base] : nil

end