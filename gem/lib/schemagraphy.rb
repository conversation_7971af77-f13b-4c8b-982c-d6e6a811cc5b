require 'config'
require 'utils'
require 'schemagraphy/utils'
require 'schemagraphy/syntaxes'
require 'schemagraphy/datatypes'
require 'schemagraphy/schema'
require 'schemagraphy/subject'
require 'schemagraphy/validator'
require 'schemagraphy/subject-parsers'
require 'schemagraphy/template-parsers'
require 'schemagraphy/'
require 'exceptions/global'
require 'exceptions/schemagraphy'

module SchemaGraphy

  # SchemaGraphy::SchemaGraphy:CONFIG_PATH = /path/to/custom/config.yml
  CONFIG_PATH = File.join(File.dirname(__FILE__), '.schemagraphy', 'config.yml')
  API_SETTINGS = Config.new('api', CONFIG_PATH).settings

  sdl_specs = Config.new('sdl')
  SDL_SPECS = sdl_specs

  # Public methhods
  def schemagraphy_valid? schema=nil, format=nil
    meta = resolve_domain(type)
    subject = SubjectMatter.new(self, schema, meta.domain, meta.format)
    Validator.valid?(self, schema, meta.domain, meta.format)
  end

  def schemagraphy_validate schema=nil, format=nil
    meta = resolve_domain(type)
    Validator.validate(self, schema, meta.domain, meta.format)
  end

  def schemagraphy_parse schema=nil, format=nil
    meta = resolve_domain(type)
    SubjectParser.parse(self, schema, meta.domain, meta.format)
  end

  def schemagraphy_valid_data? schema=nil, format=nil
    Validator.valid?(self, schema, 'data', format)
  end

  def schemagraphy_validate_data schema=nil, format=nil
    Validator.validate(self, schema, 'data', format)
  end

  def schemagraphy_load_data schema=nil, format=nil
    Parser.parse(self, schema, 'data', format)
  end

  def schemagraphy_valid_text? schema=nil, format
    Validator.valid?(self, schema, 'text', format)
  end

  def schemagraphy_validate_text schema=nil, format
    Validator.validate(self, schema, 'text', format)
  end

  def schemagraphy_parse_text schema=nil, format
    Parser.parse(self, schema, 'text', format)
  end

  def resolve_domain type
    if type == 'text' || type == 'data'
      domain = type
    else
      syntax = type
      domain = SYNTAX_DOMAINS[syntax.to_sym] || raise(ArgumentError, "Invalid type passed to #{__method__}")
    end
    out = Map.new(:domain, :syntax)
    out.domain = domain
    out.syntax = syntax
  end

end

# Public monkey patches for core classes
class Object
  include SchemaGraphy
end

class Array
  include SchemaGraphy
end

class Hash
  include SchemaGraphy
end

class String
  include SchemaGraphy
end