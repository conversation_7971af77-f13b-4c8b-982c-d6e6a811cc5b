require_relative 'datatypes'

module Tests
  file_path = File.join(File.dirname(__FILE__), 'specs/tests/types.yml')
  @test_types = YAML.load_file(file_path)

  def self.what totest
    case totest
    when "types"
      @test_types.each do |node|
        deemed = node['sample'].schemagraphy_meta
        if deemed.type == "Boolean"
          sample = "true" if node['sample'] == true
          sample = "false" if node['sample'] == false
        else
          if deemed.class == "Integer"
            sample = "#{node['sample']}"
          else
            sample = node['sample'].to_yaml[4..-2] if deemed.kind == "enumerable"
            sample = node['sample'].to_s if deemed.kind == "scalar"
          end
        end
        puts "---\n" + sample + "\n---"
        puts "native: " + node['sample'].class.to_s
        puts "stated: " + node['truth']
        puts "deemed: " + deemed.type
        puts
      end
    end
  end
end