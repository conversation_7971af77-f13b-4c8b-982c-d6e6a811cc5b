module GraphyCLI

  def self.load_file path, formats=['json','yaml']
    file_path = File.join(File.dirname(__FILE__), path)
    extension = File.extname(file_path)[1..] # Removing dot
  
    unless File.exist?(file_path)
      @logger.error "No file found at #{file_path}"
      raise "No file found at #{file_path}"
    end
  
    meta = Map.new(:format, :object)
    app_formats = APPMETAgraphy-cli['formats']
    format = app_formats.keys.find do |key|
      app_formats[key]['exts'].include?(extension) && formats.include?(key)
    end
  
    if format.nil?
      @logger.error "Unsupported file format for file: #{file_path}"
      exit 1
    end
  
    meta.format = format
    meta.object = eval(app_formats[format]['load'])
  
    meta
  end

end