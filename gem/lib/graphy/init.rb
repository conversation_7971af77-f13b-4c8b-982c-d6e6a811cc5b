require 'fileutils'
require 'tty-prompt'

module GraphyCL<PERSON>

  def init_project
    check_and_handle_config(APPWIDE_CONFIG_PATH, "global")
    check_and_handle_config(PROJECT_CONFIG_PATH, "project")

    unless FileUtils.exist?('.gitignore')
      prompt = TTY::Prompt.new
      if prompt.yes?("Would you like to create a .gitignore file?")
        File.open('.gitignore', 'w') do |f|
          f.puts "# Ignore these paths:"
        end
      end
    end

    unless FileUtils.exist?('.gitignore') && File.readlines('.gitignore').grep(/schemagraphy/i).any?
      prompt = TTY::Prompt.new
      if prompt.yes?("Would you like to append common paths to your .gitignore file?")
        File.open('.gitignore', 'a') do |f|
          f.puts "# Schemagraphy"
          f.puts "_build"
        end
      end
      if prompt.yes?("Would you like to add your project config path to .gitignore?")
        File.open('.gitignore', 'a') do |f|
          f.puts "# Schemagraphy"
          f.puts PROJECT_CONFIG_PATH
        end
      end
      if prompt.yes?("Open .gitignore and edit?")
        editor = ENV['EDITOR'] || 'nano'
        system("#{editor} .gitignore")
      end
    end

  end

  private

  def check_and_handle_config path, scope
    if FileUtils.exist?(path)
      configs_found = []
      if FileUtils.exist?(File.join(path, 'config.yml'))
        configs_found << 'config.yml'
        Dir.glob(File.join(path, '*.yml')).each do |file|
          configs_found << file
        end
      end
      @logger.info "Found #{configs_found.length} #{scope} config file(s) in #{path}: #{configs_found.join(', ')}"
    else
      prompt = TTY::Prompt.new
      question = "Where would you like to create a #{scope} configuration?"
      default = File.join(path, 'config.yml')
      prompt.ask(question, default: default) do |q|
        File.open(q, 'w') do |f|
          create_config_yaml()
        end
      end
    end
  end

  def create_config_yaml
    config = {}
    [COMPONENTS].each do |component|
      config[component] = YAML.load_file(File.join(File.dirname(__FILE__), module, 'specs', "#{component}.yml"))
    end
    config.to_yaml
  end
    
end