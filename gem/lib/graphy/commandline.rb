require 'optparse'
require 'config'
require 'tests'
require 'pathname'

module GraphyCLI

  # set default options if no arguments are passed
  if ARGV.empty?
    @no_args = true
    ARGV << '--version'
    ARGV << '--help'
  end

  # structure tthe commandline
  class Commandline
    attr_accessor :subcommand, :subject_paths, :options

    def initialize
      @subcommands = [:setup, :val, :parse, :infer, :docs :help]
      capture_subcommand_and_paths()
      @subject_paths = []
    end

    def subcommands
      @subcommands
    end

    def subcommand
      @subcommand ||= nil
    end

    def subject_paths
      @subject_paths ||= []
    end

    def options
      @options ||= {}
    end

    private

    def capture_subcommand_and_paths

      if @subcommands.include?(ARGV[0].to_sym)
        self.subcommand = ARGV.shift
      end
      
      ARGV.each_with_index do |arg, index|
        break if arg.start_with?('-')
        self.subject_paths << arg
        ARGV.delete_at(index)
      end

    end

  end

  def self.parser

    commandline = Commandline.new

    # Check if first argument is a subcommand
    if commandline.subcommands.include?(ARGV[0])
      commandline.subcommand = ARGV.shift
    end

    options_parser = OptionParser.new do |opts|
      opts.banner = <<~DOCBLOCK
        USAGE:
            graphy [action] [path/to/file[:object][ file2]] [options]
          
          ACTIONS:
            val[idate]   examine a schema or governed data/content for validity
            parse        generate a new data object or content source with defaults populated
            infer        reverse engineer a a schema by reading a data object
            docs         generate documentation for a schema
        
          SUBJECT FILES:
            a content or data file to read for validation, parsing, or inference
            also accepts comma-delimited list of file paths
        
          OPTIONS:
        DOCBLOCK

      opts.on("-b", "--base PATH", "Set the starting point for all relative paths.") do |n|
        commandline.options[:base_path] = n
      end

      opts.on("-c", "--config PATH", "Impose local configuration file.") do |n|
        commandline.options[:config_path] = n
      end

      opts.on("-s", "--schema PATH", "Impose a schema on the loaded data object.") do |n|
        commandline.options[:schema_path] = n
      end

      opts.on("--types [TYPE[,TYPE]]", "List/explain the SchemaGraphy data types.") do |n|
        DataTypes.types_explainer
      end

      opts.on("--test TEST", "DEV: Specify a manual test to run.") do |n|
        Tests.what n
      end
    
      opts.on("--verbose", "Run verbose debug logging.") do |n|
        @logger.level = Logger::DEBUG
        @verbose = true
      end
    
      opts.on("--quiet", "Run with only WARN- and error-level logs written to console.") do |n|
        @logger.level = Logger::WARN
        @quiet = true
      end
    
      opts.on("-v", "--var KEY=VALUE", "For passing key/value pairs to schema templates.") do |n|
        pair = {}
        k,v = n.split('=')
          pair[k] = v
        @passed_vars.merge!pair
      end
    
      opts.on("--version", "Display SchemaGraphy/Graphy version info") do
        puts "SchemaGraphs version: " + SDL_SPECS['version']
        puts "SchemaGraphy API/CLI version: " + API_SETTINGS['version']
        exit unless @no_args
      end

      opts.on("-h", "--help", "Returns help.") do
        puts ""
        puts opts
        exit
      end

      @commandline = commandline
    
    end

    options_parser.parse!(ARGV)

    # puts "Subcommand: #{commandline.subcommand}" if commandline.subcommand
    # puts "Subject paths: #{commandline.subject_paths}" if commandline.subject_paths
  
  end

end