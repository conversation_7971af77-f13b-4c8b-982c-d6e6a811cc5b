# - sample: true
#   truth: Boolean

- sample: "some text"
  truth: String

- sample: 1
  truth: Number

- sample: {this: 1}
  truth: Parameter

- sample: {this: 1, that: 2, the-other: "thing"}
  truth: Dictionary

- sample:
    this: 1
    that: 2
    the-other: 3
  truth: Dictionary

- sample:
    node1:
      this: 1
      that: 2
      the-other: 3
    node2:
      this: 4
      that: 5
      the-other: 6
  truth: MapTable

- sample:
    node1:
      this: 1
      that: 2
      the-other: 3
    node2:
      - this
      - that
  truth: Map

- sample:
    - this: 1
      that: 2
      the-other: 3
    - this: 4
      that: 5
      the-other: 6
  truth: ArrayTable

- sample:
    - this
    - that
    - the-other
  truth: List

- sample: [1, 2, 3]
  truth: List

- sample: [five,six,seven]
  truth: List

- sample: 2019-04-22
  truth: Date

- sample: 3/7/2019
  truth: Date

- sample: 03/07/2019
  truth: Date

- sample: "5:22:33 -5"
  truth: Time

- sample: "2019-03-07 5:22:33 +3"
  truth: DateTime

- sample: 5.3
  truth: Float

- sample: "5.4"
  truth: String

- sample: https://who.test.com/something?thisthat&what=555.646
  truth: URI

- sample: 0.12.3-rc1
  truth: SemVer

- sample: 0.12.3
  truth: SemVer

- sample: _this-here
  truth: Slug

- sample: this_thing-here_where-this
  truth: Slug

- sample: this,that,the-other
  truth: ScalarList

- sample: /test-regex[<?dd>foo]. dfdf/gm
  truth: RegExp