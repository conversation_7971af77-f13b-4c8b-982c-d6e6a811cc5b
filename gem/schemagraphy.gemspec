lib = File.expand_path('lib', __dir__)
$LOAD_PATH.unshift(lib) unless $LOAD_PATH.include?(lib)

Gem::Specification.new do |spec|
  spec.name          = 'SchemaGraphy'
  spec.version       = '0.1.0'
  spec.authors       = ['<PERSON>']
  spec.email         = ['<EMAIL>']
  spec.summary       = 'An API and CLI for validating, parsing, and more with SchemaGraphs.'
  spec.description   = 'An API and CLI for validating, parsing, and expanding your structured content and semi-structured data with SchemaGraphs.'
  spec.homepage      = 'https://github.com/DocOps/SchemaGraphy'
  spec.license       = 'MIT'

  spec.files         = Dir['{lib,spec}/**/*'] + %w[Gemfile schemagraphy.gemspec README.adoc]
  spec.bindir        = 'bin'
  spec.executables   = ['graphy']
  spec.require_paths = ['lib']

  spec.add_development_dependency 'bundler',      '~> 2.0'
  spec.add_development_dependency 'rspec',        '~> 3.0'
  spec.add_runtime_dependency     'safe_yaml',    '~> 1.0'
  spec.add_runtime_dependency     'semantic',     '~> 1.5'
  spec.add_runtime_dependency     'asciidoctor',  '~> 1.5'
  spec.add_runtime_dependency     'json',         '~> 2.2'
  spec.add_runtime_dependency     'crack',        '= 0.4.5'
  spec.add_runtime_dependency     'liquid',       '~> 4.0'
  spec.add_runtime_dependency     'logger',       '~> 1.3'
  spec.add_runtime_dependency     'kramdown',     '~> 2.4'
  spec.add_runtime_dependency     'toml-rb',      '~> 2.2'
  spec.add_runtime_dependency     'csv',          '~> 3.0'
  spec.add_runtime_dependency     'json-schema',  '~> 2.8'
  spec.add_runtime_dependency     'graphql',      '~> 1.9'
  spec.add_runtime_dependency     'tty-prompt',   '~> 0.19'
  spec.add_runtime_dependency     'tilt',
end
