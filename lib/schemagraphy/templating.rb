# frozen_string_literal: true

require 'tilt'

module SchemaGraphy
  module Templating
    if defined?(Sourcerer::Templating)
      extend Sourcerer::Templating
    else
      extend self

      module Engines
        extend self

        SUPPORTED_ENGINES = {
          'erb'    => 'erb',
          'liquid' => 'liquid'
        }.freeze

        def compile str, engine
          case engine.to_s
          when 'liquid'
            Liquid::Template.parse(str)
          when 'erb'
            Tilt::ERBTemplate.new { str }
          else
            raise ArgumentError, "Unsupported templating engine: #{engine}"
          end
        end

        def render compiled, engine, vars = {}
          case engine.to_s
          when 'liquid'
            compiled.render(vars)
          when 'erb'
            compiled.render(Object.new, vars)
          else
            compiled.to_s
          end
        end

        def supported? engine
          SUPPORTED_ENGINES.key?(engine.to_s)
        end
      end

      def resolve_field field, context = {}
        return field unless field.respond_to?(:templated?) && field.templated?
      
        if defined?(::Sourcerer::Templating)
          ::Sourcerer::Templating.resolve_field(field, context)
        else
          field.render(context)
        end
      end
      
      def render_field_if_template val, context = {}
        if val.respond_to?(:templated?) && val.templated?
          if defined?(::Sourcerer::Templating)
            ::Sourcerer::Templating.resolve_field(val, context)
          else
            val.render(context)
          end
        else
          val
        end
      end
    
      def precompile_from_schema! data, schema, base_path = '', scope: {}
        return unless data.is_a?(Hash)

        data.each do |key, value|
          path = [base_path, key].reject(&:empty?).join('.')

          if value.is_a?(Hash)
            precompile_from_schema!(value, schema, path, scope: scope)
          end

          if SchemaGraphy::SchemaUtils.templated_field?(schema, path)
            compile_templated_fields!(
              data: data,
              schema: schema,
              fields: [{ key: key, path: path }],
              scope: scope
            )
          end
        end
      end

      def compile_templated_fields! data:, schema:, fields:, scope: {}
        fields.each do |field_entry|
          field_key = field_entry.is_a?(Hash) ? field_entry[:key] : field_entry
          field_path = field_entry.is_a?(Hash) ? field_entry[:path] : field_key.to_s
      
          val = data[field_key]
          next unless val.is_a?(String) || (val.is_a?(Hash) && val['__tag__'] && val['value'])
      
          raw = val.is_a?(Hash) ? val['value'] : val
          tagged = val.is_a?(Hash)
      
          templating = SchemaUtils.templating_config_for(schema, field_path)
          engine = tagged ? val['__tag__'] : (templating['default'] || 'liquid')
      
          # Use Sourcerer for compilation and rendering if present
          if defined?(::Sourcerer::Templating)
            compiled = ::Sourcerer::Templating.compile(raw, engine)
      
            if templating['delay']
              data[field_key] = ::Sourcerer::Templating::TemplatedField.new(raw, compiled, engine, tagged, false)
            else
              data[field_key] = ::Sourcerer::Templating.render(compiled, engine, scope)
            end
          else
            # Fallback if Sourcerer is not available
            compiled = compile_with_engine(raw, engine)
      
            if templating['delay']
              data[field_key] = TemplatedField.new(raw, compiled, engine, tagged, false)
            else
              data[field_key] = Engines.render(compiled, engine, scope)
            end
          end
        end
      end

      def render_all_templated_fields! data, context = {}
        return unless data.is_a?(Hash)

        data.each do |key, value|
          if value.is_a?(SchemaGraphy::Templating::TemplatedField)
            data[key] = value.render(context)
          elsif value.is_a?(Hash)
            render_all_templated_fields!(value, context)
          elsif value.is_a?(Array)
            value.each_with_index do |item, idx|
              if item.is_a?(SchemaGraphy::Templating::TemplatedField)
                value[idx] = item.render(context)
              elsif item.is_a?(Hash)
                render_all_templated_fields!(item, context)
              end
            end
          end
        end
      end

      def compile_with_engine str, engine
        case engine.to_s
        when 'liquid'
          Liquid::Template.parse(str)
        when 'erb'
          ERB.new(str)
        else
          str
        end
      end
      private :compile_with_engine

      def liquid_like? str
        str.include?('{{') || str.include?('{%')
      end

      def erb_like? str
        str.match?(/<%=?\s*[^%]+%>/)
      end

      class TemplatedField
        attr_reader :raw, :compiled, :engine, :tagged, :inferred

        def initialize raw, compiled, engine, tagged, inferred
          @raw = raw
          @compiled = compiled
          @engine = engine
          @tagged = tagged
          @inferred = inferred
        end

        def templated?
          tagged || inferred
        end

        def deferred?
          compiled.nil?
        end

        def to_liquid
          self
        end

        def render context = {}
          full_scope = if context.respond_to?(:environments)
            context.environments.first
          else
            context
          end

          SchemaGraphy::Templating::Engines.render(compiled, engine, full_scope)
        end

        def to_s
          render({})
        end
      end

      class Context
        attr_reader :stage, :strict, :scopes

        def initialize stage: :load, strict: false, scopes: {}
          @stage = stage.to_sym
          @strict = strict
          @scopes = scopes.transform_keys(&:to_sym)
        end

        def self.from_schema schema_fragment
          return new unless schema_fragment.is_a?(Hash)

          render_conf = schema_fragment['templating'] || {}
          stage  = (render_conf['stage'] || :load).to_sym
          strict = render_conf['strict'] == true
          scopes = {}

          (render_conf['scopes'] || {}).each do |name, value|
            scopes[name.to_sym] = value
          end

          new(stage: stage, strict: strict, scopes: scopes)
        end

        def get_scope name
          scopes[name.to_sym] || {}
        end

        def merged_scope
          scopes.values.inject({}) { |memo, scope| memo.merge(scope) }
        end

        def with_scope name, data
          Context.new(stage: stage, strict: strict, scopes: scopes.merge(name.to_sym => data))
        end

        def merge other
          Context.new(
            stage: other.stage || stage,
            strict: other.strict.nil? ? strict : other.strict,
            scopes: scopes.merge(other.scopes)
          )
        end
      end
    end
  end
end
