require 'yaml'
require 'psych'

module SchemaGraphy
  class Loader
    def self.load_yaml_with_tags path
      data = Psych.load_file(path, aliases: true)
      ast  = Psych.parse_file(path)
      attach_tags(ast.root, data)
      data
    end

    def self.attach_tags node, data
      return unless node.is_a?(Psych::Nodes::Mapping)
    
      node.children.each_slice(2) do |key_node, val_node|
        key = key_node.value
    
        if val_node.respond_to?(:tag) && val_node.tag && data[key].is_a?(String)
          normalized_tag = val_node.tag.sub(/^!+/, '').sub(/^.*:/, '')
          data[key] = {
            'value' => data[key],
            '__tag__' => normalized_tag
          }
        elsif data[key].is_a?(Hash)
          attach_tags(val_node, data[key])
        end
      end
    end
    
  end
end
