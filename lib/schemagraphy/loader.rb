require 'yaml'
require 'psych'

module SchemaGraphy
  class Loader
    def self.load_yaml_with_tags path
      content = File.read(path)

      # Quick text-based check for any regexp tags before parsing
      has_regexp_tags = content.match?(/!([a-z]*\/|)regexp/i)

      data = Psych.unsafe_load(content)
      ast  = Psych.parse_file(path)
      attach_tags(ast.root, data)

      # Only normalize if we detected regexp tags in the raw text
      normalize_regexp_objects!(data) if has_regexp_tags

      data
    end

    private

    def self.normalize_regexp_objects!(obj)
      case obj
      when Hash
        obj.each { |k, v| obj[k] = normalize_regexp_objects!(v) }
      when Array
        obj.map! { |item| normalize_regexp_objects!(item) }
      when Regexp
        obj.source  # Convert Regexp object back to string pattern
      else
        obj
      end
    end

    def self.attach_tags node, data
      return unless node.is_a?(Psych::Nodes::Mapping)

      node.children.each_slice(2) do |key_node, val_node|
        key = key_node.value

        if val_node.respond_to?(:tag) && val_node.tag && data[key].is_a?(String)
          normalized_tag = val_node.tag.sub(/^!+/, '').sub(/^.*:/, '')
          data[key] = {
            'value' => data[key],
            '__tag__' => normalized_tag
          }
        elsif data[key].is_a?(Hash)
          attach_tags(val_node, data[key])
        end
      end
    end

  end
end
