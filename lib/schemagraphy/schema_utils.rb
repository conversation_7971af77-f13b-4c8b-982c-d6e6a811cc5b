module SchemaGraphy
  module <PERSON>hemaUtils
    extend self

    # Retrieve default value from schema definition using JSON Pointer–like syntax
    # The schema object will be shaped like so:
    # {
    #   "$schema": {
    #     "properties": {
    #       "property1": {
    #         "properties": {
    #           "subproperty1": {
    #             "default": "value1",
    #             "type": "String"
    #         }
    #       },
    #     },
    #   },
    # }
    # Which would be referenced in path argument as "property1.subproperty1" and would 
    #  return 'value1'
    def crawl_properties schema, path
      path_components = path.split('.')
      current = schema['$schema'] || schema

      path_components.each do |component|
        return nil unless current.is_a?(Hash)
        return nil unless current['properties']&.key?(component)

        current = current['properties'][component]
      end

      current
    end

    def default_for schema, path
      property = crawl_properties(schema, path)
      return nil unless property.is_a?(Hash)

      property['default'] || property['dflt']
    end

    def type_for schema, path
      property = crawl_properties(schema, path)
      return nil unless property.is_a?(Hash)

      property['type']
    end

    def templating_config_for schema, path
      property = crawl_properties(schema, path)
      return {} unless property.is_a?(Hash)
    
      if property['templating']
        return property['templating']
      end
    
      if property['type'].to_s.downcase == 'liquid'
        return { 'default' => 'liquid', 'delay' => true }
      elsif property['type'].to_s.downcase == 'erb'
        return { 'default' => 'erb', 'delay' => true }
      else
        return {}
      end
    end   

    def templated_field? schema, path
      property = crawl_properties(schema, path)
      return false unless property.is_a?(Hash)
    
      property.key?('templating') && property['templating'].is_a?(Hash)
    end    
    
    def self.crawl_meta schema, path = nil
      parts = path ? path.split('.') : []
      node = schema['$schema'] || schema
      meta = {}
    
      parts.each do |part|
        node = node['properties'][part] if node['properties']&.key?(part)
        break unless node.is_a?(Hash)
    
        # Only update meta if this level has it
        meta = node['templating'] ? node : meta
      end
    
      meta['_meta'] || meta['sgyml'] || meta['templating'] || {}
    end
    
  end
end
