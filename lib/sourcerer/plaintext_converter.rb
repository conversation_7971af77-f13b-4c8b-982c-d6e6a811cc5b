# This module will likely spin off into a gem

# frozen_string_literal: true

require 'asciidoctor'

module Sourcerer
  class PlainTextConverter < Asciidoctor::Converter::Base
    # Identify ourselves as a converter for the "plaintext" backend
    register_for 'plaintext'

    # Called when As<PERSON><PERSON><PERSON> wants to convert a node
    def convert node, transform = nil, opts = {}
    if respond_to?("convert_#{node.node_name}", true)
      send("convert_#{node.node_name}", node)
    elsif node.respond_to?(:content)
      node.content.to_s
    elsif node.respond_to?(:text)
      node.text.to_s
    else
      ''
    end
  end
  
  

    private

    def convert_document node
      emit_attrs = node.attr('sourcerer_mode') == 'emit_attrs'
    
      if emit_attrs
        # only emit attribute lines directly, nothing else
        attrs = node.attributes.select { |k, v|
          k.is_a?(String) && !v.nil? && !k.start_with?('backend-', 'safe-mode', 'doctype', 'sourcerer_mode')
        }.map { |k, v| ":#{k}: #{v}" }
    
        attrs.join("\n") # NO EXTRA SPACES OR LINES
      else
        node.blocks.map { |block| convert block }.join("\n")
      end
    end
    

    def convert_section node
      title = node.title? ? node.title : ''
      body = node.blocks.map { |block| convert block }.join("\n")
      [title, body].reject(&:empty?).join("\n")
    end

    def convert_paragraph node
      node.lines.join("\n")
    end

    def convert_listing node
      node.content
    end

    def convert_literal node
      node.content
    end
  end
end
