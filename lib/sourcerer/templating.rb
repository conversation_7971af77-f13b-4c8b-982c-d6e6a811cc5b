module Sourcerer
  module Templating
    module Engines
      extend self

      SUPPORTED_ENGINES = {
        'liquid' => 'liquid',
        'erb'    => 'erb'
      }.freeze

      def compile str, engine
        case engine.to_s
        when 'liquid'
          Liquid::Template.parse(str)
        when 'erb'
          require 'erb'
          ERB.new(str)
        else
          raise ArgumentError, "Unsupported engine: #{engine}"
        end
      end

      def render compiled, engine, vars = {}
        case engine.to_s
        when 'liquid'
          compiled.render(vars)
        when 'erb'
          compiled.result_with_hash(vars)
        else
          compiled.to_s
        end
      end
    end

    class TemplatedField
      attr_reader :raw, :compiled, :engine, :tagged, :inferred

      def initialize raw, compiled, engine, tagged, inferred
        @raw      = raw
        @compiled = compiled
        @engine   = engine
        @tagged   = tagged
        @inferred = inferred
      end

      def templated?
        true
      end

      def deferred?
        compiled.nil?
      end

      def to_liquid
        self
      end

      def render context = {}
        scope = context.respond_to?(:environments) ? context.environments.first : context
        Engines.render(compiled, engine, scope)
      end

      def to_s
        render({})
      end
    end

    class Context
      attr_reader :stage, :strict, :scopes

      def initialize stage: :load, strict: false, scopes: {}
        @stage  = stage.to_sym
        @strict = strict
        @scopes = scopes.transform_keys(&:to_sym)
      end

      def self.from_schema schema_fragment
        render_conf = schema_fragment['templating'] || {}

        stage  = (render_conf['stage'] || :load).to_sym
        strict = render_conf['strict'] == true
        scopes = (render_conf['scopes'] || {}).transform_keys(&:to_sym)

        new(stage: stage, strict: strict, scopes: scopes)
      end

      def merged_scope
        scopes.values.reduce({}) { |acc, s| acc.merge(s) }
      end
    end

    def self.compile_templated_fields! data:, schema:, fields:, scope: {}
      fields.each do |field_entry|
        key  = field_entry[:key]
        path = field_entry[:path]
        val  = data[key]

        next unless val.is_a?(String) || (val.is_a?(Hash) && val['__tag__'] && val['value'])

        raw     = val.is_a?(Hash) ? val['value'] : val
        tagged  = val.is_a?(Hash)
        config  = SchemaGraphy::SchemaUtils.templating_config_for(schema, path)
        engine  = tagged ? val['__tag__'] : (config['default'] || 'liquid')

        compiled = Engines.compile(raw, engine)

        if config['delay']
          data[key] = TemplatedField.new(raw, compiled, engine, tagged, inferred: !tagged)
        else
          data[key] = Engines.render(compiled, engine, scope)
        end
      end
    end

    def self.render_field_if_template val, context = {}
      if val.respond_to?(:templated?) && val.templated?
        val.render(context)
      else
        val
      end
    end
  end
end
