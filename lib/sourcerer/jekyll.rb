# frozen_string_literal: true

require_relative 'jekyll/bootstrapper'
require_relative 'jekyll/monkeypatches'
require_relative 'jekyll/liquid/file_system'
require_relative 'jekyll/liquid/filters'
require_relative 'jekyll/liquid/tags'

module Sourcerer
  module Je<PERSON><PERSON>

    def self.initialize_liquid_runtime
      Bootstrapper.load_plugins
      Monkeypatches.patch_jekyll
    end
    
  end
end
