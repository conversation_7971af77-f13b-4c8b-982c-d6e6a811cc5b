# frozen_string_literal: true

require 'kramdown-asciidoc'

module Sourcerer
  module Jekyll
    module Liquid
      module Filters

        def render input, vars=nil
          scope = if vars.is_a?(Hash)
            vars.transform_keys(&:to_s)
          else
            {}
          end

          template =
            if input.respond_to?(:render) && input.respond_to?(:templated?) && input.templated?
              input
            else
              ::Liquid::Template.parse(input.to_s)
            end

          template.render(scope)
        end

        def sluggerize input, format='kebab'
          return input unless input.is_a? String
          case format
          when 'kebab' then input.downcase.gsub(/[\s\-\_]/, '-')
          when 'snake' then input.downcase.gsub(/[\s\-\_]/, '_')
          when 'camel' then input.downcase.gsub(/[\s\-\_]/, '_').camelize(:lower)
          when 'pascal' then input.downcase.gsub(/[\s\-\_]/, '_').camelize(:upper)
          else input
          end
        end

        def plusify input
          input.gsub(/\n\n+/, "\n+\n")
        end

        def md_to_adoc input, wrap='ventilate'
          options = {}
          options[:wrap] = wrap.to_sym if wrap
          Kramdoc.convert(input, options)
        end

        def indent input, spaces = 2, line1 = false
          indent = ' ' * spaces
          lines = input.split("\n")
          indented = if line1
            lines.map { |line| indent + line }
          else
            lines.map.with_index { |line, i| i.zero? ? line : indent + line }
          end
          indented.join("\n")
        end

        def sgyml_type_check input
          if input == nil
            "Null:nil"
          elsif input.is_a? Array
            # if all items in Array are (integer, float, string, boolean)
            if input.all? { |item| item.is_a? Integer or item.is_a? Float or item.is_a? String or item.is_a? TrueClass or item.is_a? FalseClass }
              "Enumerable:ArrayList"
            else
              "Enumerable:Array"
            end
          elsif input.is_a? Hash
            "Enumerable:Map"
          elsif input.is_a? String
            "Scalar:String"
          elsif input.is_a? Integer
            "Scalar:Number"
          elsif input.is_a? Time
            "Scalar:DateTime"
          elsif input.is_a? Float
            "Scalar:Float"
          elsif input.is_a? TrueClass or input.is_a? FalseClass
            "Scalar:Boolean"
          else
            "unknown:unknown"
          end
        end

        def ruby_class input
          input.class.name
        end

        def demarkupify input
          return input unless input.is_a? String
          input = input.gsub(/`"|"`/, '"')
          input = input.gsub(/'`|`'/, "'")
          input = input.gsub(/[\*_`]/, '')
          # change curly quotes to striaght quotes
          input = input.gsub(/[“”]/, '"')
          input = input.gsub(/[‘’]/, "'")
          input
        end

        def inspect_yaml input
          require 'yaml'
          YAML.dump(input)
        end

      end
    end
  end
end

# Register the filters automatically
::Liquid::Template.register_filter(Sourcerer::Jekyll::Liquid::Filters)