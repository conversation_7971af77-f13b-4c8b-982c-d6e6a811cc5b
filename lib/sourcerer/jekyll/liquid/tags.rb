module Sourcerer
  module Jekyll
    module Tags
      class EmbedTag < ::Liquid::Tag
        def initialize(tag_name, markup, tokens)
          super
          @partial_name = markup.strip
        end

        def render(context)
          includes_paths = context.registers[:includes_load_paths] || []

          found_path = includes_paths.find do |base|
            candidate = File.expand_path(@partial_name, base)
            File.exist?(candidate)
          end

          unless found_path
            raise "Embed file not found: #{@partial_name}"
          end

          full_path = File.expand_path(@partial_name, found_path)
          source = File.read(full_path)

          begin
            partial = ::Liquid::Template.parse(source)
          rescue => e
            raise
          end

          begin
            output = partial.render!(context)
            output
          rescue => e
            raise
          end
        end
      end
    end
  end
end

::Liquid::Template.register_tag('embed', Sourcerer::Jekyll::Tags::EmbedTag)
