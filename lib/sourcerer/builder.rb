# lib/sourcerer/builder.rb
# frozen_string_literal: true

require 'asciidoctor'
require 'fileutils'
require_relative '../sourcerer'

module Sourcerer
  module Builder
    # generates an entire Ruby file at build-time, to be read during build only
    def self.generate_prebuild generated: {}, attributes: [], snippets: [], regions: [], templates: []
      attr_result     = build_attributes(attributes)
      snippet_lookup  = build_outputs(snippets, type: :snippet)
      region_lookup   = build_outputs(regions, type: :region)

      File.write("#{generated[:path]}", <<~RUBY)
        # frozen_string_literal: true
        # Auto-generated by Sourcerer::Builder

        module #{generated[:module]}
          ATTRIBUTES = #{attr_result.inspect}

          SNIPPET_LOOKUP = #{snippet_lookup.inspect}

          REGION_LOOKUP = #{region_lookup.inspect}

          def self.read_built_snippet name
            fname = SNIPPET_LOOKUP[name.to_s] || name.to_s
            path = File.expand_path("../../../build/snippets/\#{fname}", __FILE__)
            raise "Snippet not found: \#{name}" unless File.exist?(path)
            File.read(path)
          end
        end
      RUBY
    end

    private

    def self.build_attributes attributes
      attributes.each_with_object({}) do |entry, acc|
        source = entry[:source]
        name   = entry[:name] || File.basename(source, '.adoc').to_sym
        acc[name.to_sym] = Sourcerer.load_attributes(source)
      end
    end
    
    def self.build_outputs entries, type:
      lookup   = {}
      names    = []
      outnames = []
    
      entries.each do |entry|
        source = entry[:source] or raise ArgumentError, "#{type} entry is missing :source"
        tag    = entry[:tag]
        tags   = entry[:tags]

        raise ArgumentError, "use only one of :tag or :tags" if tag && tags
        raise ArgumentError, "#{type} must include a :tag or :tags" unless tag || tags

        name    = entry[:name] || tag || File.basename(source, '.adoc')
        outname = entry[:out]  || default_output_name(name, type)
    
        raise ArgumentError, "name value must be unique; #{name} already used"   if names.include? name
        raise ArgumentError, "out value must be unique; #{outname} already used" if outnames.include? outname
        names    << name
        outnames << outname
    
        tags = [tag] if tag
    
        text =
          case type
          when :snippet then Sourcerer.load_include(source, tags: tags)
          when :region  then Sourcerer.extract_tagged_content(source, tags: tags)
          else raise ArgumentError, "Unsupported type: #{type}"
          end
    
        lookup[name.to_s] = outname
    
        outpath = File.join("build/#{type}s", outname)
        FileUtils.mkdir_p File.dirname(outpath)
        File.write(outpath, text)
      end
    
      lookup
    end

    def self.default_output_name name, type
      case type
      when :snippet then "#{name}.txt"
      when :region  then "#{name}.adoc"
      else raise ArgumentError, "Unknown type: #{type}"
      end
    end
  end
end
