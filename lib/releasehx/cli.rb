# frozen_string_literal: true
require 'yaml'
require 'json'
require 'fileutils'
require_relative '../releasehx'

module ReleaseHx
  class CLI < Thor
    def self.exit_on_failure?
      true
    end

    # ---------------------------------------
    # OVERRIDE .start to handle no-arguments
    # and default subcommand behavior
    # ---------------------------------------
    def self.start original_args=ARGV, config={}
      # If user gave no arguments at all, or only gave --manpage, allow that through
      if original_args.empty? or (original_args.length == 1 && original_args.first =~ /^--?h(elp)?$/)
        show_usage_snippet
        return
      elsif original_args.length == 1 && original_args.first =~ /^--man(page)?$/
        show_manpage
        return
      else
        first = original_args[0]
        unless first.start_with?('-') || all_tasks.key?(first)
          original_args.unshift 'default'
        end
      end

      super(original_args, config)
    end

    default_task :default
  
    desc 'VERSION|PATH [options]', ReleaseHx.attrs['tagline']
    # Options
    method_option :md,    type: :string, banner: '[PATH]',
      desc: ReleaseHx.attrs['cli_option_message_md']
    method_option :adoc,  type: :string, aliases: ['--ad'], banner: '[PATH]', desc: ReleaseHx.attrs['cli_option_message_adoc']
    method_option :yaml,  type: :string, aliases: ['--yml'], banner: '[PATH]',
      desc: ReleaseHx.attrs['cli_option_message_yaml']
    method_option :html,  type: :string, banner: '[PATH]',
      desc: ReleaseHx.attrs['cli_option_message_html']
    method_option :pdf,   type: :string, banner: '[PATH]',
      desc: ReleaseHx.attrs['cli_option_message_pdf']
    method_option :json,  type: :string, banner: 'PATH',
      desc: ReleaseHx.attrs['cli_option_message_json']
    method_option :config, type: :string, banner: 'PATH',
      desc: ReleaseHx.attrs['cli_option_message_config']
    method_option :mapping, type: :string, banner: 'PATH',
      desc: ReleaseHx.attrs['cli_option_message_mapping']
    method_option :fetch,  type: :boolean,
      desc: ReleaseHx.attrs['cli_option_message_fetch']
    method_option :append, type: :boolean,
      desc: ReleaseHx.attrs['cli_option_message_append']
    method_option :over,   type: :boolean, aliases: ['--force'],
      desc: ReleaseHx.attrs['cli_option_message_over']
    method_option :check,  type: :boolean, aliases: ['--scan'],
      desc: ReleaseHx.attrs['cli_option_message_check']
    method_option :emptynotes, type: :string, aliases: ['-e'], banner: '[RULE]',
      desc: ReleaseHx.attrs['cli_option_message_emptynotes']
    method_option :internal, type: :boolean,
      desc: ReleaseHx.attrs['cli_option_message_internal']
    method_option :wrap, type: :boolean, default: true,
      desc: ReleaseHx.attrs['cli_option_message_wrap']
    method_option :frontmatter, type: :boolean, default: true, desc: ReleaseHx.attrs['cli_option_message_frontmatter']
    method_option :verbose, type: :boolean,
      desc: ReleaseHx.attrs['cli_option_message_verbose']
    method_option :debug,   type: :boolean,
      desc: ReleaseHx.attrs['cli_option_message_debug']
    method_option :quiet,  type: :boolean,
      desc: ReleaseHx.attrs['cli_option_message_quiet']
    method_option :manpage, type: :boolean, aliases: ['--man'],
      desc: ReleaseHx.attrs['cli_option_message_manpage']
      
    def default source_arg
      setup_logger
      ReleaseHx.logger.info "Invoked default task with version/source=#{source_arg}"
      if options[:config] && !File.exist?(options[:config])
        raise Thor::Error, "ERROR: Configuration declared but not found: #{options[:config]}"
      end
      config_file = options[:config] || ReleaseHx.attrs['app_default_config_path']
      ReleaseHx.logger.info "Arguing for (and found) config file #{config_file}" if options[:config]
      @settings = ReleaseHx::Configuration.load(config_file).settings
      if options[:debug]
        begin
          config_dump = SgymlHelpers.deep_stringify_safe(@settings).to_yaml
          ReleaseHx.logger.debug "Operative config settings:\n" + config_dump
        rescue StandardError => e
          require 'pp'
          ReleaseHx.logger.debug "Rescued config PP dump:\n" + PP.pp(@settings, +'')
          raise e
        end
      end

      source_arg_type = version_or_file(source_arg)
      if source_arg_type == :invalid
        raise Thor::Error, <<~ERRTXT
          ERROR: Invalid file extension for source file: #{source_arg}
                 Valid draft file types are: #{ReleaseHx.attrs['draft_source_file_types']}
                 Valid extensions are: #{ReleaseHx.attrs['draft_source_extensions']}
        ERRTXT
      end
      ReleaseHx.logger.info "Source type is: #{source_arg_type}"
      ReleaseHx.logger.info "API is: #{@settings['api']['from']}"
    
      if options[:check]
        raise Thor::Error, "ERROR: Scan operations require a version number as the first argument." if source_arg_type == :file
        perform_scan(source_arg)
        return
      end

      if options[:manpage]
        system 'man build/docs/releasehx.1'
        return
      end
    
      if options[:json] && (options[:json].nil? || options[:json].empty?)
        raise Thor::Error, 'Must specify a PATH for --json. E.g. --json release-1-1-1.json'
      end

      if [options[:adoc], options[:md], options[:yaml]].compact.size > 1
        raise Thor::Error, 'ERROR: Only one of --adoc, --md, or --yaml (or aliases) may be specified.'
      end

      determine_operations(source_arg)
    end
      
    private             

    def setup_logger
      # This ensures the global logger references the same object we configured
      log = ReleaseHx.logger
    
      if options[:debug]
        log.level = Logger::DEBUG
      elsif options[:verbose]
        log.level = Logger::INFO
      elsif options[:quiet]
        log.level = Logger::ERROR
      else
        log.level = Logger::WARN
      end
    end

    def self.show_usage_snippet
      usage_text = ReleaseHx.read_built_snippet(:helpscreen)
      puts "\nVersion: #{ReleaseHx::ATTRIBUTES[:globals]['this_prod_vrsn']}"
      puts usage_text
      puts
    end

    def self.show_manpage
      system_manfile = '/usr/local/share/man/man1/releasehx.1'
      gem_manfile    = File.expand_path('../../build/docs/releasehx.1', __dir__)
    
      unless File.exist?(gem_manfile)
        warn "Man file not found in gem: #{gem_manfile}"
        return
      end
      display_manpage_locally(gem_manfile)
    end
    
    def self.display_manpage_locally manfile
      tmp_dir = File.join(Dir.home, '.rhxtmp')
      FileUtils.mkdir_p(tmp_dir) unless Dir.exist?(tmp_dir)
    
      tmp_path = File.join(tmp_dir, 'releasehx.1')
      FileUtils.cp(manfile, tmp_path)
    
      # Use -l to display local file
      system("man -l '#{tmp_path}'")
    
      # Optionally remove after display
      FileUtils.rm_f(tmp_path)
    end

    # Determine whether first argument is a version # or a proper file path
    def version_or_file source_arg
      if source_arg.end_with?(ReleaseHx.attrs['draft_source_extensions'].split(', ').join(','))
        :file
      elsif source_arg.end_with?('.html', '.pdf')
        :invalid
      else
        :version
      end
    end

    def perform_scan version
      ReleaseHx.logger.info("Scanning for missing release notes in version #{version}")

      source_type  = determine_payload_type(version)
      payload      = fetch_or_load_issues(version, source_type)
      mapping      = load_mapping
      release_code = basename_for_output(version)
      release_date = Date.today
      issues_count = payload.is_a?(Array) ? payload.size : payload['issues']&.size || payload.size
      
      emptynotes_arg = options[:emptynotes]
    
      # Interpret CLI override for --empty/-e
      if emptynotes_arg
        if %w[skip empty dump ai].include?(emptynotes_arg)
          # override setting
          @settings['rhyml']['empty_notes'] = emptynotes_arg
        else
          # Toggle logic
          current = @settings.dig('rhyml', 'empty_notes') || 'skip'
          @settings['rhyml']['empty_notes'] = current == 'skip' ? 'empty' : 'skip'
        end
      end

      require_relative 'ops/check_ops'
    
      release = ReleaseHx::DraftOps.from_payload(
        payload: payload,
        config: @settings,
        mapping: mapping,
        release_code: release_code,
        release_date: release_date,
        scan: true
      )
    
      ReleaseHx::CheckOps.print_check_summary(release, issues_count)
      return
    end

    def check_for_existing_draft version
      # Check for existing draft files in drafts_dir
      # If found, prompt user to confirm overwrite
    end

    def determine_operations source_arg
      operation_mode = determine_mode(source_arg)
    
      case operation_mode
      when :draft_only
        ReleaseHx.logger.info("Generating draft only.")
        generate_draft(source_arg)
      when :render_only
        ReleaseHx.logger.info("Rendering document/s only.")
        render_docs(source_arg, source_arg)
      when :draft_and_render
        ReleaseHx.logger.info("Generating draft and rendering docs.")
        generate_draft(source_arg)
        render_docs(source_arg, source_arg)
      else
        raise Thor::Error, "ERROR: Could not determine valid operation mode."
      end
    end
    
    def determine_mode source_arg
      return :render_only if version_or_file(source_arg) == :file && render_requested?
      return :draft_and_render if options[:fetch] && render_requested?
      return :draft_and_render if draft_requested? && render_requested?
      return :draft_only if options[:fetch] || draft_requested?
    
      raise Thor::Error, "ERROR: You must specify a draft or render format."
    end
    
    # Returns true if any draft format flags exist
    def draft_requested?
      !options[:md].nil? || !options[:adoc].nil? || !options[:yaml].nil?
    end

    # Returns true if any render format flags exist
    def render_requested?
      !options[:html].nil? || !options[:pdf].nil?
    end

    def determine_input_type version_arg
      if File.file?(version_arg)
        return { type: :file, format: file_format(version_arg), path: version_arg }
      end
    
      # It's a version number. Try resolving a usable draft.
      existing = find_existing_drafts(version_arg)
      return { type: :draft, format: existing[:format], path: existing[:path] } if existing
    
      { type: :api, format: :api, path: version_arg }
    end

    def basename_for_output source_arg
      if version_or_file(source_arg) == :file
        File.basename(source_arg, File.extname(source_arg))
      else
        source_arg
      end
    end    
    
    def find_existing_drafts version
      base = File.join(@settings.paths['drafts_dir'], version)
    
      %w[adoc md yml].each do |ext|
        path = "#{base}.#{ext}"
        return { format: ext.to_sym, path: path } if File.exist?(path)
      end
    
      nil
    end
    
    def generate_draft source_arg
      source_type = determine_payload_type(source_arg)
      issues      = fetch_or_load_issues(source_arg, source_type)
      mapping     = load_mapping
      version     = basename_for_output(source_arg) # used only for filenames and template context
    
      release = ReleaseHx::DraftOps.from_payload(
        payload: issues,
        config:  @settings,
        mapping: mapping,
        release_code: version, # Still passed here so RHYML::Release can use it
      )
    
      fmt = draft_format_requested
      outpath = resolve_draft_path(fmt, version)
    
      if File.exist?(outpath) && !options[:over]
        ReleaseHx.logger.warn "File exists: #{outpath}. Use --force to overwrite."
        return
      end
    
      ReleaseHx::DraftOps.write_draft(
        release:     release,
        config:      @settings,
        format:      fmt,
        output_path: outpath
      )
    end

    def resolve_draft_path flag, version
      user_path = options[flag]
      return user_path if user_path && !user_path.empty? && user_path != flag.to_s
    
      ext = case flag
            when :yaml then @settings.dig('extensions', 'yaml') || 'yml'
            when :md    then @settings.dig('extensions', 'markdown') || 'md'
            when :adoc  then @settings.dig('extensions', 'asciidoc') || 'adoc'
            else raise ArgumentError, "Unknown draft format: #{flag}"
            end
    
      template_obj = @settings.dig('paths', 'draft_filename')
      filename     = SchemaGraphy::Templating::Engines.render(template_obj, 'liquid', { 'version' => version, 'format_ext' => ext })
    
      drafts_dir = @settings.dig('paths', 'drafts_dir') || 'drafts'
      File.join(drafts_dir, filename.strip)
    end        

    def draft_format_requested
      return :yaml if options[:yaml]
      return :md   if options[:md]
      return :adoc if options[:adoc]
      nil
    end    

    def derive_release_code arg
      return File.basename(arg).split('.').first if arg.include?('/')
      return arg unless arg.end_with?('.yaml', '.yml', '.json', '.adoc', '.md')
      File.basename(arg, File.extname(arg))
    end    

    def load_mapping
      if options[:mapping]
        path = options[:mapping]
        raise Thor::Error, "Mapping file not found: #{path}" unless File.exist?(path)
        return SchemaGraphy::Loader.load_yaml_with_tags(path)
      else
        api = @settings.dig('api', 'from') || 'default'
        local_dir = @settings.dig('paths', 'custom_mappings_dir') || '_mappings'
        local_path = File.join(local_dir, "#{api}.yaml")
    
        return SchemaGraphy::Loader.load_yaml_with_tags(local_path) if File.exist?(local_path)
    
        # Fallback to built-in mapping shipped with gem
        gem_root = File.expand_path('../../../..', __dir__) # adjust if needed
        built_in_path = File.join(gem_root, '_mappings', "#{api}.yaml")
    
        raise Thor::Error, "No mapping file found for API '#{api}' in local or gem paths." unless File.exist?(built_in_path)
        SchemaGraphy::Loader.load_yaml_with_tags(built_in_path)
      end
    end

    def determine_payload_type identifier
      return :yaml if identifier.end_with?('.yml', '.yaml')
      return :json if options[:json]
      :api
    end

    def render_template(template_path, context)
      engine = Tilt.new(template_path)
      engine.render(Object.new, context)
    end

    def fetch_or_load_issues identifier, source_type
      if source_type == :yaml
        load_yaml_issues(identifier)
      elsif source_type == :json
        load_json_issues(options[:json])
      else
        fetch_issues_from_api(identifier)
      end
    end

    def generate_requested_outputs version, issues, source_type
      if options[:yaml]
        output_yaml(version, issues)
      elsif options[:md]
        output_markdown(version, issues, source_type)
      elsif options[:adoc]
        output_asciidoc(version, issues, source_type)
      end

      render_docs(source_arg, version)
    end

    def render_docs source_path, version
      file = find_existing_draft(version)
      unless file
        ReleaseHx.logger.warn("No draft found for rendering.")
        return
      end
    
      if options[:html]
        html_out = resolve_render_path(:html, version, render_mode: true)
        ReleaseHx::RenderOps.render_from_file file, format: :html, output_path: html_out, config: @settings
      end
    
      if options[:pdf]
        pdf_out = resolve_render_path(:pdf, version, render_mode: true)
        ReleaseHx::RenderOps.render_from_file file, format: :pdf, output_path: pdf_out, config: @settings
      end
    end
    
    def resolve_render_path flag, version
      ext = case flag
            when :pdf  then 'pdf'
            when :html then 'html'
            else raise ArgumentError, "Unknown render format: #{flag}"
            end
    
      template_obj = @settings.dig('paths', 'publish_filename')
      filename     = SchemaGraphy::Templating::Engines.render(template_obj, 'liquid', { 'version' => version, 'format_ext' => ext })
    
      publish_dir = @settings.dig('paths', 'publish_dir') || 'site'
      File.join(publish_dir, filename.strip)
    end       

    def find_existing_draft version
      ["#{version}.adoc", "#{version}.md", "#{version}.yml"].find { |f| File.exist?(f) }
    end

    def output_yaml path, issues
      yaml_content = "---\n#YAML Draft" # placeholder
      safe_write(path, yaml_content)
    end
    
    def output_markdown path, issues, source_type
      markdown_content = "# Markdown Draft\n\n" # placeholder
      safe_write(path, markdown_content)
    end
    
    def output_asciidoc path, issues, source_type
      asciidoc_content = "= AsciiDoc Draft\n\n" # placeholder
      safe_write(path, asciidoc_content)
    end    

    def safe_write filename, content
      ReleaseHx.logger.debug("Attempting to write file #{filename}")
      if File.exist?(filename) && !options[:over]
        ReleaseHx.logger.warn("#{filename} already exists. Use --force to overwrite.")
      else
        File.write(filename, content)
        ReleaseHx.logger.info("Draft written: #{filename}")
      end
    end

    def load_yaml_issues path
      ReleaseHx.logger.info("Loading YAML issues from #{path}")
      SchemaGraphy::Loader.load_yaml_with_tags(path)
    end

    def load_json_issues path
      ReleaseHx.logger.info("Loading JSON issues from #{path}")
      JSON.parse(File.read(path))
    end

    def fetch_issues_from_api version
      ReleaseHx.logger.info("Fetching issues for version #{version} from API")
      # Placeholder logic for adapters
      []
    end
  end
end