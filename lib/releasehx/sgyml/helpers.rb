module ReleaseHx
  module SgymlHelpers

    # def self.precompile_config_templates! config_data, schema_data
    #   return unless config_data['templates'].is_a?(Hash)
    #   templates = config_data['templates']
    #   schema = schema_data['$schema'] || schema_data

    #   templated_keys = templates.keys.select do |key|
    #     type = SchemaGraphy::SchemaUtils.type_for(schema, "templates.#{key}")
    #     %w[Template Liquid ERB].include?(type.to_s)
    #   end

    #   SchemaGraphy::Templating.compile_tagged_templated_fields!(data: templates, fields: templated_keys)
    # end

    def self.precompile_from_schema! data, schema, base_path='', scope: {}
      SchemaGraphy::Templating.precompile_from_schema!(data, schema, base_path, scope: scope)
    end

    def self.render_stage_fields! data, stage
      data.each do |key, value|
        next unless value.is_a?(SchemaGraphy::Templating::Templated<PERSON>ield)
    
        tmpl_context = value.context
        next unless tmpl_context.respond_to?(:stage)
        next unless tmpl_context.stage.to_sym == stage.to_sym
    
        data[key] = value.render
      end
    end

    def self.deep_stringify_safe obj
      case obj
      when Hash
        obj.each_with_object({}) do |(k,v), h|
          h[k.to_s] = deep_stringify_safe(v)
        end
      when Array
        obj.map { |v| deep_stringify_safe(v) }
      else
        begin
          obj.to_yaml
          obj
        rescue TypeError
          obj.to_s
        end
      end
    end
    
  end
end
