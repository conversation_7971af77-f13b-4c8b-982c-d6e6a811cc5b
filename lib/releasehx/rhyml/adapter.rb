require 'jsonpath'
require 'jmespath'
require 'liquid'
require 'erb'
require 'yaml'
require 'json'
require_relative '../../schemagraphy'
require_relative '../../sourcerer/jekyll'

module ReleaseHx
  module RHYML

    # VERB_PAST_TENSES defined below

    class Adapter
      SCHEMA_PATH = File.expand_path('../../../specs/rhyml-mapping-schema.yaml', __dir__)
      MAPPING_SCHEMA = SchemaGraphy::Loader.load_yaml_with_tags(SCHEMA_PATH)['$schema']
      SKIP_KEYS = %w[_meta _config changes_array_path].freeze

      def initialize mapping:, config:
        @mapping = mapping
        @config = config
        @defaults = load_defaults
      end

      def to_release payload, release_code:, release_date: nil, release_hash: nil, release_memo: nil, scan: false
        ReleaseHx.logger.debug "Adapter.to_release called (scan = #{scan})"
        array_path = mapping['changes_array_path']
        raw_items = resolve_path(array_path, payload)

        ReleaseHx.logger.debug "Extracted raw_items (#{raw_items.size}) from path '#{array_path}'"
        ReleaseHx.logger.debug "First raw item: #{raw_items.first.inspect}"

        release = Release.new(
          code: release_code,
          date: release_date,
          hash: release_hash,
          memo: release_memo,
          changes: []
        )

        ReleaseHx.logger.debug "Mapping #{raw_items.size} raw items..."

        changes = raw_items.map { |raw| transform_change(raw, release: release, scan: scan) }.compact

        if changes.empty?
          ReleaseHx.logger.warn "All mapped changes were nil after transformation. No changes attached to release #{release_code}."
        else
          with_notes = changes.count { |c| c.note.to_s.strip != '' }
          ReleaseHx.logger.info "Transformed #{changes.size} changes for release #{release_code} (#{with_notes} with notes)"
        end

        ReleaseHx.logger.debug "Adding #{changes.size} changes to release" if scan
        ReleaseHx.logger.debug "First change keys: #{changes.first.to_h.keys.inspect}" if scan && changes.any?
        release.instance_variable_set(:@changes, changes)

        release
      end

      private

      attr_reader :mapping, :config, :defaults

      def load_defaults
        {
          'path_lang' => SchemaGraphy::SchemaUtils.default_for(MAPPING_SCHEMA, '_config.path_lang') || 'jmespath',
          'tplt_lang' => SchemaGraphy::SchemaUtils.default_for(MAPPING_SCHEMA, '_config.tplt_lang') || 'liquid'
        }
      end

      def transform_change raw, release:, scan: false
        mapped = map_single_change(raw, release: release)
        shaped = postprocess(mapped, scan: scan)

        ReleaseHx.logger.debug "Mapped: #{mapped.inspect}"
        ReleaseHx.logger.debug "Postprocessed: #{shaped.inspect}"

        if shaped.nil?
          ReleaseHx.logger.debug "Change dropped after postprocess: #{mapped.inspect}"
          return nil
        end


        Change.new(shaped, release: release)
      rescue => e
        ReleaseHx.logger.warn "Change transform error: #{e.class}: #{e.message}"
        ReleaseHx.logger.debug e.backtrace.join("\n")
        nil
      end

      def map_single_change raw, release:
        result = {}
        path_lang = mapping.dig('_config', 'path_lang') || defaults['path_lang']
        context = { 'config' => @config }

        mapping.each do |key, defn|
          next if SKIP_KEYS.include?(key) || key.start_with?('_') || defn.nil?

          # STEP 1: Render Path Expression if templated
          path_expr = render_if_templated(defn['path'], context, key, 'path')

          # STEP 2: Extract value via path (JMESPath or JSONPath)
          extracted_value = extract_value(raw, path_expr, path_lang)

          # STEP 3: If there's a template, render it using extracted value
          if defn['tplt']
            extracted_context = { 'path' => extracted_value }
            rendered_value = render_if_templated(defn['tplt'], extracted_context, key, 'tplt')
            result[key] = rendered_value
          else
            result[key] = extracted_value
          end

          # Convert verbs to past tense if configured
          if (key == 'head' && config.dig('rhyml', 'pasterize_head')) || (key == 'summ' && config.dig('rhyml', 'pasterize_summ'))
            result[key] = ReleaseHx::RHYML.pasterize(result[key])
          end
        end

        # STEP 4: Generate chid if a template is provided
        chid_template = config.dig('rhyml', 'chid')
        if chid_template
          ctx = {
            'change' => result,
            'release' => {
              'code' => release.code,
              'date' => release.date,
              'hash' => release.hash
            }
          }

          initialize_liquid_filters

          # Handle both string and TemplatedField objects
          if chid_template.respond_to?(:templated?) && chid_template.respond_to?(:render)
            # It's already a TemplatedField object
            mapped_chid = chid_template.render(ctx)
          else
            # It's a string, parse and render it
            template = Liquid::Template.parse(chid_template.to_s)
            mapped_chid = template.render(
              ctx,
              filters: [::ReleaseHx::RHYML::RhymlFilters, ::Sourcerer::Jekyll::Liquid::Filters]
            )
          end

          result['chid'] = mapped_chid.strip unless mapped_chid.to_s.strip.empty?
        end

        result
      end

      def render_if_templated template_def, context, key, field_type
        return template_def unless template_def.is_a?(String) || (template_def.is_a?(Hash) && template_def['value'])

        engine = defaults['tplt_lang'] || 'liquid'
        raw_tpl = template_def.is_a?(Hash) && template_def['__tag__'] ? template_def['value'] : template_def

        case engine
        when 'liquid'
          initialize_liquid_filters
          template = ::Liquid::Template.parse(raw_tpl)
          return template.render(context)
        when 'erb'
          compiled = ERB.new(raw_tpl)
          return compiled.result_with_hash(context)
        else
          raise "Unsupported template engine: #{engine}"
        end
      rescue => e
        raise "Error rendering '#{field_type}' template for '#{key}': #{e.message}"
      end

      def extract_value data, path, lang
        return nil unless path.is_a?(String) && !path.empty?

        case lang.downcase
        when 'jmespath'
          JMESPath.search(path, data)
        when 'jsonpath'
          JsonPath.new(path).on(data)
        else
          raise "Unsupported path interpreter: #{lang}"
        end
      rescue => e
        ReleaseHx.logger.error "Path extraction error (#{lang}): '#{path}' – #{e.message}"
        nil
      end

      def resolve_path expr, data, override_lang = nil
        engine = (override_lang || mapping.dig('_config', 'path_lang') || defaults['path_lang']).downcase
        extract_value(data, expr, engine)
      end

      def postprocess data, scan: false
        ReleaseHx.logger.debug "Entering postprocess with scan=#{scan}"
        ReleaseHx.logger.debug "Data before compact: #{data.inspect}"

        data.compact!
        sources = SchemaGraphy::TagUtils.detag(config['sources']) || {}
        templates = SchemaGraphy::TagUtils.detag(config['rhyml']) || {}

        note_pattern = sources['note_pattern'] || templates['note_pattern']
        head_pattern = sources['head_pattern'] || templates['head_pattern']
        head_source = sources['head']

        # Convert string patterns to Regexp objects when needed
        if data['note'] && note_pattern.is_a?(String)
          # Handle /pattern/flags format or plain string
          pattern_str = note_pattern.match(/\A\/(.+)\/[gimxo]*\z/) ? $1 : note_pattern
          flags = note_pattern.match(/\A\/.+\/([gimxo]*)\z/) ? $1 : ''

          options = 0
          options |= Regexp::IGNORECASE if flags.include?('i')
          options |= Regexp::MULTILINE if flags.include?('m')

          begin
            note_regexp = Regexp.new(pattern_str, options)
            match = data['note'].match(note_regexp)
            data['note'] = match[:note].strip if match&.[](:note)
          rescue RegexpError => e
            ReleaseHx.logger.warn "Invalid note_pattern '#{note_pattern}': #{e.message}"
          end
        end

        if head_source =~ /release_note_heading/i && data['note'] && head_pattern.is_a?(String)
          # Handle /pattern/flags format or plain string
          pattern_str = head_pattern.match(/\A\/(.+)\/[gimxo]*\z/) ? $1 : head_pattern
          flags = head_pattern.match(/\A\/.+\/([gimxo]*)\z/) ? $1 : ''

          options = 0
          options |= Regexp::IGNORECASE if flags.include?('i')
          options |= Regexp::MULTILINE if flags.include?('m')

          begin
            head_regexp = Regexp.new(pattern_str, options)
            match = data['note'].match(head_regexp)
            if match&.[](:head)
              data['head'] = match[:head].strip
              data['note'].sub!(match[0], '').strip!
            end
          rescue RegexpError => e
            ReleaseHx.logger.warn "Invalid head_pattern '#{head_pattern}': #{e.message}"
          end
        end

        data['tags'] = process_tags(data['tags'], data['note'])

        ReleaseHx.logger.debug "Evaluating skip logic for: #{data['tick']}" if scan

        skip_change?(data, scan: scan) ? nil : data
      end

      def process_tags tags, note
        all_tags = Array(tags).map(&:downcase)
        checkbox_tags = note.to_s.scan(/^- \[x\] (\w[\w-]{1,25})/i).flatten.map(&:downcase)
        (all_tags + checkbox_tags)
          .uniq
          .map { |slug| tag_slug_map[slug] }
          .compact
      end

      # skip conditions:
      # explicitly marked with an excluded tag
      # has 'release_note_needed' and lacks a note and (no -e override or permissive empty_notes policy)
      # has no note and no tag from _include
      def skip_change? data, scan: false
        tags         = Array(data['tags']).map(&:downcase)
        note_present = data['note'].to_s.strip != ''
        tag_config   = @config['tags'] || {}
        empty_note_policy = @config.dig('rhyml', 'empty_notes') || 'skip'

        # 1. Always exclude if blacklisted
        if (tags & Array(tag_config['_exclude'])).any?
          ReleaseHx.logger.debug "Skipping change due to blacklisted tag" if scan
          return true
        end
        # 2. Keep if note exists
        if note_present
          ReleaseHx.logger.debug "Logging change due to note present" if scan
          return false
        end
        # 3. Keep if tagged with anything in _include
        if (tags & Array(tag_config['_include'])).any?
          ReleaseHx.logger.debug "Logging change due to tag in _include" if scan
          return false
        end

        # 4. If release_note_needed is present but no note
        if tags.include?('release_note_needed')
          unless empty_note_policy == 'skip' # keep it
            ReleaseHx.logger.debug "Logging change due to release_note_needed and empty_notes policy"
            return false
          else
            ReleaseHx.logger.debug "Skipping change due to release_note_needed and empty_notes policy"
            return true
          end
        end

        # 5. Nothing triggered inclusion, skip it
        ReleaseHx.logger.debug "Skipping change due to no note and no tag in _include" if scan
        true
      end

      def tag_slug_map
        @tag_slug_map ||= begin
          tag_defs = config['tags'] || {}
          tag_defs.each_with_object({}) do |(key, value), memo|
            next if %w[_include _exclude].include?(key)
            slug = value.is_a?(Hash) ? (value['slug'] || key) : key
            memo[slug] = key
          end
        end
      end

      private

      def initialize_liquid_filters
        return if defined?(@__liquid_ready) && @__liquid_ready

        ::Sourcerer::Jekyll.initialize_liquid_runtime
        ::Liquid::Template.register_filter(::ReleaseHx::RHYML::RhymlFilters)

        @__liquid_ready = true
      end



    end

    VERB_PAST_TENSES = {
      'add'       => 'added',
      'adds'      => 'added',
      'address'   => 'addressed',
      'addresses' => 'addressed',
      'build'     => 'built',
      'builds'    => 'built',
      'change'    => 'changed',
      'changes'   => 'changed',
      'clarify'   => 'clarified',
      'clarifies' => 'clarified',
      'clean'     => 'cleaned',
      'cleans'    => 'cleaned',
      'configure' => 'configured',
      'configures'=> 'configured',
      'correct'   => 'corrected',
      'corrects'  => 'corrected',
      'create'    => 'created',
      'creates'   => 'created',
      'deprecate' => 'deprecated',
      'deprecates'=> 'deprecated',
      'document'  => 'documented',
      'documents' => 'documented',
      'downgrade' => 'downgraded',
      'downgrades'=> 'downgraded',
      'enhance'   => 'enhanced',
      'enhances'  => 'enhanced',
      'fix'       => 'fixed',
      'fixes'     => 'fixed',
      'implement' => 'implemented',
      'implements'=> 'implemented',
      'improve'   => 'improved',
      'improves'  => 'improved',
      'initialize'=> 'initialized',
      'initializes'=> 'initialized',
      'launch'    => 'launched',
      'launches'  => 'launched',
      'make'      => 'made',
      'makes'     => 'made',
      'merge'     => 'merged',
      'merges'    => 'merged',
      'optimize'  => 'optimized',
      'optimizes' => 'optimized',
      'patch'     => 'patched',
      'patches'   => 'patched',
      'refactor'  => 'refactored',
      'refactors' => 'refactored',
      'refine'    => 'refined',
      'refines'   => 'refined',
      'remove'    => 'removed',
      'removes'   => 'removed',
      'rename'    => 'renamed',
      'renames'   => 'renamed',
      'revert'    => 'reverted',
      'reverts'   => 'reverted',
      'support'   => 'supported',
      'supports'  => 'supported',
      'test'      => 'tested',
      'tests'     => 'tested',
      'tweak'     => 'tweaked',
      'tweaks'    => 'tweaked',
      'update'    => 'updated',
      'updates'   => 'updated',
      'upgrade'   => 'upgraded',
      'upgrades'  => 'upgraded'
    }

    def self.pasterize input
      input.gsub(/\b(\w+)\b/) do |word|
        replacement = VERB_PAST_TENSES[word.downcase]
        next word unless replacement

        # Preserve casing
        if word == word.upcase
          replacement.upcase
        elsif word == word.capitalize
          replacement.capitalize
        else
          replacement
        end
      end
    end

  end
end
