module ReleaseHx
  module RHYML
    
    class Change
      attr_reader :vrsn, :chid, :tick, :hash, :type, :parts,
                  :summ, :head, :note, :tags,
                  :lead, :auths, :links, :version
      attr_accessor :version

      def initialize attrs={}, release:
        raise ArgumentError, "attrs must be a Hash" unless attrs.is_a? Hash
        @release = release
        @vrsn    = @release.code
        @chid    = attrs['chid']
        @tick    = attrs_value(attrs, %w[tick ticketid])
        @hash    = attrs['hash']
        @type    = attrs['type']
        @summ    = attrs_value(attrs, %w[summ summary title])
        @head    = attrs['head']
        @note    = attrs['note']
        @tags    = attrs['tags'] || []
        @lead    = attrs_value(attrs, %w[lead contributor auth])
        @auths   = normalize_auths(attrs['auths'])
        @links   = normalize_links(attrs['links'])
        
        part  = attrs['part']
        parts = attrs['parts']
        if part && parts
          raise ArgumentError, "Change cannot have both 'part' and 'parts'"
        end
        @parts = if parts
          Array(parts).map(&:to_s)
        elsif part
          [part.to_s]
        else
          []
        end

        ReleaseHx.logger.debug "Initialized Change: #{@tick} – #{@summ}"
      end

      def to_h
        {
          'vrsn' => vrsn,
          'chid' => chid,
          'tick' => tick,
          'hash' => hash,
          'type' => type,
          'parts' => parts,
          'summ' => summ,
          'head' => head,
          'note' => note,
          'tags' => tags,
          'lead' => lead,
          'auths' => auths,
          'links' => links,
          'deprecation' => deprecation?,
          'removal' => removal?,
          'highlight' => highlight?,
          'breaking' => breaking?,
          'experimental' => experimental?
        }
      end

      def highlight?     = tags.include?('highlight')
      def breaking?     = tags.include?('breaking')
      def experimental? = tags.include?('experimental')
      def deprecation?  = tags.include?('deprecation')
      def removal?      = tags.include?('removal')

      def has_tag? tag_name
        tags.include?(tag_name) || tags.include?(tag_name.to_s) || tags.include?(tag_name.to_sym)
      end

      private

      # Retrieve the first available attribute value from a list of keys
      def attrs_value attrs, keys
        keys.find { |key| return attrs[key] if attrs.key?(key) }
        nil
      end

      # Normalize the 'auths' attribute to ensure consistent structure
      def normalize_auths val
        return [] if val.nil?
        val.map do |a|
          {
            'user' => a['user'] || a[:user],
            'memo' => a['memo'] || a[:memo]
          }.compact
        end
      end

      # Normalize the 'links' attribute to ensure consistent structure
      def normalize_links val
        return [] if val.nil?
        val.map do |l|
          {
            'text' => l['text'] || l[:text],
            'xref' => l['xref'] || l[:xref],
            'href' => l['href'] || l[:href]
          }.compact
        end
      end
    end

  end
end