module ReleaseHx
  module RHYML

    class Release
      attr_reader :code, :date, :hash, :memo, :changes

      def initialize code:, date: nil, hash: nil, memo: nil, changes: []
        @code    = code
        @date    = date
        @hash    = hash
        @memo    = memo
        @changes = Array(changes).map { |ch| init_change(ch) }.compact
        # accepts an Array of Change objects or an Array of Hashes
        # Can be called like:
        # Release.new(
        #   code: '2.1.0',
        #   date: '2024-03-30',
        #   changes: [
        #     { 'tick' => 'JIRA-1234', 'summ' => 'Add auth', 'tags' => ['highlight'] },
        #     { 'tick' => 'JIRA-1235', 'summ' => 'Fix bug',  'tags' => ['bug'] }
        #   ]
        # )
        # OR
        # Release.new(
        #   code: '2.1.0',
        #   changes: [Change.new(...), Change.new(...)]
        # )
        ReleaseHx.logger.debug "Release initialized with changes (post-compact):"
        @changes.each_with_index do |ch, i|
          ReleaseHx.logger.debug "  changes[#{i}]: #{ch.class}" unless ch.nil?
        end
        raise "Unexpected nil in changes" if @changes.any?(&:nil?)

      end

      def add_change change
        attach_release(change)
        @changes << change
      end

      def change_count
        changes.size
      end

      def contributors
        changes.map(&:lead).compact.uniq
      end

      def tag_stats
        changes.compact.flat_map { |c| c.tags || [] }.tally
      end

      def to_h
        {
          'code' => code,
          'version' => code, # alias for backward compatibility
          'date' => date,
          'hash' => hash,
          'memo' => memo,
          'tag_stats' => tag_stats,
          'contributors' => contributors
          # purposely not passing changes here
        }.compact
      end

      private

      def init_change ch
        return nil unless ch
      
        if ch.is_a?(Change)
          ch.release = self
          return ch
        elsif ch.is_a?(Hash)
          begin
            obj = Change.new(ch, release: self)
            obj.release = self
            return obj
          rescue => e
            ReleaseHx.logger.warn "Skipping malformed change: #{e.message}"
            return nil
          end
        else
          ReleaseHx.logger.warn "Unknown change type: #{ch.class}"
          return nil
        end
      end
      
      def attach_release change
        change.release = self # you can alias change.release = self too
        change
      end

      # You can add more counting methods as needed
    end

    class History
      # We oddly don't have a use for this class yet, but it is part of the model
      # In RHYML, a complete history is an Array of Release objects.
      # We might do some kind of lookup-release-by-changeid or something, otherwise 
      #  there is little need for this layer to reach further than subordinate 
      #  Release objects.
      attr_reader :releases

      def initialize
        @releases = []
      end

      def add_release(release)
        raise ArgumentError, "release must be a Release object" unless release.is_a? Release
        @releases << release
        ReleaseHx.logger.debug "Added Release: #{release.code} (#{release.date})"
        release
      end

    end

  end
end
