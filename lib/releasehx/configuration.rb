# frozen_string_literal: true

require 'psych'

module ReleaseHx
  # High-level config loader/manager
  class Configuration
    attr_reader :settings

    GEM_GLOBALS = ReleaseHx::ATTRIBUTES[:globals].freeze
    gem_root_path = File.expand_path('../..', __dir__)
    # Path to internal config-def file:
    CONFIG_DEF_PATH = File.join(
      gem_root_path, 
      GEM_GLOBALS['gem_config_definition_path'
    ]).freeze
    # Default path to user's config
    DEFAULT_CONFIG_PATH = File.join(
      File.expand_path('.'),
      GEM_GLOBALS['app_default_config_path'] || 'releasehx.yml'
    ).freeze

    def initialize settings={}
      @settings = settings
    end

    # Entry point to load and merge user config with definition
    #
    # @param user_config_path [String] path to user config
    # @param definition_path [String] path to config-def.yml
    # @return [Configuration] Final merged config object
    def self.load user_config_path=DEFAULT_CONFIG_PATH, definition_path=CONFIG_DEF_PATH
      ReleaseHx.logger.info "Loading configuration from: #{user_config_path}"
      definition = SchemaGraphy::Loader.load_yaml_with_tags(definition_path)
      meta = SchemaGraphy::SchemaUtils.crawl_meta(definition, 'history.head')
      
      user_config = File.exist?(user_config_path) ? SchemaGraphy::Loader.load_yaml_with_tags(user_config_path) : {}
      merged_settings = apply_schema(definition['properties'], user_config)
      config = new(merged_settings)

      ReleaseHx::SgymlHelpers.precompile_from_schema!(
        config.settings,
        definition, # should contain $schema or be the schema
        scope: { 'config' => config.settings }
      )
      ReleaseHx::SgymlHelpers.render_stage_fields!(config.settings, :load)

      templated = SchemaGraphy::SchemaUtils.templated_field?(definition, 'history.head')


      config
    end

    # enable dot format, like config.api.from
    def method_missing name, *args, &block
      # If settings has a key matching the method name, return that sub-hash
      key_str = name.to_s
      return settings[key_str] if settings.key?(key_str)

      super
    end

    def respond_to_missing? name, include_private=false
      settings.key?(name.to_s) || super
    end

    def settings
      # Return the internal settings hash
      @settings
    end

    # Access config fields with bracket syntax:
    #
    #  config[:api][:from]
    #
    # or normal Ruby dot methods
    def [] key
      @settings[key.to_s]
    end


    private

    # Recursively apply defaults; handle nested properties, arrays, etc.
    def self.apply_schema schema_properties, user_hash
      final_hash = {}

      (schema_properties || {}).each do |prop_key, prop_def|
        user_val = user_hash.fetch(prop_key, nil)
        # Skip processing this property if user explicitly set it to $nil
        unless user_val.to_s.strip == '$nil'
          final_val = apply_property(prop_def, user_val)
          final_hash[prop_key] = final_val
        end
      end

      # Preserves extra user-supplied keys that aren't in the definition/schema
      user_hash.each do |unk_key, unk_val|
        final_hash[unk_key] = unk_val unless final_hash.key?(unk_key) || unk_val.to_s.strip == '$nil'
      end

      final_hash
    end

    # Applies a single property definition
    # prop_def is a Hash containing e.g. { "type"=>"String", "dflt"=>"yaml", "properties"=>{...} }
    # user_val is the user config's value for this property
    def self.apply_property prop_def, user_val
      # If user val is literal $nil, return nil to remove this property
      return nil if user_val.to_s.strip == '$nil'

      # If user_val is nil, adopt default
      default_val = prop_def['dflt'] if prop_def.key?('dflt')
      val = user_val.nil? ? default_val : user_val

      # If this prop has "properties", it's a nested object
      if prop_def['properties']
        # val should be a Hash (or nil)
        val_hash = val.is_a?(Hash) ? val : {}
        # recursively handle sub-properties
        val = apply_schema(prop_def['properties'], val_hash)
      # If it's an array type and we have a default array
      elsif prop_def['type'] == 'ArrayList' && val.nil?
        val = default_val || []
      end

      val
    end

    VALIDATION_RULES = [
      {
        scopes: [:fetch],
        message: "'api.href' is required for remote sources",
        check: -> config {
          if %w[jira github gitlab].include?(config.api['from']) && config.api['href'].to_s.empty?
            raise ArgumentError, "'api.href' is required for remote source '#{config.api['from']}'"
          end
        }
      },
      {
        scopes: [:fetch],
        message: "'rhyml' file must exist at api.href",
        check: -> config {
          if config.api['from'] == 'rhyml' && !File.exist?(config.api['href'].to_s)
            raise ArgumentError, "Missing RHYML file at: #{config.api['href']}"
          end
        }
      }
    ].freeze


    # Enable constraints to be defined here for particular properties, selected as
    # "api.from" or whatever works best, which for now we will hard-code into this method
    def self.validate_config config, *scopes
      scopes.flatten!
      rules = VALIDATION_RULES.select { |rule| (rule[:scopes] & scopes).any? }
      rules.each { |rule| rule[:check].call(config) }
    end

    def self.known_api_sources config
      builtin_path = File.join(GEM_ROOT_PATH, '_mappings')
      custom_path = File.expand_path(config.paths['custom_mappings_dir'] || '_mappings')
    
      mapping_files = Dir["#{builtin_path}/*-api.yml", "#{custom_path}/*-api.yml"]
    
      base_names = mapping_files.map do |file|
        File.basename(file).sub('-api.yml', '')
      end
    
      (base_names + %w[rhyml git]).uniq
    end

  end
end
