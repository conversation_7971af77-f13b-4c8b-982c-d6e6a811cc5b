# Generates and embeds manpage for the rhx command
# Generates documentation for config file

module ReleaseHx
  module Documentation
    # Generate manpage for rhx command
    # this is a build-time operation
    def self.generate_manpage
      # source manpage at <gem_root>/docs/manpage.adoc using Asciidoctor
      gem_root = File.expand_path('../..', __dir__)
      manpage_source = File.join(gem_root, 'docs/manpage.adoc')
      manpage_out = File.join(gem_root, 'build/docs/releasehx.1')
      Asciidoctor.convert_file(
        manpage_source,
        backend: 'manpage',
        safe: :unsafe,
        standalone: true,
        output: manpage_out
      )
    end
  
    # Generate documentation for config file (NYI)

  end
end