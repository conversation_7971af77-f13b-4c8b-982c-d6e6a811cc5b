require 'fileutils'
require 'tilt'

module ReleaseHx
  module WriteOps

    def self.safe_write path, content
      dirname = File.dirname(path)
      FileUtils.mkdir_p(dirname) unless Dir.exist?(dirname)
      File.write(path, content)
      ReleaseHx.logger.info "Wrote file: #{path}"
      :written
    end

    def self.gem_template_root
      File.expand_path('../../../templates/rhyml', __dir__)
    end

    def self.resolve_template_path name, config:
      user_dir = config.dig('paths', 'templates_dir')
      fallback_dir = gem_template_root

      search_paths = []
      search_paths << File.expand_path(name, user_dir) if user_dir
      search_paths << File.join(fallback_dir, name)

      found = search_paths.find { |p| File.exist?(p) }

      return found if found

      raise "Template not found: #{name} (searched #{search_paths.join(' , ')})"
    end

    def self.derive_output_path version, format, config
      filename_tpl = config.dig('paths', 'draft_filename')
      filename     = if filename_tpl
        Tilt.new { filename_tpl }.render(Object.new, 'version' => version)
      else
        "#{version}.#{format}"
      end

      drafts_dir = config.dig('paths', 'drafts_dir') || 'drafts'
      File.join(drafts_dir, filename)
    end

    def self.process_template template_path, vars, config
      template_content = File.read(template_path)
      
      plugin_dirs = [
        File.expand_path('../../../jekyll_plugins', __dir__)
      ].compact
      
      includes_load_paths = []

      user_templates_dir = config.dig('paths', 'templates_dir')
      gem_templates_dir  = File.expand_path('../../../lib/templates/rhyml', __dir__)


      if user_templates_dir
        user_templates_dir = File.expand_path(user_templates_dir, Dir.pwd) unless Pathname.new(user_templates_dir).absolute?
        includes_load_paths << user_templates_dir
      end

      includes_load_paths << gem_templates_dir
      
      # STEP 1: Make sure plugins and monkeypatches are loaded
      Sourcerer::Jekyll.initialize_liquid_runtime
    
      # STEP 2: Setup fake site
      site = Sourcerer::Jekyll::Bootstrapper.fake_site(
        includes_load_paths: includes_load_paths,
        plugin_dirs: plugin_dirs
      )
    
      # STEP 3: Parse template
      template = ::Liquid::Template.parse(template_content)
    
      # STEP 4: Render template
      rendered = template.render(
        vars,
        registers: {
          site: site,
          file_system: Sourcerer::Jekyll::Liquid::FileSystem.new(includes_load_paths.first),
          includes_load_paths: includes_load_paths,
          releasehx_debug: true
        }
      )

      if rendered.include?('Liquid error')
        raise "Template rendering failed:\n#{rendered}"
      end

      # STEP 5: Post-process rendered content
      blank_lines = config.dig('modes', 'remove_excess_lines')
      if blank_lines == 0
        rendered = rendered.gsub(/\n{2,}/, "\n")
      elsif blank_lines
        rendered = rendered.gsub(
          /\n{#{blank_lines + 1},}/,
          "\n" * (blank_lines + 1)
        )
      end

      rendered
    end
    
    def self.gem_template_root
      File.expand_path('../../../lib/templates/rhyml', __dir__)
    end

  end
end
