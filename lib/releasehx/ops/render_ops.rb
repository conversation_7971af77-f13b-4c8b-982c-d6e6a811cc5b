module ReleaseHx
  module <PERSON>der<PERSON><PERSON>
    def self.render_from_file file_path, format:, config:, force: false
      raise "File not found: #{file_path}" unless File.exist?(file_path)

      content = File.read(file_path)
      engine = Tilt.new(file_path)
      rendered = engine.render(Object.new, 'config' => config)

      output_ext = format.to_sym == :html ? 'html' : 'pdf'
      output_path = file_path.sub(/\.[^.]+$/, ".#{output_ext}")

      WriteOps.safe_write(output_path, rendered, force: force)
      output_path
    end

    def self.render_from_rhyml release:, config:, format:, version:, force: false
      context = {
        'release' => release.to_h,
        'changes' => release.changes.map(&:to_h),
        'config'  => config
      }

      template_name = case format.to_sym
                      when :html then 'rhyml.html.liquid'
                      when :pdf  then 'rhyml.adoc.liquid' # assumes asciidoctor-pdf downstream
                      else raise "Unsupported render format: #{format}"
                      end

      template_path = WriteOps.resolve_template(template_name, config: config)
      rendered = Tilt.new(template_path).render(Object.new, context)

      output_ext = format.to_sym == :html ? 'html' : 'pdf'
      output_path = WriteOps.derive_output_path(version, output_ext, config)
      WriteOps.safe_write(output_path, rendered, force: force)

      output_path
    end
  end
end