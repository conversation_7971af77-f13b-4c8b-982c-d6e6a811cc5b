module ReleaseHx
  module DraftOps

    def self.from_payload payload:, config:, mapping:, release_code:, release_date: nil, scan: false
      adapter = ReleaseHx::RHYML::Adapter.new(mapping: mapping, config: config)

      adapter.to_release(
        payload,
        release_code: release_code,
        release_date: release_date || Date.today,
        scan: scan
      )
    end

    def self.write_draft release:, config:, format:, output_path:
      raise ArgumentError, 'release is nil' if release.nil?

      # Establish available datasets
      all_changes = release.changes.select { |ch| ch.respond_to?(:to_h) }
      changes_mapped = all_changes.map(&:to_h)

      sorted = {
        'by' => {
          'tag'  => {},
          'type' => {},
          'part' => {}
        }
      }

      changes_mapped.each do |ch|
        Array(ch['tags']).each do |tag|
          sorted['by']['tag'][tag] ||= []
          sorted['by']['tag'][tag] << ch
        end

        type = ch['type']
        if type
          sorted['by']['type'][type] ||= []
          sorted['by']['type'][type] << ch
        end
        
        # treat 'parts' differently from type or tags as it is an Array in all cases
        parts = ch['parts']
        if parts
          Array(parts).each do |part|
            sorted['by']['part'][part] ||= []
            sorted['by']['part'][part] << ch
          end
        end
      end

      # Ensure all config-defined parts/types/tags are initialized
      Array(config.dig('types')&.keys).each do |type|
        sorted['by']['type'][type] ||= []
      end

      Array(config.dig('parts')&.keys).each do |part|
        sorted['by']['part'][part] ||= []
      end

      Array(config.dig('tags')&.keys).each do |tag|
        next if %w[_include _exclude].include?(tag)
        sorted['by']['tag'][tag] ||= []
      end

      context_scope = {
        'release' => release.to_h,
        'changes' => changes_mapped,
        'sorted'  => sorted,
        'config'  => config
      }

      SchemaGraphy::Templating.render_all_templated_fields!(config, context_scope)

      variables = {
        'release' => release.to_h,
        'changes' => changes_mapped,
        'config'  => config,
        'sorted'  => sorted
      }

      tplt = case format.to_sym
             when :yaml, :yml then 'rhyml.yaml.liquid'
             when :md         then 'release.md.liquid'
             when :adoc       then 'release.adoc.liquid'
             when :html       then 'release.html.liquid'
             else raise ArgumentError, "Unsupported draft format: #{format}"
             end

      tplt_path = WriteOps.resolve_template_path(tplt, config: config)
      ReleaseHx.logger.debug "Using template: #{tplt_path}"

      rendered = WriteOps.process_template(tplt_path, { 'vars' => variables }, config)
      WriteOps.safe_write(output_path, rendered)
    end

  end
end
