module ReleaseHx
  module Check<PERSON><PERSON>
    def self.print_check_summary release, raw_issues_count
      logged_changes = release.changes.size
      notes_present  = release.changes.count { |c| c.note.to_s.strip != '' }
      missing_notes  = logged_changes - notes_present
    
      puts
      puts '📝 Release Note Check Report'
      puts "• Issues Fetched:     #{raw_issues_count}"
      puts "• Changes Logged:     #{logged_changes}"
      puts "• Notes Present:      #{notes_present}"
      puts "• Missing Notes:      #{missing_notes}"
      puts "• Release Code:       #{release.code}"
    end
  end
end
