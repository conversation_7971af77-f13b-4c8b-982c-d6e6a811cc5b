{%- assign key = include.property[0] %}
{%- assign val = include.property[1] %}
{%- assign parent_disp = include.parent_disp %}
{%- assign dflt = val.dflt %}
{%- assign tier = include.tier | plus: 0 %}
{%- assign indent = '' %}

{%- if tier > 1 %}
{%- for i in (2..tier) %}{% assign indent = indent | append: '  ' %}{% endfor %}
{%- endif %}
{%- assign cmmt = val.cmmt | default: val.desc | default: '' | strip | split: "
" | first %}
{%- if cmmt and cmmt != "" %}
  {%- assign cmmt = cmmt | demarkupify | truncatewords: 10 | prepend: '# ' %}
{%- endif %}
{%- assign dflt_kind = dflt | sgyml_type | split: ":" | first %}
{%- assign dflt_class = dflt | sgyml_type | split: ":" | last %}
{%- assign key_first_char = key | slice: 0, 1 %}
{%- if parent_disp and parent_disp == "nulled" %}
  {%- assign disp = "nulled" %}
{%- else %}
  {%- if key_first_char == "<" %}
    {%- assign disp = "nulled" %}
  {%- elsif dflt contains '
  ' or val.type == "Multiline" %}
    {%- assign disp = "multiline" %}
  {%- elsif val.type == "String" and dflt.size > 40 %}
    {%- assign disp = "multiline" %}
  {%- elsif val.templating or val.type == "Template" or val.type == "Liquid" or val.type == "RegExp" %}
    {%- if dflt %}
      {%- assign disp = "multiline" %}
    {%- else %}
      {%- assign disp = "nulled" %}
    {%- endif %}
  {%- elsif val.type == "Array" or val.type == "ArrayList" or dflt_class == "ArrayList" %}
    {%- if dflt.size < 4 and (val.type == "ArrayList" or dflt_class == "ArrayList") %}
      {%- assign disp = "sequence-flow" %}
    {%- else %}
      {%- assign disp = "sequence-block" %}
    {%- endif %}
  {%- elsif val.properties or val.type == "Map" %}
    {%- assign disp = "mapping-block" %}
  {%- elsif dflt == nil %}
    {%- assign disp = "nulled" %}
  {%- elsif !val.type %}
    {%- assign disp = "unquoted" %}
  {%- elsif val.type == "String" and dflt == "" %}
    {%- assign disp = "blank-string" %}
  {%- else %}
  {%- endif %}
{%- endif %}

{%- case disp %}
{%- when "multiline" %}
{%- assign ml_indent = indent.size | plus: 2 %}
{{- indent }}{{ key }}: | {{ cmmt }}
{{ dflt | indent: ml_indent, true }}
{%- when "mapping-block" %}
{{- indent }}{{ key }}: {{ cmmt }}
{%- when "sequence-block" %}
{{- indent }}{{ key }}: {{ cmmt }}
  {%- for item in dflt %}
{{ indent }}  - {{ item }}
  {%- endfor %}
{%- when "sequence-flow" %}
{{- indent }}{{ key }}: [{{ dflt | join: ', ' }}] {{ cmmt }}
{%- when "nulled" %}
{{- indent }}# {{ key }}: # {% if val.type %}({{ val.type }}) {% endif %}{{ cmmt }}
{%- when "blank-string" %}
{{- indent }}{{ key }}: '' {{ cmmt }}
{%- when "unquoted" %}
{{- indent }}{{ key }}: {{ dflt }} {{ cmmt }}
{%- else %}
{{- indent }}# UNIDENTIFIED
{%- endcase %}
