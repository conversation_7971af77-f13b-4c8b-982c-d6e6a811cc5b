{%- assign format  = "asciidoc" %}
{%- assign config  = vars.config %}
{%- assign release = vars.release %}
{%- assign changes = vars.changes %}
{%- assign sorted  = vars.sorted -%}
:icons: font
{%- embed head-parser.liquid %}

{%- if release.hash and release.hash != "" and config.links.git and config.links.git != "" %}
link:{{ config.links.git | render: release }}[icon:{{ config.links.git_icon | default: 'code-fork' }}[]{{ release.hash | slice: 0, 7 }}]
{%- endif %}

{%- embed sections.liquid %}