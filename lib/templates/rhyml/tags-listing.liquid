{%- assign tags_listing = "" %}
{%- for tag in change.tags %}
{%-   assign skip_tag = false %}
{%-   if tag == "changelog" or tag == "release_note_needed" %}
{%-     assign skip_tag = true %}
{%-   elsif tag == group1_slug or tag == group2_slug %}
{%-     assign skip_tag = true %}
{%-   elsif config.tags[tag] == nil %}
{%-     assign skip_tag = true %}
{%-   endif %}
{%-   unless skip_tag %}
{%-     capture tags_listing %}{{ tags_listing }}
{%-       if config.tags[tag].icon and show_icons and show_icons == "before" %}
icon:{{ config.tags[tag].icon }}[]
{%-       endif %}
{%-       if show_labels %}
[.tag-label]*{{ config.tags[tag]['text'] | default: tag }}*
{%-       endif %}
{%-       if config.tags[tag].icon and show_icons and show_icons == "after" %}
icon:{{ config.tags[tag].icon }}[]
{%        if show_labels %}
[.tag-label]*{{ config.tags[tag]['text'] | default: tag }}*
{%-       endif %}
{%-       endif %}
{%-     endcapture %}
{%-   endunless %}
{%- endfor %}
