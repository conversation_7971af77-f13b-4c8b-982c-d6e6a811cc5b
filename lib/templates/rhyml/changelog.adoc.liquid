{%- assign content_config = config.changelog %}
{%- embed changes-sorter.liquid %}
{%- assign level = content_config.htag | replace: "h", "" | default: dflt_next_header_level | plus: 0 %}
{%- capture header_chars %}
{%-   for i in (1..level) %}{{ header_char }}{% endfor %}
{%- endcapture %}
{%- include header.liquid format=format level=level text=content_config.head %}
{%- assign dflt_next_header_level = level | plus: 1 -%}
{%- assign item_count = 0 %}
{%- embed section-text.liquid %}

{%- embed groupings.liquid %}