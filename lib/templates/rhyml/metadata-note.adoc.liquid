{%- assign show_labels = content_config.items.metadata_labels %}
{%- assign show_icons = content_config.items.metadata_icons %}
{%- embed tags-listing.liquid %}

{%- capture change_metadata %}
{%-   for kind in metadata_kinds %}
{%-     assign change_meta_kind = change[kind] %}
{%-     assign kind_plural = kind | append: "s" %}
{%-     if kind == "tag" %}
{%-       continue %}
{%-     endif %}
{%-     if config[kind_plural][change_meta_kind].icon and show_icons and show_icons == "before" %}
icon:{{ config[kind_plural][change_meta_kind].icon | default: 'tag' }}[role=meta-icon]
{%-     endif %}
{%-     if show_labels %}
[.meta-label.{{ kind }}-label]*{{ config[kind_plural][change_meta_kind]['text'] | default: change_meta_kind }}*
{%-     endif %}
{%-     if config[kind_plural][change_meta_kind].icon and show_icons and show_icons == "after" %}
icon:{{ config[kind_plural][change_meta_kind].icon | default: 'tag' }}[role=meta-icon]
{%-     endif %}
{%-   endfor %}
{%-   if tags_listing != "" %}
{{- tags_listing | trim }}
{%-   endif %}
{%-   if config.links.web and config.links.web %}
link:{{ "https://github.com/DocOps/releasehx-demo/commit/{{ hash }}" | render: change }}[icon:ticket[]{{ change.tick }}]
{%-   endif -%}
{%-   if config.links.git and config.links.git != "" %}
link:{{ config.links.git | render: change }}[icon:{{ config.links.git_icon | default: 'code-fork' }}[]{{ change.hash | slice: 0, 7 }}]
{%-   endif %}
{%- endcapture %}
{%- assign catch_metadata_error = change_metadata %}