  {%- assign change = include.change %}
  {%- assign config = include.config %}
  - chid: {{ change.chid }}
    type: {{ change.type }}
    {%- if change.parts %}
    parts: {% if change.parts.size == 1 %}[{{ change.parts[0] }}]{% else %}
      {%- for part in change.parts %}
      - {{ part }}
      {%- endfor %}
      {%- endif %}
    {%- endif %}
    {%-  if change.hash %}
    hash: {{ change.hash }}
    {%- endif %}
    summ: {{ change.summ }}
    {%- if change.head %}
    head: {{ change.head }}
    {%- endif %}
    {%- if change.note %}
    note: |
      {{ change.note | indent: 6 }}
    {%- endif %}
    {%-  if change.tags %}
    tags:
    {%- for tag in change.tags %}
      - '{{ tag }}'
    {%- endfor %}
    {%- endif %}
    lead: {{ change.lead }}