# frozen_string_literal: true

require 'thor'
require 'logger'
require 'liquid'
require 'yaml'
require_relative 'sourcerer'
require_relative 'schemagraphy'
require_relative 'releasehx/configuration'
require_relative 'releasehx/rhyml'
require_relative 'releasehx/version'
require_relative 'releasehx/sgyml/helpers'
require_relative 'releasehx/ops/check_ops'
require_relative 'releasehx/ops/draft_ops'
require_relative 'releasehx/ops/write_ops'
require_relative 'releasehx/ops/render_ops'

module ReleaseHx

  def self.attrs
    if ENV['RELEASEHX_DEV_RELOAD'] == 'true'
      # Development-only reload from source document
      require 'asciidoctor' # explicitly required here for dev-only reload
      Sourcerer.load_attributes(File.expand_path('../README.adoc', __dir__))
    else
      # Always use pre-generated attributes at runtime
      ReleaseHx::ATTRIBUTES[:globals]
    end
  end

  class << self
    attr_accessor :logger

    def logger
      return @logger if @logger

      # Force line-by-line flushing
      $stdout.sync = true

      log = Logger.new($stdout)
      log.level = Logger::INFO
      log.formatter = proc do |severity, _datetime, _progname, msg|
        "#{severity}: #{msg}\n"
      end

      @logger = log
    end
  end

  class Error < StandardError; end

end