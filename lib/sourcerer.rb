# frozen_string_literal: true

# This module is a pre-alpha version of what I will eventually spin off
#  as AsciiSourcery, for single-sourcing documentation AND product data
#  in AsciiDoc and YAML files
# It is pretty messy for now as I play around with various ways it might
#  get used, including as a build-time generator of artifacts to be used
#  in both the app and the docs

require 'asciidoctor'
require 'fileutils'
require 'yaml'
require_relative 'schemagraphy'
require_relative 'sourcerer/builder'
require_relative 'sourcerer/plaintext_converter'
require_relative 'sourcerer/jekyll'

module Sourcerer
  # This module will probably be spun off as AsciiSourcerer or something

  # Loads AsciiDoc attributes from the document header as a Hash
  def self.load_attributes path
    doc = Asciidoctor.load_file(path, safe: :unsafe)
    doc.attributes
  end

  # Loads a snippet from a main AsciiDoc file, with optional tag and tags filtering
  def self.load_include path_to_main_adoc, tag: nil, tags: [], leveloffset: nil
    opts = []
    opts << "tag=#{tag}" if tag
    opts << "tags=#{tags.join(',')}" if tags.any?
    opts << "leveloffset=#{leveloffset}" if leveloffset
  
    snippet_doc = <<~ADOC
      include::#{path_to_main_adoc}[#{opts.join(', ')}]
    ADOC
  
    doc = Asciidoctor.load(
      snippet_doc,
      safe: :unsafe,
      base_dir: File.expand_path('.'),
      header_footer: false,
      attributes: { 'source-highlighter' => nil } # disable extras
    )
  
    # Get raw text from all top-level blocks
    doc.blocks.map(&:content).join("\n")
  end
  

  # Programmatically extract tagged content from any plaintext file demarcated with tagged content
  def self.extract_tagged_content path_to_tagged_adoc, tag: nil, tags: [], comment_prefix: '// ', comment_suffix: '', skip_comments: false
    raise ArgumentError, "tag and tags cannot coexist" if tag && !tags.empty?
    tags = [tag] if tag
    raise ArgumentError, "at least one tag must be specified" if tags.empty?
    raise ArgumentError, "tags must all be strings" unless tags.is_a?(Array) && tags.all? { |t| t.is_a?(String) }
    tagged_content = []
    open_tags = {}
    tag_comment_prefix = comment_prefix.strip || '//'
    tag_pattern = /^#{Regexp.escape(tag_comment_prefix)}\s*tag::([\w-]+)\[\]/
    end_pattern = /^#{Regexp.escape(tag_comment_prefix)}\s*end::([\w-]+)\[\]/
    comment_line_init_pattern = /^#{Regexp.escape(tag_comment_prefix)}+/
    collecting = false
    File.open(path_to_tagged_adoc, 'r') do |file|
      file.each_line do |line|
        # check for tag:: line
        if line =~ tag_pattern
          tag_name = Regexp.last_match(1)
          if tags.include?(tag_name)
            collecting = true
            open_tags[tag_name] = true
          end
        elsif line =~ end_pattern
          tag_name = Regexp.last_match(1)
          if open_tags[tag_name]
            open_tags.delete(tag_name)
            collecting = false if open_tags.empty?
          end
        else
          if collecting
            unless skip_comments && line =~ comment_line_init_pattern
              tagged_content << line
            end
          end
        end
      end
      if tagged_content.empty?
        tagged_content = ''
      else
        # return a string of concatenated lines
        tagged_content = tagged_content.join
      end
    end
    
    tagged_content
  end

  # Build-time generation of manpage from AsciiDoc source
  def self.generate_manpage source_adoc, target_manpage
    FileUtils.mkdir_p File.dirname(target_manpage)
    Asciidoctor.convert_file(
      source_adoc,
      backend: 'manpage',
      safe: :unsafe,
      standalone: true,
      to_file: target_manpage
    )
  end

  # Render templates
  def self.render_templates templates_config
    templates_config.each do |template_config|
      data_obj = template_config[:key] || 'data'
      render_template(
        template_config[:template],
        template_config[:data],
        template_config[:out],
        data_object: data_obj
      )
    end
  end

  # Render a template with data
  def self.render_template template_file, data_file, out_file, data_object: 'data', includes_load_paths: []
    require_relative 'sourcerer/jekyll'
    require_relative 'sourcerer/jekyll/liquid/filters'
    require_relative 'sourcerer/jekyll/liquid/tags'
    require 'liquid' unless defined?(Liquid::Template)
    Sourcerer::Jekyll.initialize_liquid_runtime

     # Load the data
    data = SchemaGraphy::Loader.load_yaml_with_tags(data_file)

    out_file = File.expand_path(out_file)
    FileUtils.mkdir_p(File.dirname(out_file))

    # Determine includes root
    fallback_templates_dir = File.expand_path('lib/templates', Dir.pwd)
    paths = (includes_load_paths.any? ? includes_load_paths : [fallback_templates_dir])

    # Create a fake Jekyll site
    site = Sourcerer::Jekyll::Bootstrapper.fake_site(
      includes_load_paths: paths,
      plugin_dirs: []
    )

    # Setup file system for includes
    file_system = Sourcerer::Jekyll::Liquid::FileSystem.new(paths.first)

    # Resolve and load template
    template_path = File.join(paths.first, template_file)
    template_content = File.read(template_path)

    template = Liquid::Template.parse(template_content)

    # Prepare context
    context = {
      data_object => data,
      'include' => { data_object => data }  # for compatibility with {% include ... %} expecting include.var
    }
    options = {
      registers: {
        site: site,
        file_system: file_system
      }
    }

    # Render the template
    rendered = template.render(context, options)

    File.write(out_file, rendered)
  end   
    
end
