{"issues": [{"id": "1001", "key": "PROJ-310", "fields": {"issueType": {"name": "Improvement"}, "summary": "Add inline editing to dashboard widgets", "description": "Implements interactive dashboard widgets using JavaScript hooks and async save.\n\n## Draft Release Note\n\nIntroduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.\n\nThis significantly streamlines common tasks.", "components": [{"name": "webui"}], "labels": ["highlighted", "changelog", "release_note_needed"], "assignee": {"displayName": "julie"}, "development": {"commits": [{"type": "commit", "sha": "a1b2c3d4e5f60718293a4b5c6d7e8f9012345678"}]}}}, {"id": "1002", "key": "PROJ-467", "fields": {"issueType": {"name": "Improvement"}, "summary": "Add rate limiting headers to public API", "description": "Implements middleware to attach X-RateLimit headers.\n\n## Draft Release Note\n\nThe API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.\n\n> **Note:** The default rate limit remains unchanged.", "components": [{"name": "api"}], "labels": ["breaking", "changelog", "release_note_needed"], "assignee": {"displayName": "devin"}, "development": {"commits": [{"type": "commit", "sha": "abc123abc123abc123abc123abc123abc123abcd"}]}}}, {"id": "1003", "key": "PROJ-983", "fields": {"issueType": {"name": "Bug"}, "summary": "Fix modal overflow on small screens", "description": "Bugfix: modal height exceeded viewport height.\n\n## Draft Release Note\n\nModals now scroll correctly on devices with < 600px screen width.", "components": [{"name": "webui"}], "labels": ["changelog", "release_note_needed"], "assignee": {"displayName": "sam"}, "development": {"commits": [{"type": "commit", "sha": "deadbeef0000000000000000000000000000feed"}]}}}, {"id": "1004", "key": "PROJ-987", "fields": {"issueType": {"name": "Bug"}, "summary": "Fix JSON serialization issue with null values", "description": "Previously, null values were omitted from JSON payloads.\n\n## Draft Release Note\n\nPreviously, null values were omitted from JSON payloads. This has been corrected.\n\n**Impact:** Clients expecting explicit nulls will now receive them as specified.", "components": [{"name": "api"}], "labels": ["release_note_needed"], "assignee": {"displayName": "cory"}, "development": {"commits": [{"type": "commit", "sha": "facefeed1234567890abcdef1234567890abcdef"}]}}}, {"id": "1005", "key": "PROJ-399", "fields": {"issueType": {"name": "Improvement"}, "summary": "Add missing keyboard shortcuts to Help page", "description": "User doc update requested by QA lead.\n\n## Draft Release Note\n\nThe Help page now includes a full list of supported keyboard shortcuts, including:\n\n- `/` to focus search\n- `?` to show help modal", "components": [{"name": "webui"}], "labels": ["release_note_needed"], "assignee": {"displayName": "robin"}, "development": {"commits": [{"type": "commit", "sha": "ffeeddbbaa99887766554433221100aa00bbccdd"}]}}}, {"id": "1006", "key": "PROJ-200", "fields": {"issueType": {"name": "Documentation"}, "summary": "Deprecate v1 endpoints for user settings", "description": "Phase-out plan for v1 settings endpoints.\n\n## Draft Release Note\n\nThe following v1 endpoints are now deprecated:\n\n- `GET /api/v1/settings/user`\n- `PUT /api/v1/settings/user`\n\nUse `/api/v2/settings/profile` instead.", "components": [{"name": "api"}], "labels": ["deprecation", "release_note_needed"], "assignee": {"displayName": "amir"}, "development": {"commits": [{"type": "commit", "sha": "55556666777788889999aaaabbbbccccddddeeee"}]}}}, {"id": "1007", "key": "PROJ-1234", "fields": {"issueType": {"name": "Improvement"}, "summary": "Speed up dependency resolution during install", "description": "Performance improvement during CLI install.\n\nThis issue is intended for changelog only.", "components": [{"name": "installer"}], "labels": ["changelog"], "assignee": {"displayName": "jorge"}, "development": {"commits": [{"type": "commit", "sha": "0000111122223333444455556666777788889999"}]}}}]}