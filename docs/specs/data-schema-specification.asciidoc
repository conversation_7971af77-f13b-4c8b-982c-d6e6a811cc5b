include::{path_to_project_root}/README.adoc[tag="globals"]
= SchemaGraphy Data Schema Syntax Specification (Version {proj_vrsn_this})
:toc: macro
:toc-title: Table of Contents

toc::[]

== Introduction

SchemaGraphy has been designed as a flexible solution for those looking to define, validate, and parse schemas for both data and structured text. 
Given the complexity and volume of information we interact with daily, a standardized approach to mapping and interpreting this information is indispensable.

This specification document focuses on SchemaGraphy _data_ schemas, which are intended to help define and validate data objects such as YAML, JSON, or other serialized formats, or data objects native to a programming language in a transferrable, universal fashion.

ifdef::env-github[]

[IMPORTANT]
This document is best presented in rendered/published form, at https://schemagraphy.docopslab.org/data-schema-specification.
You are reading an un-preprocessed version of the source document, which depends on but does not express other source documents.

endif::[]

=== Purpose of this Specification Document

The objective of this document is to provide a comprehensive Specification for the SchemaGraphy YAML syntax for _data_ schemas.
It aims to enable users to understand and employ the syntax effectively in their respective contexts -- either to specify how tooling should interpret ScehmaGraphy data schemas or how users should author them.

For best practices and perhaps user-friendlier descriptions of the aspects and usage of a SchemaGraphy data schema, see the User Documentation.
However, this document represents the canonical source of truth with regard to the specifics of the SchemaGraphy Data Schema (version {proj_vrsn_this}).

=== SchemaGraphy Overview

SchemaGraphy is a framework for describing structured data and text documents.
It exploits the simplicity and human readability of YAML for schema definition as well as data formatting.

SchemaGraphy also extends YAML with SGYML, which adds data typing and transclusion to the official YAML Specification, but that is handled in its own link:{schemagraphy_sgyml_spec}[Specification document].
Likewise, SchmaGraphy data schemas have their own link:{schemagraphy_text_schemas_spec}[Specification document].

== Background and Terminology

Understanding SchemaGraphy necessitates a basic comprehension of both data schemas and text schemas, as well as certain key concepts and terms related to SchemaGraphy.
These will be discussed in detail in this section.

=== Conceptual Terms of SchemaGraphy

SchemaGraphy is expressed as a domain-specific language (DSL) for serialized data called *SGYML*, which is a specific implementation of YAML that modestly extends the YAML Specification with enhanced capabilities.

SchemaGraphy also establishes two parallel schema-definition languages (SDL) that in turn establish syntaxes for schemas that govern and aid in the parsing of certain _data_ and _text_ documents.
These are SchemaGraphy Data Schemas (SGDS) and SchemaGraphy Text Schemas (SGTS), respectively.

In SchemaGraphy, *schemas* are SGYML documents specially formatted to define the structure of _data or text documents_.

While the two different schema formats (_text_ and _data_) are highly parallel and overlapping, each has unique or specially handled features, concepts, and syntax elements, and each is defined by entirely separate _root-schema_ and _descriptive specification_ documents.

*This is the descriptive Specification document for SchemaGraphy _data schemas_.*
The root-schema for this type of schema is located at link:https://github.com/DocOps/schemagraphy/blob/main/gem/lib/schemagraphy/specs/schemas/schema-data.yml[SchemaGraphy Data Schema].

This section () includes concepts and terms shared by both types, but below this section (starting at <<data-schema-description>>), all references are specifically to SGYML-formatted _data_ and SchemaGraphy _data schemas_; details likely will _not apply_ to SchemaGraphy _text schemas_.

Several terms of art are given a very particular meaning in this Specification and the broader documentation of SchemaGraphs and the SGYML syntax.

[[glossary-terms-of-art]]
==== Technical Terms of Art

SchemaGraphy and SGYML depend on various terms of art that are used with particular meaning in the concept of these specific technologies and this Specification document.
Herein and hopefully throughout the rest of SchemaGraphy/SGYML documentation and usage more broadly, to the extent possible, the following words are used with the meanings annotated below.

[NOTE]
The following listing is ordered by relationship and hopefully a sensible means of acquiring the information.
For an alphabetical arrangement, see <<glossary-reference>>

include::_built/glossary-data-schema-by-relevance.adoc[]

Note that some of the above terms have _different definitions_ in the other specifications (SchemaGraphy text schemas and SGYML), and each such "`Technical Terms of Art`" section contains only a subset of all defined terms.
The listings and definitions are tailored for contextual relevance but also expediency.

=== Semantic Key Words in this Document

The key words "`MUST`", "`MUST NOT`", "`REQUIRED`", "`SHALL`", "`SHALL NOT`", "`SHOULD`", "`SHOULD NOT`", "`RECOMMENDED`", "`NOT RECOMMENDED`", "`MAY`", and "`OPTIONAL`" in this document are to be interpreted as described in link:https://tools.ietf.org/html/bcp14[BCP 14] link:https://tools.ietf.org/html/rfc2119[RFC2119] link:https://tools.ietf.org/html/rfc8174[RFC8174] when, and only when, they appear in all capitals, as shown here.

=== Data Typing Overview

The following data types and formats are more thoroughly defined in the link:{scehmagraphy_sgyml_spec}[SGYML Specification] and referenced in the <<data-typing-reference>> appendix below.

These types ensure cross-platform consistency and versatility, which strengthens the robustness of your schemas.
The following section will discuss these types and their role in SchemaGraphy.

Data types are divided into _kinds_, _classes_, and _variants_.

The *kinds* are _Scalar_ (sometimes called "`atomic`" or "`primitive`") and _Composite_ (sometimes called "`enumerable`" or "`complex`").

The *classes* are _String_, _Number_, _DateTime_, _Boolean_, _Array_, _Map_, and _Null_.

The *variants* are the numerous specific forms that objects of these classes may take.
For instance, a `String` value may be an e-mail address (`Email`) or a Regular Expression (`RegExp`) variant, or a `Number` value may be an whole-number integer (`Integer`) or a decimal-delimited float (`Float`) variant.
Even the composite classes (Arrays and Maps) have variants that dictate the structure of their serialized contents.

[NOTE]
Anywhere a SchemaGraphy Data Schema designates a property value must be a `DataType`, you can indicate any _kind_, _class_, or _variant_ name, incorporating all members of the stated set.

Here is a brief, unannotated listing of SchemaGraphy data types.

include::_built/data-types-listing.adoc[]

See the <<data-typing-reference>> for a detailed breakdown.

[[data-schema-description]]
== Data Schema STandard

Data schema in SchemaGraphy presents a defined structure for subject data, serving as a form of contract, specifying the anticipated structure of the data.
This section provides an in-depth specification of the SchemaGraphy schema-definition language for serialized data.

== What SchemaGraphy Data Schemas Do



=== Indicating a Schema or Sub-schema

Without delving into how SchemaGraphy data schemas are designated to govern a specific data document, it is important to understand how a given schema is flagged as such.

In an SGYML context, where valid tooling supports SGYML-formatted data documents, base-level schema is typically flagged with a `$schema` property key directive.

[source,yaml]
----
$schema:
  # schema an subschemas go here
----

[NOTE]
An imported (transcluded) schema is _not_ considered a sub-schema.
See <<schema-import>>.

A sub-schema may be cross-referenced: that is, transcluded from a remote document using an SGYML `$ref` or `$refs` directive.

=== Simple Schema Makeup

Technically, a schema governs just the _content_ of a YAML, JSON, or other-formatted document or data object.
If the entire contents of a subject document is one un-parameterized line or block, a SchemaGraph can govern it.

.A schema that allows a subject document to consist of Strings `this` or `that`
[source,yaml]
----
$schema:
  value:
    type: String
    rules:
      regexp: /this|that/
----

.A _valid_ subject.json file according to the above schema
[source,json]
----
"this"
----

.A _valid_ subject.yml file according to the above schema
[source,yaml]
----
this
----

.A schema that implies a default of `this` (`this-that.yaml`)
[source,yaml]
----
$schema:
  type: String
  default: this
  rules:
    regexp: /this|that/
----

.A _valid_ subject.yml file according to the above schema
[source,yaml]
----
---
# this document has no contents
----

Therefore, an example implementation:

[source,ruby]
----
thisthat_schema = SGYML.load_schema('_schemas/this-that.yaml')
thisthat_object = SGYML.load_file('data/subject.yml', thisthat_schema)
puts thisthat_object + " | " + thisthat_object.sgyml_class + " | " + thisthat_object.is_a? String
----

The above example would return: `this | String | true`.

=== Basic Schema Makeup

More commonly, schemas are used to define Composite objects -- Maps and Arrays.

=== Advanced Schema Makeup



== Data Schema Application

This section discusses the means and methods by which SchemaGraphy data schemas are associated and used with the data objects they govern.

== Appendices & References

[appendix]
[[glossary-reference]]
=== Glossary (Alphabetical)

This is a terms-of-art reference in alphabetical order.

include::_built/glossary-data-schema-alphabetical.adoc[]

[appendix]
[[data-typing-reference]]
=== SGYML Data Typing Reference

The following is a detailed breakdown of the supported data types (_classes_ and _variants_) divided broadly by _kind_.

==== Scalar Classes and Types

include::_built/data-types-refence-scalar.adoc[]

==== Composite Classes and Types

include:_built/data-types-reference-composite.adoc[]