= SchemaGraphy Text Schema Specification
:toc: macro
:toc-title: Table of Contents

toc::[]

== Introduction

SchemaGraphy has been designed as a flexible solution for those looking to define, validate, and parse schemas for both *data and text documents*.
Given the complexity and volume of information we interact with daily, a standardized approach to mapping and interpreting this information has potential for adoption across data-typing systems and textual-markup syntaxes.

This specification document focuses on SchemaGraphy *_data_ schemas*, called *SchemaGraphs* which are intended to help define and validate data objects such as YAML, JSON, or data objects native to a programming language in a transferrable, universal fashion.

Related specification documents include *"`SchemaGraphy Text Schema Specification`"* and *"`SGYML Data Format Specification`"*.

ifdef::env-github[]

[IMPORTANT]
This document is best presented in rendered/published form, at https://schemagraphy.docopslab.org/data-schema-specification.
You are reading an un-preprocessed version of the source document, which depends on but does not express other source documents.

endif::[]