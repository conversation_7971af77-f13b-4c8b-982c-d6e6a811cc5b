= SGYML Data Format Specification
:toc: macro
:toc-title: Table of Contents

This document defines the SchemaGraphy YAML-based Markup Language (SGYML), an extension of the YAML data markup specification.

The SGYML Specification describes how applications can interpret particular valid YAML syntax to extend the capabilities of YAML documents and objects such that they:

* embed remotely or locally defined objects for single-sourcing
* comport to a semantically improved and extended data typing system
* take advantage of SchemaGraphy data schemas for data governance

All supporting SGYML parsers ("`supportive tooling`" herein) MUST conform to this Specification.

toc::[]


== Background and Terminology

Understanding SchemaGraphy necessitates a basic comprehension of both data schemas and text schemas, as well as certain key concepts and terms related to SchemaGraphy.
These will be discussed in detail in this section.

=== SchemaGraphy Overview

SchemaGraphy is a framework for describing structured data and text documents.
It exploits the simplicity and human readability of YAML for schema definition as well as data formatting.

SchmaGraphy link:{schemagraphy_data_schemas_spec}[data schemas] and link:{schemagraphy_text_schemas_spec}[text schemas] have their own Specification documents.
SGYML relates more to the former, and it is also associated with the link:{schemagraphy_urix_spec}[URIx Specification], which designates how to author and interpret extended URI address pointers.

This Specification document ("`{doctitle}`") covers the direct extension of standard YAML to include 

[[glossary-terms-of-art]]
=== Technical Terms of Art

SchemaGraphy and SGYML depend on various terms of art that are used with particular meaning in the concept of these specific technologies and this Specification document.
Herein and hopefully throughout the rest of SchemaGraphy/SGYML documentation and usage more broadly, to the extent possible, the following words are used with the meanings annotated below.

[NOTE]
The following listing is ordered according to relationship of the terms and hopefully a sensible means of learning the information.
For an alphabetical arrangement, see <<glossary-reference>>

include::_built/glossary-sgyml-by-relevance.adoc[]

Note that some of the above terms have _different definitions_ in the other specifications (SchemaGraphy data and text schemas), and each such "`Technical Terms of Art`" section contains only a subset of all defined terms.
The listings and definitions are tailored for contextual relevance but also expediency.

=== Semantic Key Words in this Document

The key words "`MUST`", "`MUST NOT`", "`REQUIRED`", "`SHALL`", "`SHALL NOT`", "`SHOULD`", "`SHOULD NOT`", "`RECOMMENDED`", "`NOT RECOMMENDED`", "`MAY`", and "`OPTIONAL`" in this document are to be interpreted as described in link:https://tools.ietf.org/html/bcp14[BCP 14] link:https://tools.ietf.org/html/rfc2119[RFC2119] link:https://tools.ietf.org/html/rfc8174[RFC8174] when, and only when, they appear in all capitals, as shown here.


== Overview of SGYML Syntax

SGYML is a data serialization format that is designed to be both human-readable and easy to work with programmatically.

As a superset of the YAML 1.2 Specification, SGYML is designed to be easy to read and fun to write, and to seamlessly map to data structures in a wide variety of programming languages.

SGYML honors the YAML 1.2 Specification in its entirety, but it also adds:

. The mapping merge (`<<`) capability defined in the YAML 1.1 specification.
. Special `$ref` and `$refs` keys that transclude data from internal, external, or remote URI references.
. A special `$schema` key that applies an SGYML data schema to a specified data object.
. A special `$payload` key that designates part of a document as the _subject_ ("`governed object`") of an SGYML schema (SchemaGraph).

SGYML also establishes its own terminology for specific data types and categories of data types.

All SGYML data documents are valid YAML documents, but if any of the above features are in use, they must be parsed by tooling that supports SGYML's extensions.
(Some YAML non-SGYML YAML parsers do support the merge notation, which was removed in YAML 1.2.)

If an SGYML data object is governed by an SGYML schema, default values may be expected to be applied during a data resolution stage.

If an SGYML data object contains `$ref`/`$refs` keys, or even commented-out `# $ref:`/`# $refs:` indicators, it is intended to transclude data from other parts of the document or else other documents.


[[yaml-extended]]
== Extended YAML Features and Concepts

The entire purpose of SGYML is to enhance the *YAML 1.2 Specification* with user-friendlier concepts (such as data types) and dynamic capacities (such as directive keys).

In that capacity, this specification is an _augmentation to_ rather than a replacement for the YAML 1.2 Specification.


[[data-types-yaml]]
=== Data-typing Changes

The data-typing system used in YAML's 1.2 specification is decent, but some of its naming conventions are odd, and all the more confusing for having changed or had aliases assigned to them in version 1.1.

The primary changes made to this system from YAML 1.2 are:

. SGYML specifies the concept of an "`object`", which is any "`parameterized`" instance of data (datum) in either a source data document or a native data format -- any key-value pair is an _object_.

. SGYML changes the data type YAML calls a "`sequence`" to the more popular term "`Array`".

. SGYML changes the data type known as a "`mapping`" to the more common "`Map`".

. SGYML calls the category containing both of the above types "`Composites`" instead of "`Collections`".

. SGYML changes the data type that YAML refers to as "`timestamp`" to the more accurate "`DateTime`".

. In SGYML, the core "`types`" of data (Array, Map, String, Number, DateTime, and Boolean) are known as "`classes`", and each class has multiple "`variants`", which are usage-specific forms a value of a given class may take.

. Technically, the "`Integer`" and "`Float`" data types recognized in YAML are _variants_ in SGYML, both grouped into the _Number_ class.

And that is pretty much the totality of the difference in data typing concepts between YAML and SGYML.

Most of this is will be of very little consequence outside SchemaGraphs (SchemaGraphy schema definitions).

Since what SGYML calls _classes_ largely align with the core typing concept in YAML, SGYML leaves them essentially intact, if slightly renamed/recategorized.

However, in SGYML, the typing system is far more elaborate to accommodate SchemaGraphs, where data-type designations may be as broad as a _kind_ (`Composite` or `Scalar`) or as specific as a variant (`Email` or `BooleanYesNo`).
It is fully documented below in the section <<data-types-full>>.

=== SGYML-specific Terminology

To further flesh out some terms and concepts introduced in <<glossary-terms-of-art>>, here we describe particular ways SGYML aligns with or diverges from YAML and JSON.

==== "`Documents`"

Data "`serialization`" formats such as YAML and JSON technically represent data in the form of one collective String.
They are _documents_ that are marked up such that their constituent components can be treated as distinct data objects of designated or implied types.

Unlike "`native`" data in a programming language, a YAML or JSON (or XML or CSV, etc) document is a representation of one or more data objects, typically in storage or in transit between applications.

While there is often overlap between documents and objects is that the former are assumed to be objects in this representational state as marked-up Strings.

In SGYML, "`document`" is used to refer to the entirety of a source file or string, which is also an _object_, whereas "`object`" is used to refer to specific _content_ of such a document: the _data_ the document represents.

It is not to be assumed that any reference to an _object_ is a reference to something in a document -- an object is also a distinct conceptual entity of its own.

==== "`Objects`"

In JSON, the data type of "`object`" is used the way YAML uses "`mapping`" and SGYML uses "`Map`".

The YAML specification uses the term _object_ in various ways.
The most technical and closest to JSON's use is defined as "`repeated nodes`" or YAML-anchor-defined values that can be referenced with YAML aliases, which happen to be in the form of a collection of one or more key-value pairs.

In SGYML, this terminology is more clearly delineated.
An "`object`" is any key-identified instance of data; a "`parameterized`" data value; a key-value pair, where in fact the value might be a series of items in an Array or a series of objects in a Map.

In YAML format, this looks like:

[source,yaml]
----
key: value
key2:
  - value
  - valew
key3:
  - name: content
key4:
  key5: another value
----

Above, we have one object that consists of 4 additional objects named `key`, `key2`, `key3`, and `key4`.
And the final two of those constituent objects also contain objects: `key3` contains one as an Array item and `key4` contains one as a direct child property.

Note that `key3` itself is an Array object and `key4` itself is a Map object.

The concept of an object is not specific to the source syntax but rather the _resolved_ state of the data.

In the following example, only the key and the String `valew` are the object; the tag (`!some-tag`) and anchor (`&key_anch`) syntax is considered a merely semantic aspect of the _node_, specific to the YAML syntax but not the underlying data.

[source,yaml]
----
key: !some-tag &key_anch valew
----

So again, the "`datum`" of relevance here is the String `valew`, and the object is the named state of that datum.

==== "`Properties`"

In SGYML, _properties_ are constituent objects of a Map object, whether represented in YAML/JSON form or conceptually referenced as a keyed instance of data.

One difference between an _object_ and a _property_ in SGYML is that a Null property is not ever called an _object_.
A key with no value is simply an empty representation of a thing that could be but is not data.

Another nuance difference is that a _property_ is expected to exist _inside_ (or _as the entirety or part of the value of_) a parent _object_.
A property is a parameterized value of another object, which may in turn be the entire document or simply a parent property in the same document/object.

Therefore, while a YAML document is an object (because it is named and contains at least a value), it is _not_ itself a property (because it is not part of a parent object).

The concept of _properties_ in SGYML is very similar to its use in the JSON Schema specification, and in JSON Schema instances themselves.
It is a _key-value pair_ (or parameterized datum) that belongs to a parent object.

As with objects, in YAML presentation, only the property's identifier ("`key`") and its (resolved) value matter.

A key with no value is also a _property_.

[source,yaml]
----
prop1:
prop2: &some_anchor Some value
prop3: Null
----

In the above example, `prop1` is a property equivalent to `prop3` (excepting their differing keys).
Also, as with "`object`", `&some_anchor` is YAML syntax that is not truly part of the property.

[[data-types-full]]
==== Data Type Categories: Kinds, Classes, Variants

SGYML uses a tiered data-typing system that encompasses _kinds_, _classes_, and _variants_.

In SGYML parlance, the generic term *"`type`"* refers to both of the _kinds_ and all of the _classes_ and _variants_ that data objects may qualify as.
All data objects therefore qualify as at least _two_ types (a _class_ and its _kind_), and some additionally qualify as at least one _variant_.

All classes and variants fall into two main categories referred to as _kinds_.
Those two kinds are _Scalar_ and _Composite_.

The fixed _classes_ are:

* String (Scalar)
* Number (Scalar)
* DateTime (Scalar)
* Boolean (Scalar)
* Array (Composite)
* Map (Composite)

There are additionally dozens of built-in _variants_, which are specific forms that an instance of a given class may take.
These options are enumerated in the respective sections for <<types-reference-scalar>> and <<types-reference-composite>>.

Users and applications MAY define <<custom-variants,custom variants>> in an SGYML data-type configuration file.

In SGYML and SchemaGraphy parlance, when an object's _type_ is called for, its accurate kind, class, or variant MAY be indicated.

This will typically come into play when a SchemaGraphy data schema defines or constrains the type allowance of a given property.

Such a schema may, for instance, define that a given property be a String, or else a specific kind of String, such as a URI.

Alternatively, the _type_ defined by a whole schema may be any variant of Map or the ArrayTable variant of Array.

In another case, a SchemaGraph may dictate that each item in a simple Array property itself be a Scalar.

Each of these examples will indicate the type requirement using a property called `type` in the schema definition.

==== "`Maps`"

In YAML format, everything SGYML calls _properties_ are technically in the form of a YAML "`mapping`": they express a key and a value.

And any valid YAML document's data content that is not strictly a scalar value or an Array (YAML "`sequence`") is in fact a mapping.

However, in SGYML parlance, a Map refers to an object that consists of one or more objects.

The following YAML document is itself a Map:

[source,yaml]
----
prop1: value
prop2: valew
----

Likewise, in the following example, the document itself is a Map, and the property `propA` is a Map.

[source,yaml]
----
propA:
  prop1: value
  prop2: valew
----

Whereas the properties `prop1` and `prop2` are Strings.
All of properties can also be called _objects_.

[[types-reference-scalar]]
==== Scalar Data Types Reference

The first broad category of data classes and variants is the _kind_ that is herein known as "`Scalar`".
These types are called "`scalars`" in YAML and also sometimes "`atomic`", "`simple`", or "`primitive`" in other languages.

include::_built/scalar-types-reference.adoc[]

[[types-reference-composite]]
==== Composite Data Types Reference

The second _kind_ of data classes and variants are called "`Composite`" in SGYML.
Other languages refer to these as "`collections`" (YAML) or more commonly "`enumerable`" or "`complex`".

include::_built/composite-types-reference.adoc[]


== SGYML-specific YAML Syntax

SGYML introduces several new features to the YAML syntax that are not part of the YAML 1.2 specification, while also reintroducing one characteristic that was dropped in YAML 1.2.

=== Special Characteristics of YAML

Some lesser-used aspcts of YAML that are not universally supported by YAML parsers SHOULD be supported by SGYML parsers.

[[map-merge]]
==== Map Merge Capability

Though the mapping merge capacity introduced in YAML 1.1 specification was removed in YAML 1.2, SGYML reintroduces it.
Supportive tooling SHOULD support merging SGYML Maps into other SGYML Maps.

[[sgyml-directives]]
=== SGYML Directives

Dynamic properties indicated with a `$` prefix in the keyname are used to indicate dynamism.

The SGYML Specification (this document) designates several reserved keynames that indicate a given flag or dynamic behavior.

[[standard-directives]]
==== Standard SGYML Directives

Several SGYML directive properties are built-into the SGYML standard and are therefore reserved properties keywords.
This section is an overview; each directive has its own section below.

`$sgyml:`::
Indicates a given data object is to be processed using an SGYML parser.
This is optional, typically used to indicate to tooling that the data needs to be specially handled.
Used either as the parent property of an SGYML object or as a commented-out first line of a document to be interpreted as SGYML.

`$schema:`::
Indicates a SchemaGraph for governing a data object.

`$payload:`::
Indicates a schema-governed data object.

`$ref:`/`$refs`::
Transcludes one or more data objects, either internal (elsewhere in the document) or external (in a separate document).
Uses either a URIX (URI with fragment paths )

`$import:`::
From within a schema, transcludes an additional schema or schemas.

[[sgyml-yaml-tags]]
==== Standard SGYML Tags

SGYML employs several custom YAML tags that have a specific meaning in the SGYML context.

`!sgyml`::
This YAML tag indicates that a given document or object is to be handled as SGYML.

`!ignore` or `!sgymlignore`::
This tag indicates that the flagged property is to be dropped during SGYML parsing.

[[custom-sgyml-directives]]
==== User-defined SGYML Directives

Applications that support SGYML formatting MAY define custom directive properties.

==== Keyname Conventions

SGYML suggests several keyname or path conventions for property names.

Use an `$anchors` block anywhere for establishing YAML anchors for referencing later in the document.
_This block SHALL always be removed during SGYML parsing._

Use `+++#$defs+++` or `+++#$components+++` paths from the root of the local file as a data path for storing cross-referenceable content.
This is technically comment code, but an SGYML parser will detect it as a directive for implanting if you wish to use `$ref` directives to transclude relative or remote objects.

[NOTE]
SGYML recognizes specific commented-out syntax precisely so you can enhance your existng files with SGYML features while using those files with legacy tooling.

=== Object Cross-references

The value of a `$ref` directive property can either be a URIx String or a Map containing one or more properties that further configures the reference.

The optional properties of a Map-formatted `$ref` directive:

`source`:: The URIx of the reference target (required).
`vars`:: Variables with which to parse the target document.
`lang`:: The templating language of the target document.
`default`:: Value to impose if the `$ref` is not found.


== Data "`Processing`"

SGYML supports two types of data processing.
The first is _resolving_ any SGYML directives and YAML syntax, such as handling tags and aliases.

The result of "`resolution`" of an SGYML document is a complete and valid YAML object with no SGYML-specific artifacts remaining.

While the source of any SGYML document is typically either valid YAML or templatized YAML (with templating markup mixed in, though this is technically not part of the SGYML specification except to _accommodate_ templating), the end result of such a document is just a data object (typically a YAML "`collection`": a "`mapping`" or "`sequence`").

Here is the procedural order for resolving an SGYML document into a YAML document:

. *resolution* (<<stage-resolution,details>>)
.. *dereferencing*: process any `$ref`/`$refs` directives, transcluding data from cross-referenced objects (<<stage-dereferencing,details>>
.. *substitution*: replace any aliases with their established anchors, including <<map-merge,merges>>

. *loading* (<<stage-loading,details)>>
.. *ingest*: uptake the source and convert from document to data
.. *interpretation*: subject document gets converted to SGYML tooling data instances, with type inference/assignment and any tags read and applied

. *parsing*: (<<stage-parsing,details>>)
.. *expansion*: apply any default values or join relationships to the data object (<<step-expansion,details>>)
.. *validation*: ensure the data object conforms to the schema (<<step-validation,details>>)

[[stage-resolution]]
=== Resolution Stage

Dereferencing reconstructs an SGYML document into a YAML document.

Resolution happens according to a schema.
If no specific schema is assigned, then the extremely permissive default SGYML schema is applied to the object, only constraining such things as cross-reference depth and disabling some features by default.

Schemas are only concerned with the source document if it contains cross-references in need of dereferencing.

[[step-dereferencing]]
==== Dereferencing Step

The most powerful means of adding content reuse is SGYML's expansion of JSON Schema's `$ref` keyword functionality.
Thereby, "`dereferencing`" is the process of transcluding data from cross-referenced objects, either elsewhere in the document, in a local file, or at a remote resource, when permitted.

[[step-substitution]]
==== Substitution Step

Substitution in data files is a YAML-specific process that involves replacing _aliases_ with their defined _anchors_.

In YAML 1.1, this process included the _merging_ of Map-formatted objects into other Map-formatted objects.
Most YAML parsers still honor the YAML-native merge key syntax and procedure, which originated in YAML 1.1 but was dropped in YAML 1.2.

If unsupported by platform-specific YAML libraries, SGYML-supportive tooling MUST add merge-key capability in.

[[stage-loading]]
=== Loading Stage

Once dereferencing and substitutions have been performed, the source _document_ is loaded into native tooling as _data_.
This is the last point at which YAML source files will be considered in YAML format, so YAML tags are evaluated here.

This is the first point at which an optional schema gets applied for any parsing that is called for by a schema.

Now the further manipulation of the subject data based on the governing schema can commence.

_If no schema is associated_, this is to be the final status of the data.
It is valid so long as the resolved and ingested YAML is valid.

[[stage-parsing]]
=== Parsing Stage

At this point in the process, the document is loaded as data, so there are no more YAML syntax/formatting elements to consider.

From this point forward, we are manipulating and evaluating data entirely according to the schema definition.

The processes of (1a) populating schema-governed data object's Map properties with any schema-defined default values and (1b) embedding any "`joined`" data from other objects ("`expansion`"); then (2) ensuring the entirety of that schema-governed object fits its schema ("`validation`") are fundamentally intertwined.

A schema-governed data object is of course _valid_ when any optional properties are missing.
And, of course, such an object remains equally valid after its defaults have been expanded.

Nevertheless, the appropriate order is to first _expand_ and only then _validate_ the object.

[[step-expansion]]
==== Expansion Step

This is where implied properties with default values are imposed on the data object.

It is also where join relationships between tabular data is processed.
See link:{schema-data-specification_www}/#cross-tabular-joins[the SchemaGraphy Data Schema Specification].

[[step-validation]]
==== Validation Step

At this stage, the schema-governed data object is evaluated for compliance with any rules or constraints established in the governing schema.
This is the process described in the SchemaGraphy Data Schema Specification, which is beyond the scope of this document.

For now, it is enough to acknowledge that SGYML data documents and objects MAY be subject to schema governance, and thus their post-resolution state MUST conform to the format expected and defined by SchemaGraphs.

[[custom-variants]]
=== Custom Variants

User-defined variants are made possible by a custom configuration.


== Supportive Tooling

Any technology that wishes to support SGYML data formatting MUST provide certain capabilities exactly as specified.

Applicable data objects are typically are formatted as YAML.
The subset of YAML documents that are further to be treated as SGYML format are referred to as [.term.term_sgyml-flagged]*"`SGYML-flagged`"*.

Any special features or treatment described in this Specification will require that the containing document or object be "`flagged`" for being handled as valid SGYML.

Flagging occurs in the following manners:

. *external flagging:* When a tooling or environment are configured to treat all or certain/specific YAML documents as SGYML-formatted.

. *internal flagging:* Using (1) the `$sgyml` directive property, (2) the `!!!sgyml` tag, or (3a) an in-document or (3b) in-file `$schema` block, which implies the sibling document/object is SGYML formatted, unless otherwise indicated (see SchemaGraphy Data Schema Specification).

=== Resolving SGYML Directives

If an object flagged as SGYML contains _properties or comments_ that begin with `$`, supportive tooling MUST treat them as dynamic SGYML properties.

Any reserved directives MUST be executed in place.

==== Schema Application

The appearance of property keyed as `$schema` in an SGYML-flagged data object will indicate that the object is governed by a SchemaGraph.

[source,yaml]
----
some_object:
  $schema:
    $ref: "./_schemas/names-arraytable.yaml"
  $payload:
    - name: "Alice"
      age: 32
    - name: "Bob"
      age: 24
----

In the above example, the object `some_object` is governed by the schema defined in the file `names-arraytable.yaml`.

=== Requisite Functionality

All supportive tooling MUST provide the following capabilities:

dereferencing:: The ability to process `$ref` and `$refs` directives, transcluding data from cross-referenced objects.

substitution:: The ability to replace aliases with their established anchors, including merges.

expansion:: The ability to apply default values or join relationships to the data object.

validation:: The ability to ensure the data object conforms to the schema.

table conversion:: The ability to convert an object between MapTable and ArrayTable formats.


