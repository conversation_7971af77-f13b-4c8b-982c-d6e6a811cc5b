= URIx Specification
:sectnums:
:toc: macro

URIx is an extended URI (Universal Resource Indicator) syntax designed to augment how paths and file contents are reference and retrieved by supportive systems.

All the enhanced URI features features are for specifying or annotating nested objects as URI fragments, so all URIx resources include a path to an internal, external, or remote parameter, usually to reference, retrieve, alter, or supplement its value.

This functionality is intended for use in environments where there is no risk of passing dangerous commands or queries to a third-party server or database.

.Example URIx
[source,uri]
----
./file.json#/component/nested$$https://example.com/schemas/nested-component.json
----

The above is an example of a reference to an object called `nested`, inside an object called `component`, inside a file named `file.json`, which exists in the same directory as the document in which this URIx exists (`./`).

Additionally, a JSON Schema definition at the URL `\https://example.com/schemas/nested-component.json` is to be applied to the designated object (`nested`).

URIx honors all paths designated in a JSON Pointer format, with additional syntax for:

JMESPath and JSO<PERSON>ath pointers::
Perform advanced data-path queries to indicate/retrieve deeply nested data, including within Arrays.

schema references::
Indicate and apply JSON Schema or SchemaGraphy schemas at the point of referencing a data object.

semantic tagging of target resources::
Pass tags along with referenced data using `!bang` tag notation.

template variable arguments::
When referenced documents include templating markup, preprocess them with arbitrary variables right in the URIx.

All of these features are fleshed out in this Specification.

toc::[]

== Background and Terminology

The URIx standard emerges from the SchemaGraphy project, which defines new ways to format, process, and govern data and text documents.

Because SchemaGraphs and SGYML data objects support `$ref` properties for importing/transcluding data from external or internal documents, extending the basic URI string with the power of specificity and instructions for augmenting or preprocessing the data is highly advantageous.

Hence the "`post-fragment`" string format for pointing to and further defining the handling of such objects.

=== Semantic Key Words in this Document

The key words "`MUST`", "`MUST NOT`", "`REQUIRED`", "`SHALL`", "`SHALL NOT`", "`SHOULD`", "`SHOULD NOT`", "`RECOMMENDED`", "`NOT RECOMMENDED`", "`MAY`", and "`OPTIONAL`" in this document are to be interpreted as described in link:https://tools.ietf.org/html/bcp14[BCP 14] link:https://tools.ietf.org/html/rfc2119[RFC2119] link:https://tools.ietf.org/html/rfc8174[RFC8174] when, and only when, they appear in all capitals, as shown here.

[[glossary-terms-of-art]]
=== Technical Terms of Art

The URIx standard depends on various terms of art that are used with particular meaning with regard to the specific technologies around it.
Herein, to the extent possible, the following words are used with the meanings annotated below.

[NOTE]
The following listing is ordered according to relationship of the terms and hopefully a sensible means of learning the information.
For an alphabetical arrangement, see <<glossary-reference>>.

include::_built/glossary-urix-by-relevance.adoc[]

Note that some of the above terms have _different definitions_ in the other specifications (SchemaGraphy data and text schemas and the SGYML format), and each such "`Technical Terms of Art`" section contains only a subset of all defined terms.
The listings and definitions are tailored for contextual relevance but also expediency.

=== Basic Data Types

For purposes of this specification, referenced data types shall hue to the data "`classes`" defined in SGYML, which are based on the types supported by YAML and JSON, with a few alterations of terms or definition.

These types are:

include::_built/data-classes-sgyml.adoc[]


== Components of a URIx Locator

A URIx locator is a reference that consists of the following components:

Base URI::
The location of a specific data document, whether (1) an _absolute URI_ locator with a schema (such as `https://`) and host (such as `example.com`), and so forth; (2) a _relative reference_ pointing to a nearby filepath.
Alternatiely, the base URI may be left blank for an _internal reference_, indicating the Fragment points to an object inside the same document.

URIx Fragment::
The part after the "`fragment`" (`#`) symbol, indicating the "`object path`", filters, tag, and schema.
This is where the URIx specification defines the entire string breakdown.

So a URIx locator might look like this:

[source,uri]
https://example.com/data/static.json#/tier01#array_property??slug=unique-entry-id!uniq

The above example indicates an absolute remote path as well as a path to a nested Object within the `static.json` document which is filtered down to an individual object within an array.

Or it might look something like:

[source,uri]
./file.json#/main_object#people??name#first=jason&&name#last=porter$$./schemas/person.yaml

The above example finds a local file with a nested record for an individual, and it assigns the `person.yaml` schema to the resulting object.

The Base URI component is exactly according to the standard URI specification (RFC 3986), while the URIx Fragment component is the unique part of the syntax that is defined by this specification.

For an _internal reference_, the URIx might look something like:

[source,uri]
#secondary_object#nested_target

The above points to an object within the same document.

=== Fragment Breakdown

Within a URIx Fragment, several functional and semantic signifiers are available.

Nested path navigation::
Use `/` to separate nested tiers of the data-document structure.

Root anchor designation::
Use `#` (fragment designator) to indicate the root of a document's data contents.

Schema assignment::
Use `$$` to indicate a valid schema URI/path, optionally with its own URIx Fragment.

Semantic tagging::
Use `!!` to specify tags to pass along with the referenced data.

Template parameter assignment::
Use `^^` to designate a series of key-value pairs for pre-parsing a data file that is templatized.

== Syntax

Here is a complex example of a URIx, including deeply nested objects and query filters.

[source,uri]
https://example.com/data/static.json#tier01/array_property?slug=unique-entry-id!uniq

The above URIx could be used to locate an object in the following document source.

[source,json]
.data/static.json
====
----
{
  "tier01": {
    "array_property": [
      {
        "slug": "unique-entry-id",
        "name": "Entry One",
        "description": "This is the unique entry.",
        "details": {
          "type": "example",
          "value": 123
        }
      },
      {
        "slug": "another-entry-id",
        "name": "Entry Two",
        "description": "This is another entry.",
        "details": {
          "type": "example",
          "value": 456
        }
      }
    ]
  }
}
----
====

Because of the `!uniq` tag at the end of the URIx query string, we will return only one result as a Map (Object).
By default (without the `!uniq` tag), the query would have returned a 1-item Array.


[NOTE]
URIx-supportive tooling MUST request the entire resource for an external or remote URI document and validates it before trying to select a nested object or value.

=== Object Path

Nested objects are either Maps (Objects) or Arrays containing Maps (Objects).

Use the `#` (fragment symbol) to designate the root tier of a data document structure.

=== Query Conditions

Indicating items within Arrays requires a query-like method, since by definiton Array items are unnamed.
 
A `#` separator is required even when the entire fragment is a query.

==== Operators

[cols="1m,2a"]
|===
| ==
| equals

| !=
| does not equal

| >
| greater than

| <
| less than

| >=
| greater than or equal to

| \<=
| less than or equal to

| &&
| logical _and_

| \|\|
| logical _or_

| (...)
| compound conditions

| ~/.../
| matches Regular pattern

| !~/.../
| does not match pattern

| [...].contains(...)
| array contains value

| [...].excludes(...)
| does not contain value
|===

The `??` pattern is used to introduce query conditions for filtering arrays.

[source,uri]
main_array?id=1

==== AND Operator

The `&&` pattern is used to combine multiple conditions within the same level.

[source,uri]
id=1&hide!=false

==== OR Operator

The `||` pattern is used to specify alternative conditions.

[source,uri]
?status=active||status=pending

==== Not Equal Operator

The `!=` pattern set is used for not equal conditions.

[source,uri]
?hide!=false

==== Matches Operator

The `~/.../` pattern is used to match a property value with a Regular Expression pattern.

[source,uri]
?prop1~/alpha|beta|omega/

Use any simple RegExp pattern between the `/` symbols.

==== Grouping Conditions

Use `(...)` to group conditions.

[source,uri]
?(id=1&status=active)||(id=2&status!=pending)

=== Query Returns

Queries using the above-described syntax will return results as Arrays when successful, unless specially flagged to return only 1 result.

Adding `!uniq` as a tag to any query string will return a single result as a Map object or else will return a nil response if no record is found or if more than one record is found.

Queries that return no results return an empty Array or else `Null`, if the `!uniq` flag is used.

==== Deeply nested Query Results

A URIx fragment may include multiple query strings to penetrate multiply nested maps.
These will return items from the last Array that match the query arguments.

=== CSV/TSV/DSV Object Identification

Delimiter-separated data files are essentially just Arrays of Maps or Arrays, so they can be directly queried and/or filtered.


As the target document includes a heading row or headings are passed in the URIx, each row of a DSV document can be treated as a Map.
They can also be queried using a numeric column identifier in the case headings are not available or are unknown.

An entire row or range of rows may also be selected.

.Example URIx that filters by header reference
[source,uri]
data.csv#??some_col_header='this value'

The above example returns all rows where the column `some_col_header` has the value `this value`, as an Array of Maps.
The target CSV file must begin with a row of column header names.

.Example URIx that provides header names for reference
[source,uri]
data.csv#??name='this value'^id,name,phone,email

The above example returns all rows where the column `name` has the value `this value`, as an Array of Maps, using the provided column names as keys.

.Example URIx using a numbered column identifier
[source,uri]
data.csv#??col[2]=5!uniq

The above returns a Map of the row where the second column has the value `5`.

.Example URIx selecting a specific row
[source,uri]
data.csv#row[3]

The above returns the third row of the CSV file, as a Map if it has headers or else an Array if it does not.

.Example URIx selecting a specific row with column names provided
[source,uri]
data.csv#row[3]^^id,name,phone,email

The above returns the specific row as a Map with the provided column names as keys.

.Example URIx selecting a specific row and column
[source,uri]
data.csv#row[3][2]

The above example returns the _value_ of the specified cell.

.Example URIx selecting a specific row and column by header
[source,uri]
data.csv#row[3][colname]

The above example returns the _value_ of the specified cell, as long as `colname` exists as a header.

=== Schema Assignment

Any selected data MAY be governable by a data schema.

A `#` separator is required to designate a fragment even if the entire fragment is a schema.

For example, `object.json#$$./schemas/object-schema.yaml`

The `$$` pattern is used to flag the URIx of a schema or schemas to be applied to a given data object.

By default, any schema or comma-delimited schemas are applied using `all_of` logic, meaning the data is expected to be valid under _all_ the listed schemas.

The following logical indicators of schema compliance may also be used:

`$$all_of:...,...`::
The default behavior of a `$$`-flagged URIx string.
+
[source,uri]
$$all_of:./path/schema.yml,https://example.com/json-schemas/schema2.json

The above is the same as:

[source,uri]
$$./path/schema.yml,https://example.com/json-schemas/schema2.json

`$$any_of:...,...`::
Selected data must match any  of the listed schemas.

`$$one_of:...,...`::
Selected data must match one and only one of multiple schemas.

`$$none_of:...,...`::
Selected data must not match any of one or more listed schemas.

Any of these may be followed by one or more URIx strings, separated by commas.

==== Sub-schemas

Sub-schemas may also be selected using URIx fragments within schema strings.

[source,uri]
data/config.json#/settings$$./schemas/settings.yaml#$schema/config

The above would select a SchemaGraph object called `config` that is nested inside the main `$schema` object.

A JSON Schema sub-schema can also be assigned this way.

[source,uri]
data/config.json#/settings$$.schemas/settings.json#$schema/config

Schemas referenced in a URIx may be SchemaGraph definitions (SchemaGraphy schemas) or JSON Schema definitions.

A URIx always references a primary data object, even if that data object is itself a schema, as in when a SchemaGraph is imported.
For instance, value of the second line the following is a valid URIx reference in a YAML document.

[source,yaml]
----
$schema:
  $include: "./schemas/personnel.yaml"
----

In SchemaGraphy, a `$schema.$include` field always calls for a URIx, which can also just be a valid URI pointing to a valid SchemaGraph document with no fragment.

However, URIx formats also allows assigning a schema of either type (JSON or SchemaGraph) to the primary data object referenced by the URIx.
Here are some ways this is done:

[source,yaml]
----
$ref: "./data/config.json#/settings$$./schemas/settings.yaml"
----

The above reference points directly to an object called `settings` in a file called `config.json`, and it assigns the SchemaGraph that makes up the entire `settings.yaml` file.

[source,yaml]
----
$ref: "./data/config.json#/settings$$./schemas/settings.json"
----

The above reference points to the same object in the same file as the previous example, but it assigns a JSON Schema definition to that object.

[source,yaml]
----
$ref: "./data/config.json#/settings$$./schemas/config.yaml#/settings"
----

The above reference points to the same object as the other examples, but it assigns a SchemaGraph definition object called `settings` from within a schema file called `config.yaml`.

[source,yaml]
----
$ref: "./data/config.json#$$./schemas/config.yaml"
----

The above reference points to the entire `config.json` file and assigns the schema file `config.yaml` to govern it.

=== Template Parameters

It is common for SGYML documents to be "`templatized`", meaning they are sourced as a mix of YAML and a templating markup format, such as Liquid, Jinja2, Handlebars, or others.
The templating markup must be parsed ahead of time to render valid YAML/SGYML.

Variables can be passed to such templates upon ingest.
While most templating engines can handle complex/composite data objects, a URIx can only argue simple parameters with scalar values.

The `^^` notation is used to designate a parameter string.

Parameters are then passed using a `&key=value` format.

[source,uri]
----
./file.yaml^^&env=staging&version=1.2.1
----

Template parameters can also be passed to templatized SchemaGraph definition documents.

[source,uri]
----
./data/object.json$$./schemas/main.yaml^^&type=A&locale=en/us
----

[source,yaml]
----
data:
  source: ./data/object.json
  schema: 
    file: ./schemas/main.yaml
    vars:
      type: A
      locale: en/us
----

The above source/schema combination, presumably entered into a URIx-supportive utility, would be the same as:

[source,yaml]
----
data:
  source: ./data/object.json#$$schemas/main.yaml^^&type=A&locale=en/us
----

The schema fragment is referencing a specific schema definition document and passing `type` and `locale` arguments to it for pre-processing.


== Interpretation Order

A URIx must be resolved in a specific order to ensure proper data reference and retrieval.

The order is as follows:

. base URI resolution
. fragment identification
. fragment model parsing
. 

=== Base URI Resolution

Determine the base URI, which will either be a reference to a file or a local object (internal to the document, relative to the document root).

.Example relative path URI
[source,uri]
./path/file.json

.Example absolute path URI
[source,uri]
/etc/files/file.json

.Example absolute URL URI
[source,uri]
https://eample.com/path/to/file.json

.Example internal URI
[source,uri]
#components

In the final example above, the URI is _only_ a fragment, which indicates the object referenced by the fragment is in the current document, at a path relative to the document's data root.

=== Fragment Identification

The entire portion after the first `#` in any URIx is the URIx Fragment, which may consist of an Object Path String and/or a Schema String.

The Object Path String MAY include a Query String.

The Schema Path String MUST include one or more schema references, each of which MUST be a valid URIx.

[source,uri]
#main_array??id=1&hide!=false||status=active/ub_array??prop1~alpha|beta|omega/name$$./schemas/my_schemas.yaml#schema_2

=== URIx Fragment Model/Parsing

The URIx Fragment is broken down into two main parts -- Object Path and Schema String -- each of which can be made up of several components.

[source,yaml]
----
$ref: "#main_map/main_array??id=1&&hide!=false||status=active/sub_array??prop1~alpha|beta|omega!uniq$$./schemas/my_schemas.yaml#schema_2,./schemas/my_schemas.yaml#schema_3"
----

Object Path::
`main_map/main_array??id=1&&hide!=false||status=active/sub_array??prop1~alpha|beta|omega!uniq`
+
In a YAML document, the object path would resolve to something like:
+
[source,yaml]
----
main_map:
  main_array:
    - id: 1
      status: active
      sub_array:
      # both objects below will be returned as an Array
        - prop1: omega
          name: Robert
        - prop1: alpha
          name: Samuel
----

Schema String::
`./schemas/my_schemas.yaml#/schema_2,./schemas/my_schemas.yaml#$$schema_3,.schemas/general.json`
+
This Schema String includes two SchemaGraph definitions and a JSON Schema definition, ALL of which apply to the object defined in the Object Path.

[NOTE]
These examples show SGYML `$ref` proprties, but any field, argument, or property (such as in a form or CLI or API) COULD be designated to support URIx strings.



==== Tagging

URIx introduces a means of adding arbitrary arguments to object paths and queries through _tagging_ within the URIx locator string.

A tag can be placed after a nested-object string, after a query string, or after a schema string, to be passed along to processors as an argument flag along with the string fragment with which it is associated.

[source,uri]
./some-file.json#/object-a/array-1??id=4!uniq$$./schemas/array-schema.yaml!latest

In the above example, the `!uniq` tag is associated with the query string, and the arbitrary `!latest` tag is associated with the schema string.

The only built-in tag that MUST be supported is the query tag `!uniq`, which indicates one and only one match is expected, and thus the query should return a Map (or an Array in the case of an unlabeled table such as DSV), or else `nil`.

Applications may honor other tags, but the recognition of all other tags is OPTIONAL for compliance with the URIx standard.

=== Schema Application

URIx-supporting systems SHOULD enable the application of JSON Schema AND SchemaGraphy schemas to designated objects.
If a schema is referenced using the `$$` syntax, supportive tooling SHOULD apply that schema for validation and default-value resolution, at a MINIMUM.

However, if URIx-recognizing tooling does not support schemas, it does not have to take action in response to a `$$`-designated schema or schemas.
Support for both JSON Schema and SchemaGraphy is OPTIONAL.

=== Extended Reference Capabilities

Supportive tooling MAY add support for data document types in addition to JSON and YAML, both of which all URIx-compliant tooling MUST support.

For instance, AsciiDoc files are typically supported, meaning the `.adoc` (or similar `.asciidoc` or `.ad`) files must be rendered so attributes and content objects may be referenced.

Any document attribute can then be referenced as `file.adoc#/attribute_name`.

Any term from the document can be indicated with `file.adoc#/section-id/term%20string`.
This will capture:

[source,asciidoc]
----
[[section-id]]
term string::
This is the value of the `term string` term indicator.
----

Any document tag can be indicated with `file.adoc#//tag-string`.
This references tagged content such as:

[source,asciidoc]
----
// tag::tag-string[]
tag-string::
This is the content that will be retrieved, including the line above.
The example shows distinctly selecting for the tagged content rather than the term-keyed content.
// end::tag-string[]
----

Because AsciiDoc is not supported everywhere, this syntax for capturing data and content from `.adoc` documents is OPTIONAL.
If you wish to offer this or similar support in URIx-supportive tooling, you are RECOMMENDED to stick with a syntax that adheres to the URIx pattern and respects however data is keyed and referenced in your target objects.

