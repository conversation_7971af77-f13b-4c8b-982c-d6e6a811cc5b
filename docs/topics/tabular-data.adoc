= Tabular Data Objects

There are two main types of tabular data that can be defined by SchemaGraphy schemas.
These are known as *MapTable* and *ArrayTable*.

Both of these object types perform the same function, but they take a slightly different form.
Both types are used to store serialized Maps, usually containing two or more similar or identical sets of property keys (dictionaries), typically with variant values.

The only real difference is in how the identifying keys for each such "`record`" are formatted.

.Example MapTable format
[source,yaml]
----
key1:
  prop1: value1
  prop2: value2
key2:
  prop1: value3
  prop2: value4
----

.Example ArrayTable format
[source,yaml]
----
- slug: key1
  prop1: value1
  prop2: value2
- slug: key2
  prop1: value3
  prop2: value4
----