= The Schema Model

Under SchemaGraphy, both content and data schemas use the same basic structure.
Learning one should aid in learning the other.
The aim is accessibility and simplicity.

*Data schemas* can share a document with the data they govern, or they can be stored separately and imposed during data loading and parsing.

*Content schemas* can be stored separately as YAML files and referenced in the content file's front matter as a page property using `schema_source:` or an AsciiDoc document attribute using `:schema_source:`.
They can also be defined locally in commented-out YAML at the top of the content file, though external references are strongly recommended by convention.

Inline schemas for content such as topics may come in handy when the aim is to ensure a docs contributor stays within certain bounds for a particular document -- including a document compiled from multiple AsciiDoc source files.

[NOTE]
We use the term "`subject`" to reference both types of governable matter: _data objects_ and _text documents_.
These items are _subject_ to schema governance, though in most instances we state whether the Subject is a data object or a text document.

Next up, we cover each type in some detail, from the _why_ to the _how_ of each.
The next section, on data schemas, contains some information which might better help introduce content schemas later, but you should skip to <<content-schemas>> now if you wish.

== Data Schemas

Data schemas govern data objects, and they can be imposed on any data object that is supposed to match the schema.
Once applied, data schemas perform any of several valuable tasks:

* establish default values for properties
* constrain valid values for properties
* establish relationships between properties
* provide contextualized assistance to data entry and review
* constrain the size and scope of a _dataset_
* auto-generate JSON Schema and GraphQL schemas and queries

These objects _may_ be sourced in YAML files, but data schemas govern data _objects_ not data _files_ or data _formats_.
Graphy converts all governed data to generic objects _before_ validation.
This is true irrespective of whether the data was sourced in YAML, JSON, XML, or SQL, in flat files or in ether, or whether the source objects also conform to other type systems, including programming languages' native data types or SchemaGraphy's types.

Datasets are collections of data objects, technically serialized as Arrays of Maps.

When arranged into a dataset, data objects are called _records_ or _nodes_.
It is possible to ingest a number of Map-formatted YAML files whole, compile them into an Array of said Maps, and apply the schema to that Array.
All that matters to SchemaGraphy data schemas is that you feed them objects to govern -- it will tell you when you provide something unexpected.

[NOTE]
For clarity, the term _parameter_ will be used to refer to a key/value pair that exists in a schema, whereas _property_ will be used to reference a key/value pair that exists in an data object governed by the schema.
So in the below listing, the markup `schema:`, `$ref:`, and even `properties:` all name schema _parameters_, whereas the parameters in the `properties:` block itself refer to _properties_ of the object type being defined.

While most of a schema's parameters define a single data object type, when applied to a dataset, a schema governs the constituent members of that set: data objects organized into an array.
When a data schema is applied to a collection of data objects, each individual object -- a record or node -- is expected to conform to the schema's rules, unless exceptions are made.

[NOTE]
In these models, parameter names wrapped in `<` and `>` symbols are placeholders for arbitrary examples; they represent any properly formatted string to use as a name for a key being set or referenced.

.Model data schema with parameter explanations instead of values
[source,yaml]
----
include::../snippets/schema-model-data.yml[tags="model"]
----

Above is a model reference for all the valid parameters in a LiquiDoc data schema.
Every single one of them is optional, as is their order, though _nesting levels are strictly enforced_.

[NOTE]
LiquiDoc validates all schemas before applying them to data objects, ensuring they conform to the above format.

Let's take a look at the blocks that make up this data schema.

// Import the built file extracted from the data also found in `schema-model-data.yml`
include::../snippets/_built/schema-model-block-defs.adoc[]

== Content Schemas

Content schemas are designed to look and work a lot like data schemas.
Most of their structure is overlap, and conceptually they are highly parallel.

While data schemas govern data _objects_, content schemas govern _documents_.
Similarly to data, humans are still needed to evaluate the _quality_ of schema-governed content.
Schemas define the shape, order, and syntax of our content, precisely so writers and reviewers can focus on its accuracy, clarity, and relevance.

Well-defined schemas should also take the “guesswork” out of modularized writing and content management.
When it comes to getting started with technical documentation, contributors are better off the less they _must_ know about their content's place in a hierarchy of topics, pages, chapters, and volumes of intricately nested material.
And to the extent tooling can prompt proper formatting, *content input systems* and *linters* will benefit from first being aware of the intended shape and context of the content they process.

As long as a data object is transformed only into a new data object or objects, a _data schema_ can be applied to that data.
Once data becomes content, governance requires a content schema.
Unlike data, with its universal form in which we care to evaluate it, we tend to engage with _content_ across up to _three_ layers.

First is our *lightweight source markup* -- probably AsciiDoc, but technically our schema could govern Markdown, restructuredText, or any other source markup.
Second is our *converted HTML source* -- that's the stuff a web browser uses to generate the third state of content: *rendered rich text*.

Because each of these three states presents differently, they give us three opportunities to make sure content comes in the shape we expect.

.Use Content Schemas to Validate Transformed Data
****
Data tends to convey core product information that _must_ remain true no matter its travels and permutations.
Later-stage validation of data is essential to a mature, continuously integrated documentation environment.

Consider the three content layers to be the second, third, and fourth opportunities to govern _data_ that has been parsed and rendered.
If data is transformed by Liquid, Asciidoctor, or other converters after it is ingested, those changes may result in output that needs new evaluation.

When that ingested data is expressed in AsciiDoc, HTML, or rich text as a result of these conversions, it is open to governance, in context, using a _content schema_.
Use content schemas to inform linters that will bug-proof your converted data at the relevant stages.
****

=== Content “Elements”

Whereas data comes in (sometimes nested) _objects_, content comes in (sometimes nested) _elements_.
For lack of a better universal term, we will use _elements_ to describe “bits” or “chunks” of content source that we can expect to be routinely conveyed.
That is, a content _element_ is a piece of markup syntax and/or marked-up content itself that we can latch onto in order to apply governance.

The definition of an element in this system is highly freeform.
Content schemas empower you to define character patterns that, when found in processed content, determine an “element” of a certain type has been found.
This is true for capturing AsciiDoc or other lightweight source markup as well as the rich-text renderings from HTML.

However, for the HTML layer itself, the concept of an _element_ is natively defined by the HTML (really XML) specification.
In HTML, an element is represented by an XML-style “tag”, any of which can be identified using link:https://www.w3schools.com/cssref/css_selectors.asp[_selector_ notation] such as `p.annotation` for any paragraph (`<p>`) element of the class `annotation`, or `summary#metastuff` for the `<summary>` element specifically identified as `metastuff`.

Let's take a closer look at content schemas.

.Model content schema with parameter explanations instead of values
[source,yaml]
----
include::../snippets/schema-model-content.yml[]
----

As you can see, content schemas are overwhelmingly similar to data schemas.
Just like with data schemas, every parameter of a content schemas is optional, allowing you to govern as little about your content as you choose.

The models diverge significantly in a few other places, so let's add or redefine some blocks.

<8> *schema.elements.<element>.context:*
For adding additional constraints concerning which content elements can be associated with which other elements.
Preventing admonitions from appearing inside sidebars would be an example use case.

<10> *schema.elements.<element>.format:*
These parameters constrain the permitted formats of content elements to ensure that all new content meets the schema constraints.

<11> *schema.elements.<element>.format.<layer>:*
Most element definitions are not much use without a `format.<layer>.det:` value, as this parameter describes how to detect the element in question.
Formatting checks can be targeted at any of three stages of content: (1) lightweight source, (2) converted HTML markup, and (3) rendered rich-text (as it appears in the browser).
These validation layers inform linters that carry out checks after Asciidoctor's validation and processing are complete, and hopefully long before end users' browsers get hold of it.

Content schemas are _missing_ several of the blocks data schemas support.
These include `value:`, `alias:`, and `schema:`.
Most elements are not parameterized (_description lists_ being one exception), so they do not have values to constrain.
You are naming them arbitrarily only in the schema; users will not enter `TIP: Some text here` the way they enter data in YAML, so you don't have to give them alternatives to `TIP:` the way you _might choose to_ do in YAML.
And content does not have subschemas, so the `$schema:` block is moot.

Finally, the concept of `context:` is nearly identical in content schemas, but it is applied a bit differently.
The `context:` parameters define how elements are arranged in the overall document.
Unlike selectors, which are source-language agnostic, context only works with AsciiDoc source, which obeys a standard architecture that can be further constrained by arbitrarily defined rules.

== Schema Imposition

The process of combining either type of schema with its intended target may be carried out for numerous reasons.
Let us review some of the expected applications for this _schema imposition_:

* validating the structure and content of ingested data objects
* exposing help to users entering data in forms, templates, or CLIs
* giving reviewers insights into the intended use of data properties
* setting default values for properties, to reduce the vertical expanse of small-data files
* limit or define the size and scope of a collection of records -- a _dataset_
* generate JSON or GraphQL schemas

*Data schemas* define and can be imposed on a given Map or any given MapTable or ArrayTable.
When assigned to an ArrayTable, the schema applies to every record (aka, "`node`" or "`item`") in the array, but its `dataset:` param can provide additional meta definition for the Array object itself.

*Text schemas* are imposed on _content_, which could be anything from an independently rendered admonition block to a complete book.
When used simply for validation, the process can occur independently of any production build activity.
For instance, in order to validate one chapter of a book distinctly from the rest (using a different schema, that is), simply run your validations against two separate builds, then perform a combined build without validation (or just validate against a final schema to ensure the overall structure).

=== When NOT to Use Schemas

LiquiDoc has different ways of seeing schemed data, as opposed to unschemed data.
The parsing processes give unschemed data the easiest time, passing it along to wherever you want, so long as it can be read as validly formatted _for the language it is in_.
If you do not impose a schema on your data, LiquiDoc assumes your _code_ (e.g., Liquid, JavaScript, AsciiDoc) handles the ingested data (JSON, YAML, CSV, XML, etc) just fine.
No problem -- do your thing.

There is not necessarily any need to schematize your data; if you do not recognize a real-world need among the prime uses listed in the last section, do not feel pressured to proceed.
LiquiDoc was used in production for years before data schemas were considered particularly helpful and finally made it onto the roadmap.

Harder than recognizing when you do not need a schema may be understanding just what a schema is not even intended for, at least under the lead dev's imagination.
The main thing schemas were _not_ designed for is making your work harder or more complicated.

=== Validating Ingested Data

When imposed during ingest, a schema validates all of the data it governs, returning an error message if any record is found to be invalid.
The LiquiDoc actions that take advantage of schemas will do so as long as they know where to find a schema to impose.

Schema definition happens mostly in YAML-formatted schema files or else in the data object's source file, could be JSON or even XML.
Schemas defined inline are particularly ephemeral: they only apply to `records:` array with which they are paired, and then they are forgotten for the remainder of the LiquiDoc build procedure.
These inline schemas cannot be applied to outside objects.

Independently defined schemas become _schema instances_ when they are processed, and they can be applied multiple times throughout a LiquiDoc build config.
When a new dataset of the same model is encountered, as with versioned docsets, the cached schema is applied to the new data object.

Otherwise, once a data object or dataset is processed against a schema, it becomes a _schemed object_ or a _schemed dataset_ and can be called upon as-is without revalidation.
Whether ingested data is schemed or unschemed, once you assign your data a token in your config, it will live on inside the config until overwritten.

[source,yaml]
----
  action: parse
  name: tag-articles
  data:
    - file: _data/articles.yml
      schema: _data/schemas/articles.yml
    - file: _data/tags.yml
      schema: _data/schemas/tags.yml
  query:
    gql: _data/queries/article-tags.gql
  builds:
    - token: articles_tags
    - template: _templates/liquid/articles-tagged.yaml
      output:  _build/snippets/_built_articles-tagged.adoc
----

This parse action results in a reusable schemed object (`articles_tags`) and an AsciiDoc-formatted file.

Like any data object, a schemed object will be maintained from the point it is established in a LiquiDoc build routine until either:

* the routine ends, or
* the schemed object is overwritten by a same-named token

This caching prevents duplicate processing of schemas as well as schemed data once validated.

This cacheing process is relevant to content schemas more so than to content itself.
LiquiDoc provides no way to carry content from action to action in a routine except by writing it to file.
Revalidation should not be an issue -- any content source validated once should validate indefinitely against the same schema.

=== Using a Content Schema to Validate a Document

The real value in validating content with schemas is in mixing and matching _expectations_ with _environments_.

[source,yaml]
----
{% for guide in guides %}
  action: render
  source: index.adoc
  data: attributes.yml
  schema:
    - tech-book-style-guide.yml
    - code-listing-style-guide.yml
    - platform-specific-terms.yml:{{guide.platform}}
  builds:
    - output: user-guide.html
{% endfor %}
----

This configuration will apply 3 schemas to the document, the third being derived from a subsection of the file `platform-specific-terms.yml`, found among child objects named after target platforms.

=== Feeding a Data Schema to a GraphQL Query

Let's look at a snippet of the schema that defines the `topics:` dataset of a LiquiDoc Ops site manifest file.
But let's start with that governed object, so you can see an example of what's happening.

.manifest.yml
[source,yaml]
----
$schema:
  $ref:
    source: manifest.yaml
    anchors: data/anchors-global.yaml
---
topics:
  - slug: install
    title: Installing our Software
    categories:
      - setup
      - os
    tags:
      - windows
      - linux
      - macos
    subtopics:
      - install-macos
      - install-windows
      - install-linux
  - slug: install-macos
    title: Install on MacOS
    categories: [setup]
    tags: [macos,installation]
    guides: [mac]
  - slug: install-windows
    title: Install on Windows 10
    categories: [setup]
    tags: [windows,win10,installation]
    guides: [win]
  - slug: install-linux
    title: Install on Your Favorite Linux Distro
    categories: [setup]
    tags: [linux,installation,debian,ubuntu,redhat,arch,apt,rpm,yum,dpkg,dnf,pacman]
    guides: [nix]

guides:
  - slug: win
    name: Windows User's Guide
  - slug: mac
    name: MacOS User's Guide
  - slug: nix
    name: Unix and Linux User's Guide

categories:
  - slug: setup
    name: Getting Started
  - slug: config
    name: Configuring the Product
  - slug: os
    name: Operating Systems
----

You can probably infer what that file designates: a 

.manifest.yaml -- schema for topics metadata manifest
[source,yaml]
----
$schema:
  type: Map

  properties:
    _meta:
      req: [topics] # these properties MUST be present
      acc: [guides,topics] # these properties MAY be present
    topics:
    $schema:
      type: ArrayTable
      items:
        type: Map
        properties:
          _meta: 
            idx: [slug]
            req: [slug,title]
            acc: [categories,tags,subtopics,guides]
          slug:
            value:
              type: Slug:LowerCase
              pattern: ^[\w\s\-]{5,35}
              
          title:
            type: String
            pattern: ^[\w\s\-]{5,35}
            uniqueness: unique
          categories:
            type: ArrayList
            items:
              type: Slug:LowerCse
              belongs_to: categories.slug
          tags:
            type: Array
            items:
              type: String
          subtopics:
            type: Array
            items:
              type: String
              belongs_to: topics.slug
          guides:
            type: Array
            items:
              type: String
              belongs_to: guides.slug
    
    guides:
      type: ArrayTable
      items:
        type: Map
        properties:
          _meta:
            req: [slug,name]
          slug:
            type: String
            pattern: ^[\w\s\-]{5,35}
            uniqueness: unique
          name:
            type: String
            pattern: ^[\w\s\-]{5,35}
            uniqueness: unique
    
    categories:
      type: ArrayTable
      items:
        type: Map
        properties:
          _meta:
            req: [slug,name]
          slug:
            type: String
            pattern: ^[\w\s\-]{5,35}
            uniqueness: unique
          name:
            type: String
            pattern: ^[\w\s\-]{5,35}
            uniqueness: unique
----

A simple schema like this provides lot of insights to anyone looking to automate based on such a set of instructions.

For starters, we can ask for topics and their categories and subtopics all at once, and expect the complete records for the nested objects to be situated within the parent records upon return, _in addition to appearing in their own place_.

[IMPORTANT]
Datasets of this kind will in fact effectively return *duplicate records* when references are replaced by actual nested data.
Therefore, they can be accessed at their original location (`topics.<topic>`) or at their nested location (`topics.<parent>.subtopics.<topic>`).

Approximating this effect in standard Liquid, we would need to acquire data from multiple objects individually and patch together what appeared to be a new object.

With GraphQL and our new schemas, these cross-object insights are baked in.
Ask for the properties you want from the various objects, and LiquiDoc now returns data objects that are as complete as you need.

No more of this:

[source,liquid]
----
{% for t in topics %}
  {% kids = "" | split: "," %}
  {% for st in t.subtopics %}
    {% assign kid = topics | where: "slug", st %}
    {% assign kids = kids | concat: kid %}
  {% endif %}
  {% cats = "" | split: "," %}
  {% for c in t.categories %}
    {% assign cats = categories | where: "slug", c %}
    {% assign cats = kids | concat: kid %}
  {% endif %}
  {{ t.title }} (
    {% for c in cats %}
      {{ c.name }} |
    {% endfor %}
    )
  {% for st in kids %}
    * <<{{st.slug}},{{ st.title }}>>
  {% endfor %}
{% endfor %}
----

The above code builds a Liquid array called `kids` that contains the children of the topic at hand.

With our schematized data ingest, GraphQL was able to create a new data object with complete information -- even duplicate information!

.New solution
[source,liquid]
----
{% for t in topics %}
  {{ t.title }} |
  {% for c in t.categories %}
    {{ c.name }} |
  {% endfor %}
  {% for st in t.subtopics %}
    * <<st.slug,st.title>>
  {% endfor %}
{% endfor %}
----

No kidding.
All the data preparation was done by GraphQL, as dictated by the LDSL data schema and a simple, automated query.

This doesn't just look and feel better -- it is drastically better aligned with development best practices for content management systems and related applications.
Now our query logic is truly separate from our content, where it belongs.
The object relationship model in our schema is doing its job, preparing objects for convenient expression in a simple templating language like Liquid.

=== Feeding Contextualized Help to Front-end Tools

=== Guiding and Validating Data Entry

== Dynamically Building Schemas with Templates

Schema templates impose a _preset_ or _default_ schema of sorts onto a specific schema-instance definition.
Schema templates can be stored anywhere, but they are always _files_ (and by convention use the `.yaml` extension).

When a template inclusion occurs, the calling schema definition names the resulting _schema instance_ -- an objectified schema, if you will, ready to be applied to target data or content.
Cached _instances_ of schemas can be imposed on as many _targets_ of their type (_content_ or _data_) as you wish, but no existing schema instance can be used as an import template into a schema definition.
Schema templates must be called individually and converted to schema instances, which are in turn applied to data objects.

[IMPORTANT]
Processing a template into another schema does _not_ make a _schema instance_ of the template's contents.
That is, schemas and schema templates are not cached and held as reusable objects themselves by LiquiDoc until they have been turned into schema instances.

Template import and schema imposition are two different actions.
If you want a schema that includes templated matter to impose on target matter, build the schema object as one step in the build procedure, and then impose it as needed.

