= Data Types

I have chosen an unconventional data typing system, but I truly believe it makes practical sense.

It is based loosely on YAML's own, with some alterations, as well as the definition of numerous variants based on real-world applications and the need to shorthand the determination and designation of common use cases.

This section contains notes on several of the choices I made, which are sure to be controversial.

== YAML-Based Data Typing

Because I strongly prefer to source my small-data files in YAML, I have chosen to let YAML do most of the work and set most of the terminology.

I have, however, added and altered some namespaces that are not part of YAML's (admittedly overwhelming) Specification document.

=== Data "`Objects`"

I have chosen to refer to _keyed instances of datum_ in our system as _objects_.
This means anything that can be represented by a variable -- any piece of named data defined/stored in YAML -- is an _object_ of one or another _kind_, and one or another _class_ and possibly _variant_.

Collectively, the kind/class/variant of a data object all constitute its "`type`".

=== Data "`Kinds`"

I have designated two broad _kinds_ of data types: _Scalar_ and _Composite_.

_Scalar_ objects are simple: Numbers, Strings, Booleans, and Nulls, corresponding exactly to how YAML specification uses "`scalar`", and mapping 1:1 to JSON's scalar types.
These are sometimes called "`simple`" or "`primitive`" types in other systems.

_Composite_ objects are what YAML calls _sequences_ and _maps_, which we call Arrays and Maps, plus all of their variants.
Other systems might call these "`complex`" or "`compound`" or "`enumerable`" types.

Effectively, the difference between a Scalar and a Composite is whether object of the given kind can be included in an ArrayList or StringList: Scalars can, whereas Composites cannot.
Notably, an ArrayList is itself a Composite (variant of Array), and a StringList is itself a Scalar (variant of String).
See <<arraylist-stringlist>>.

[NOTE]
YAML's 1.1 and 1.2 specifications use "`scalar`" and "`collection`" to refer to these two _kinds_ of data types, but YAML also considers "`sequences`" (our Arrays) and "`mappings`" (our Maps) as their own "`kinds`", whereas SGYML (and most other typing systems) recognizes a single non-scalar kind (Composite).

This works pretty well with YAML's typing system, but we make several <<augmented-yaml,augmentations>>.

=== Data "`Classes`"

Every data object has a _class_, and some have a _variant_.

With a caveat relating to Numbers, the recognized classes all correspond to a YAML and JSON counterpart, if not namesake, as follows:

[cols="1,1,1",options="header"]
|===
| YAML Spec | JSON | SGYML

| string (`str`)
| string
| String

| integer (`int`)
| number
| Integer (variant of Number)

| float (`float`)
| number
| Float (variant of Number)

| boolean (`bool`)
| boolean
| Boolean

| null (`null`)
| null
| Null

| sequence (`seq`)
| array
| Array

| mapping (`map`)
| object
| Map
|===

Each Data type has variants, which basically represent specific, common _formats_ of the data type.

For any Scalar type, the variants are essentially pattern-constrained versions of the type.

For instance, a property typed as Email will be a String that conforms to the Regular Expression pattern `/^[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+$/i`.
In another example, data typed as the Money variant must pass `datum.to_str.match(/^\d{1,15}\.\d{2}$/)`.

Composite-type variants (sub-types of Array and Map) must match a structural pattern, rather than a String-like pattern.
MapTable and ArrayTable variants will have certain arrangements of properties, for instance.

[NOTE#yaml-numbers]
While Float and Integer are variants of Number, they are distinct types in YAML and most data typing systems.
In SchemaGraphy, a Number can be whole or decimal, leaving flexibility for the format.

[cols="1,1,1,1,1,1,1",options="header"]
|===
| SGYML | Python | Ruby | Java | PHP | Rust | SQL

| String
| str
| String
| String
| String
| String
| VARCHAR

| Integer
| int
| Integer
| int
| int
| Number
| INTEGER

| Float
| Float
| Float
| Double, Float
| float
| f64,f62
| FLOAT

| Boolean
| Boolean
| Boolean
| Boolean
| boolean
| bool
| BOOLEAN

| Null
| None
| Nil
| null
| null
| None
| NULL

| Array
| list
| Array
| List
| array
| Vec
| TABLE

| Map
| dict
| Hash
| Map
| array
| HashMap
| ROW
|===

[cols="1,1,1,1,1,1,1,1",options="header"]
|===
| SGYML | C | C++ | C# | Swift | R | Go | JavaScript

| String
| char*
| string
| string
| String
| character
| string
| String

| Integer
| int
| int
| int
| Int
| numeric (double)
| int
| Number

| Float
| float, double
| float, double
| float, double
| Float, Double
| double
| float32, float64
| Number

| Boolean
| _Bool
| bool
| bool
| Bool
| logical
| bool
| Boolean

| Null
| NULL (pointer)
| nullptr (C++11 and later)
| null
| nil
| NULL
| nil
| null

| Array
| Array defined manually or using structs
| vector, array (from <array>)
| Array
| Array
| vector, matrix
| slice, array
| Array

| Map
| Struct with pointers
| map (from <map>)
| Dictionary
| Dictionary
| list
| map
| Object

|===

=== Data "`Types`"

The term "`type`" in SchemaGraphy refers to any or all of an object's _kind_, _class_, or _variant_, in that order of specificity.

When prompted to designate a _type_ for any property or object in SchemaGraphy, or when any such _type_ is referred to in documentation, the _variant_ assumes the _class_ and _kind_, and the _class_ assumes the _kind_.

=== Data "`Parameters`"

When we use the term _parameter_ in SchemaGraphy, we are talking about a _key-value pair_, where the key is a valid YAML key notation, and the value is a datum of a certain type.

If we ever refer to the "`kind`" or "`type`" of a parameter, we are actually referencing the _value_ of the parameter: the data object it represents.

In the YAML specification, everything to the right of a key demarcation (`key:`) is a _node_.
By this logic, the _content_ of a parameter is only one component of the node, anchors and tags being optional components.

In SchemaGraphy, we are generally only talking about the resolved value of a parameter.
We therefore generally stick to the terms _key_ and _value_, where _value_ is the useful datum being conveyed.

From the perspective of a SchemaGraphy _schema definition_, parameters seen as *properties* of a governed object.
Any Map object's parameters are all technically _properties_ if and they are expected by the schema in any way (including being rejected or disallowed)

=== Departures From YAML

I find the terms _collection_ and _sequence_ conflict with terminology I prefer to use for application descriptions, such as "`collections`" of content and "`sequences`" of operations.

I cannot abide calling what most languages consider an Array or a List a "`Sequence`".
These objects are _Arrays_, plain and simple.

What YAML calls a "`Mapping`" most serialization markup formats (JSON, HCL, CSON) call an Object, which is unhelpful, because it clashes with the concept of a named instance of datum, which SchemaGraphy also calls an "`object`" (as do many programming languages).
That is, any "`parameterized`" instance of a String or Number or Array or any other data type is an _object_, by our treatment.

This data type is better understood as a _Map_, which is (at least part of) the official nomenclature in many programming languages, including Java, C++, JavaScript, Go, and Rust.

=== Peculiarities of YAML

One widely panned feature of YAML is its recognition of three ways to express *Boolean* (truthiness/falsiness) values, where all of the following are _falsey_: `false`, `False`, and `FALSE`, and `no`; while the following all evaluate as _truthy_: `true`, `True`, `TRUE`, and `yes`.

If you want to ensure your `FALSE` or `yes` is recognized as a String, you must quote it: `"FALSE"` or `"yes"`.
This will come up in SchemaGraphy, as you will often need to pass an explicit `"false"` or `"true"` instead of `false` or `true`, and this goes for `"null"` as well.

Far more sensibly, YAML sees the value `1.20` as a *float* (floating-point integer) equivalent to `1.2`.
In order to use decimals unconventionally, such as to indicate the 20th revision of the first major release, you must quote the value: `"1.20"`.
Our `Float` type will permit a YAML _float_ type and it will force a , but if you want to be safe, you should wrap all Numbers you want treated as Floats in quotes, so they will work with your underlying system as expected.

While YAML will assume any series of numbers with more than one `.` character are a String, it is probably best practice to quote any such patterns.

[[augmented-yaml]]
=== Weakly Supported YAML

SchemaGraphy's approach to YAML usage effectively and very unofficially "`deprecates`" some YAML namespaces in favor of what I think is better terminology.

Where we replace a type with our own terminology or definition, you can still fall back on the original YAML types in your SchemaGraphs by listing the YAML nomenclature (`set`, `omap`, or `pair`) in the [.prop]`type` property of a `$schema` object or a [.ppty]`value:type`.

This is not recommended, however, because SchemaGraphy documentation standardizes around the new terminology and definitions so new users will have an easier time picking it up.
Having never encountered YAML's precise terminology "`in the wild`" (an existing user-facing implementation), it does not seem like it has ever been popularized successfully.

Hopefully, our alterations will make a standard data-typing system easier to pick up.

[[arraylist-stringlist]]
==== ArrayList and StringList types

We enable the treatment of YAML strings consisting of comma-delimited series of [.term.scalar-data]_scalar-typed_ data objects without square brackets (StringList) as special cases of Arrays and Strings, respectively.
Official SchemaGraphs will not use or honor these types in underlying tooling, and all instances must be ArrayLists (proper YAML _sequences_ of strings, numbers, or Booleans).

However, because enabling user entry of multi-item lists without requiring brackets is highly advantageous in some settings, we have chosen to support this feature in SchemaGraphy.
This is especially useful for defining CLI tools or form-field interfaces that might enable value entries delimited by commas (or other characters).

An ArrayList is simply an Array (or YAML sequence) that only contains Scalar values.

A StringList, on the other hand, is a String that contains one or more comma-delimited Scalar values.
Supportive tooling will convert a StringList value to an ArrayList when it is loaded.

[[maptable-arraytable]]
==== MapTable and ArrayTable

YAML has a number of types that we rename in SchemaGraphy using more straightforward YAML syntax, including link:https://yaml.org/type/omap.html[_omap_] (which we call an ArrayTable), link:https://yaml.org/type/pairs.html[_pairs_], and link:https://yaml.org/type/map.html[_set_].
These types can be enforced by a SchemaGraph, as `Dictionary`

=== Other Data Characteristics

The data type Null is a special case of Scalar where a parameter is defined but given no value.
In YAML, a key with no value is considered Null type.

In SchemaGraphy, an object that is supposed to contain an Composite value but in fact has no value, will be considered _nil_, which is the same as not existing and evaluates to _falsey_ in most languages.
To indicate that such a value is merely _empty_, use `keyname: []` for Arrays and `keyname: {}` for Maps.

So a governed String property with no value or with `Null` will be considered _empty_, and an object expected to be a Number or Boolean will be considered invalid _falsey_ when valueless.

So an object that is defined but assigned no value will always be different from an object that is defined and considered empty.