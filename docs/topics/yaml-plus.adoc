== Enhanced YAML Features

SchemaGraphy markup (SGYML) adds quite a bit of optional power to the parsing of YAML-formatted documents.

It enables references (pointers) between objects of all <<data-types,types>>, including across documents, even remote files on the Internet.
This means pulling in not only whole data objects but also segmented portions of entire text/code documents.

=== Document/Object Transclusion with Pointers

YAML files can get unwieldy, and YAML itself does not have a method for cross-referencing or concatenating YAML documents.

`$ref:` or `$refs`::
Typically used to point to one YAML or JSON document or segment thereof.
Merges the target property in place (see below).

.Example pointer URIs.
[source, yaml]
----
$refs:
  - './definitions.yml#/some_property' # embeds the value of this property from nearby file
  - '#/definitions/some_property' # embeds the value of this property from same document
$ref: 'https://example.com/some_file.yml' # embeds the entire contents of the file
----

When used anywhere in a subject YAML document, the `$ref: #/path/to/property` itself is fully replaced with the value of the target property.

Any sibling property to the `$ref` property gets merged into the referenced value, assuming it is a Map value to begin with, which is the only data type that permits _siblings_.

Any commented out `$ref`/`$refs` property gets replaced with the _uncommented_ target object.

Any Map propertu can contain a `$ref` _and_ a `$refs` property, but only one of each.

Unless otherwise configured in a SchemaGraph, each `$ref` and `$refs` property may contain either a single URIx string, a Map with a `source` and other properties, or an Array of either or any of these types.

==== Additional Syntax Options for $references

Many uses of the `$ref`/`$refs` properties will necessitate the passing of arguments to the target file or the processes manipulating it.

Available keys of the `$ref` property are as follows:

[.ref-properties-dl]
`source`::
The source URI.
+
Needed any time the value of `$ref` or any individual item in a `$ref`/$refs` Array is a Map rather than a URI.

`lang`::
The templating format of the target document.
+
When determinable from the filename, this will be inferred.
WHen not reflected in the target filename, the template will either be parsed and rendered with the default engine (Liquid unless user-specified) or else the value of this property.

`vars`::
A Map of variables to be passed to the target document.
+
Can be AsciiDoc attributes when rendering AsciiDoc for data extraction.
Whenever this property is included, the designated or default engine will try to render the document.

=== Array Merging and Concatenation

Similar to YAML 1.1's `<<` merge key, SchemaGraphy adds the ability to concatenate Array objects.

In order to maintain strict adherence with the YAML specification, inserting multiple items into the array must be done with commented-out `$ref` properties.

.Example Array merging and concatenation
[source,yaml]
----
my_list:
  - c
  - d
  - e
  - f
my_array:
  - a
  - b
  - c
  # $ref: "#/my_list"
  # $ref: "./some-other-file.yml#/some_array"
----

Assuming the file `some_other_file.yml` contains:

[source,yaml]
----
some_array:
  - f
  - g
  - h
----

Then the above will resolve to:

[source,yaml]
----
my_array:
  - a
  - b
  - c
  - c
  - d
  - e
  - f
  - f
  - g
  - h
----

If you wish to merge the arrays instead of concatenating them, call the `$ref` property as a map with `source,` `strategy`, and optionally `key` properties.

[source,yaml]
----
my_array:
  - id: a
    text: Some text here.
  - id: b
    text: More arbitrary text.
  - id: c
    text: Some more text.
  # $ref:
  #   source: "#/my_list"
  #   strategy: merge
  #   key: "id"

my_list:
  - id: c
    text: Some different text to override.
  - id: d
    text: Some more different text.
----

The result would be:

[source,yaml]
----
my_array:
  - id: a
    text: Some text here.
  - id: b
    text: More arbitrary text.
  - id: c
    text: Some different text to override.
  - id: d
    text: Some more different text.
----

For Arrays of scalars, no key property is applicable, but the strategy can still be `merge` or `ignore`.

[source,yaml]
----
my_array:
  - a
  - b
  - c
  # $ref:
  #   source: "#/my_list"
  #   strategy: ignore

my_list: [c, d, e, f]
----

The result would be:

[source,yaml]
----
my_array:
  - a
  - b
  - c
  - d
  - e
  - f
----

==== Array-Combining Properties

For reference, the properties for determining how "`duplicate`" items are handled when Arrays are combined are defined as flows:

[.ref-properties-dl]
`strategy`::
The merge strategy: `replace`, `duplicate`, `ignore`.
+
For use when target data is an Array.
Designate whether to let one object override corresponding items in the other.
Use `replace` to let items in the target Array overwrite the calling Array, `ignore` to keep the corresponding item from the calling Array, or `duplicate` to append the new item anyway.
Defaults to `replace`.

`key`::
When merging two ArrayTables (Arrays of Maps), the property to consider the unique identifier of the Map.
+
If the key is not found in the target Array, the item will be appended to the end of the Array.
If the item is found in the calling Array, the `strategy` property will determine whether to append the item from the target object anyway (`duplicate`), keep the calling item (`ignore`), or the target item (`merge`).

Merge keys and strategies may also be defined for any Array object in its governing Schema.

=== URIx Pointers

The values of any `$ref` and `$refs` properties in an SGYML document may be formatted as URIx strings: URI references with specially formatted fragments for designating nested objects and even configuring how to consume them.

A proper URIx is a valid URI base path optionally followed by a `#` character and any of the following:

* an object-path fragment (nesting delimited with `::`)
* an Array filter (query) designated by `?` and parameters/operators
* a schema designator, itself a valid URIx following a `$` character
* tag arguments designated by `!` characters


[[template-rendering]]
=== Template Parsing and Rendering

Transcluded data documents can be templates that need to be rendered before they are included.
If your target document is templated in Liquid, ERB, or AsciiDoc and you wish to process the document before including it, you can designate the `lang` property in the `$ref` object.

==== Target Source Types

If the targeted source is a conventional templating language like Liquid, Jinja2, ERB, Haml, Mustache, or Handlebars, template rendering will be performed on the target document before it is included.

[NOTE]
The template language will be determined by the `lang` property in the `$ref` Map.

If the targeted source is AsciiDoc, the document will only be converted if the `$ref` operation is collecting data (attributes).


==== Processing Order for Pointers

SGYML parsers handle YAML and JSON documents imported using pointers like `$ref` variously depending on the format and content of the node.

If a targeted source is a template in need of _template rendering_ or _pointer resolving_, this will be performed on the target document prior to importing the content.

No resolution of YAML syntax will be performed on the target document, other than the optional removal of comment lines and evaluating the validity of the _syntax_.
The entire target document is not validated during the import process, so the object only needs to be valid in the context of the _calling_ document/object. 

Only then will any dynamic YAML features such as anchors, aliases, and merges be resolved.
Aliases not defined as anchors in the target document will therefore be valid as long as those anchors are pre-defined in the calling document.

This handling is determined by the content of the `$ref` or similar node.

For instance, if the content of `$ref` is a URI String, the parser will use the targeted URI to directly and simply ingest the exact contents of the referenced object.

[source,yaml]
----
some_property:
  $ref: "https://example.com/some-file.yml" # gets the entire contents of the file, minus any lines that start with `#` (comments).
----

If the content is an Array of URIs or URIx strings, each item in the Array will be handled essentially as above.

[source,yaml]
----
some_property:
  $refs:
    - "https://example.com/some-file.yml" # same as above
    - "https://example.com/another_file.json#/tier2" # literal JSON content
----

When the target object is a templated document in need of preparsing (rendering) before it is included, the designation format is a Map per source, minimally including a `source` key.

Typically, at least one key-value pair under the `vars` property.
If your target document is however a template that does not need variables, you must indicate so by including the `lang` property.

[source,yaml]
----
some_property:
  $ref:
    - source: "https://example.com/some_template.yaml"
      vars:
        some_variable: "some_value"
        another_variable: "another_value"
        # lang will be presumed the default (Liquid or your setting)
    - source: "https://example.com/variable-free_template.yaml"
      lang: erb # language designated to invoke parser without variables.
----

The `lang` property is unnecessary as long as you are passing `vars`, unless the template is in a format other than the default `lang` value.

==== Transcluding Literal Code

If you prefer to author descriptive texts in AsciiDoc, you can even `$ref` to a local or remote AsciiDoc file, or even a specific segment of such a file.

.Example pointer to an entire AsciiDoc file.
[source,yaml]
----
some_property:
  docs_block: |
    $ref: "./docs/rules.adoc"
----

.Example pointer to an AsciiDoc include-tagged segment
[source,yaml]
----
some_property:
  docs_block: |
    $ref: "./rules.adoc#some_text"
----

This would embed the entire contents of any include-tagged portion of the target file.

.Example include-tagged AsciiDoc file
[source,asciidoc]
----
AsciiDoc text that won't be included.

// tag::some_text[]
This is a AsciiDoc-formatted block of rich-text source.
// end::some_text[]

More text outside the included range.
----

This works with any target source file, not just AsciiDoc.
Just the way AsciiDoc's `include::[]` macro enables tagged translcusion from _any_ remote document, SGYML supports this as well.

.Example include-tagged Python file
[source,python]
----
# some Python code that won't be included
# tag::example_function[]
def example_function():
    return "This is an example function."
# end::example_function[]
----

This is certainly _not_ the way to ingest data from a separate YAML file, as you would risk indentation errors and other parsing issues.

Segments of template files will not be parsed upon inclusion via text.


=== Ingesting AsciiDoc-sourced _Data_

There are three ways to ingest _data objects_ from AsciiDoc files:

* <<attributes-dictonary,Document attributes as Dictionaries>>
* <<description-listl-dictonarh,sDescription lists converted to Dictionaries>>
* <<table-arraytable,Table data as ArrayTables>>

[[attributes-dictonary]]
===== AsciiDoc Attrbutes Ingest

The first way is to ingest AsciiDoc attributes, either all for the entire document or else a specific list of attributes.

.Example AsciiDoc attributes ingest using pointers
[source,yaml]
----
these_attributes: # arbitrary container object
  $ref: "./properties.adoc?attributes"
----

.Example ingesting specific AsciiDoc attributes only
[source,yaml]
----
these_attributes:
  $ref: "./properties.adoc?attributes=doctitle,author,revnumber"
----

With the `attributes` query parameter included, the source target will be converted using Asciidoctor for proper harvesting of attributes.

For the remaining methods of data ingest, you must indicate if you wish the source target first be rendered with AsciiDoc.
The default behavior is to collect data from the AsciiDoc syntax, which means some macros, substitutions, and other conversion processes will not have been performed.

[[description-list-dictionary]]
===== Description-List Ingest

The second way is by pointing to a description list or a specific term in a description list.

.Example description-list content storage in AsciiDoc file
[source,asciidoc]
----
[#dictonary1.dictionaries]
some_property::
  This is a rich-text, AsciiDoc-formatted block of text.

another_property:::
  This is a nested term description, which can be referenced by a pointer.
  Note the 3 colons after the term name.

third_property::
  This is another term definition or property description.

Here is some intervening text that breaks up the DL.

[#dictionary2.dictionaries]
fourth_property::
  Yet another term definition or property description.
----

.Example pointer to a specific AsciiDoc description-list
[source,yaml]
----
dictonary1:
  $ref: "./properties.adoc#/#dictionary1"
----

The second `#` symbol designates a specific ID in the AsciiDoc file.
The above would therefore resolve to:

[source,yaml]
----
dictonary1:
  dictionaries:
    some_property: This is a rich-text, AsciiDoc-formatted block of text.
      another_property: This is a nested term description, which can be referenced by a pointer.
    third_property: This is another term definition or property description.
----

You can also point to a specific, individual term object in any AsciiDoc description list.

.Example pointer to a specific (nested) term in an AsciiDoc DL
[source,yaml]
----
some_property:
  $ref: "./properties.adoc#/#dictionary1/some_id/another_property"
----

The above would resolve to:

[source,yaml]
----
some_property:
  another_property: This is a nested term description, which can be referenced by a pointer.
----

You can also collect multiple DLs that have the same role designation into a single Map in YAML.

.Example pointer to multiple DLs in an AsciiDoc file
[source,yaml]
----
dictonaries:
  $ref: "./properties.adoc#/.dictionaries"
----

The `.` identifier acts as a role selector, so the above would resolve to:

[source,yaml]
----
dictonaries:
  some_property: This is a rich-text, AsciiDoc-formatted block of text.
    another_property: This is a nested term description, which can be referenced by a pointer.
  third_property: This is another term definition or property description.
  fourth_property: Yet another term definition or property description.
----


[[table-arraytable]]
===== Table Data Ingest

The third way is to ingest a specific AsciiDoc table.

[NOTE]
This only works with standard AsciiDoc tables that have an equal number of cells per row.
It does not work with CSV- or TSV-sourced tables, but those formats can be ingested directly.

Let's say we have a table like:

.Example AsciiDoc table
[source,asciidoc]
----
[cols=",,,",id="some_table"]
|===
|term |desc |type |example
|apple |a fruit |noun |"I ate an apple."
|banana |a fruit |noun |"I ate a banana."
|cat |a mammal |noun |"I saw a cat."
|dog |a mammal |noun |"I saw a dog."
|===
----

We can point to this table for ingesting as data.

.Example AsciiDoc table ingest using pointers
[source,yaml]
----
some_table:
  $ref: "./properties.adoc#some_table"
----

The above would resolve to:

[source,yaml]
----
some_table:
  - term: apple
    desc: a fruit
    type: noun
    example: "I ate an apple."
  - term: banana
    desc: a fruit
    type: noun
    example: "I ate a banana."
  - term: cat
    desc: a mammal
    type: noun
    example: "I saw a cat."
  - term: dog
    desc: a mammal
    type: noun
    example: "I saw a dog."
----

==== CSV and TSV Data Ingest

You can also ingest CSV and TSV data directly into a YAML document, very similarly to how table-data ingest works from AsciiDoc files.

.Example CSV data ingest using pointers
[source,yaml]
----
some_table:
  $ref: "./properties.csv"
----

This will resolve to an ArrayTable using the first row's content as property keys.

Additionally, we can ingest data from multi-sheet XLS or similar files.

.Example multi-sheet XLS data ingest using pointers
[source,yaml]
----
some_table:
  $ref: "./properties.xlsx#/Sheet1"
----

=== Schema-based Concatenation

Within a SchemaGraph definition, you can indicate an ArrayList of concatenated YAML files to be loaded and validated together.

.Example schema definition for a property sourced across multiple files
[source, yaml]
----
$schema:
  type: Map
  properties:
    terms:
      $schema:
        type: ArrayTable
        properties: &term_properties
          _meta:
            req: [term]
          term:
            type: String
          desc:
            type: Block
            default: Description missing...
        dataset:
          sources:
            - "./terms/a-e.yml#terms"
            - "./terms/f-j.yml#terms"
            - "./terms/k-o.yml#terms"
            - "./terms/p-t.yml#terms"
            - "./terms/u-z.yml#terms"
        properties:
----

The above instructs a SchemaGraphy parser to concatenate all source files whenever this schema is invoked or applied.

Notably, each of the alphabetically organized files can contain its own local schema that only governs the document itself.
For instance:

[source,yaml]
.terms/a-e.yml local schema
----
$schema:
  properties:
    terms:
      $schema:
        type: ArrayTable
        properties:
          term:
            value:
              rules:
                pattern: /^[a-e]/
---
terms:
  - term: apple
    desc: a fruit
  - term: banana
    desc: a fruit
  - term: cat
    desc: a mammal
  - term: dog
    desc: a mammal
  - term: zipperumpazoo
    desc: an invalid term for this file, according to the local schema
----

Before concatenating this file, its local schema will be applied for validation and parsing.

Therefore, any governed object must be valid according to the local schema, and the concatenated document must be valid according to the schema that governs the concatenated document, if any.

This is also a good way to add common properties on a per-file basis.

[source,yaml]
.categories/mammals.yml local schema
----
$schema:
  $ref: ../_schemas/animals.yml
  properties:
    animals:
      $schema:
        type: ArrayTable
        properties:
          genus:
            value:
              default: mammal
---
animals:
  - species: cat
    sound: meow
  - species: dog
    sound: bark
  - species: capybara
    sound: chirp
----

When parsed, these ArrayTable records will have an additional parameter.

[source,yaml]
----
animals:
  - species: cat
    sound: meow
    genus: mammal
  - species: dog
    sound: bark
    genus: mammal
  - species: capybara
    sound: chirp
    genus: mammal
----