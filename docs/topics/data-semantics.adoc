= Semantic Data Structure in YAML

Most systems that use YAML for configuration or scripting try to sensibly name and nest the constituent properties.
They provide a fairly strict if extensible model for fine-tuning a configuration or routine.

This is a fairly advanced guide for actually _designing your own_ extensions and applications that can handle custom content types, case-specific procedures, and any great way to design and manage your YAML-sourced data objects.

When it comes to data, two types of semantics are important:

. *namespace:* how you name properties
. *typing:* what property types you choose
. *positional:* how you _nest_ properties
. *tagging:* how you _annotate_ properties

Tagging is a YAML-specific feature that is rarely used, but we will touch on it briefly.
The other three are essential to designing a good data structure.






== When Data Types Matter

Data types in YAML are almost always inferred from the basic key/value syntax, with little need for explicit typing.

The value `4` is an Integer, unless it is quoted, in which case it is a string.

While YAML facilitates explicit typing with tags, these are rarely necessary.

[source,yaml]
----
- 3.0
- !!str 3.0
- "3.0"
----

The first value is a float, the second and third are strings.
Obviously quotes are easier and clearer than type tags in nearly all situations.

There are some cases where *explicit tagging can signal* to someone editing a YAML document how their changes must conform to requirements.
Unfortunately, this is really only valuable for a few types of special composite types, and the YAML-native types (`!!set`, `!!seq`, `!!omap`, and `!!pairs` from YAML 1.1) are very obscure and not at all widely used.

SGYML processors recognize tags for all valid data types, but the effect is limited.
For example:

[source,yaml]
----
some_dictionary: !!ArrayTable
  - name: Alice
    age: 25
  - name: Bob
    age: 30
----

This would effectively invalidate the object if a third record was added that was not a Map, but without a schema, the constraint is of very little value.

[source,yaml]
----
some_dictionary: !!ArrayTable
  - name: Alice
    age: 25
  - name: Bob
    age: 30
  - Charlie # INVALID
  - name: Hannah # VALID
----

As you can see, what this `some_dictionary` object really needs is a schema, not a one-dimensional tag.

[source,yaml]
----
some_dictionary:
  $schema:
    type: ArrayTable
    properties:
      _meta:
        key: name
        req: [name ,age]
      name: { type: String }
      age: { type: Integer }
  $payload:
  - name: Alice
    age: 25
  - name: Bob
    age: 30
  - Charlie # INVALID
  - name: Hannah # INVALID (no age property)
----


=== Transformable Data

=== Composite Data

The two types of composite data objects in YAML are _sequences_ and _mappings_.
In SGYML parlance, we call these _Arrays_ and _Maps_.

As a refresher:

[source,yaml]
----
# Array
- item1
- item2
- item3

# Map
key1: value1
key2: value2
key3: value3
----

It is often obvious which of these to use.

The most complicated cases are what we call _tabular_ data.

If you are familiar with spreadsheets or relational databases, you are familiar with tabular data.
The _tab_ relates more to _table_, as in database table, than to most other ways we think of "`tabs`" -- unless you think of the tabs of a spreadsheet or the tabs of a real-world filing system.

Now you may be seeing why we recognize to different tabular types: _ArrayTables_ and _MapTables_.

[source,yaml]
----
# ArrayTable
- name: Alice
  age: 25
- name: Bob
  age: 30

# MapTable
Alice:
  age: 25
Bob:
  age: 30
----

Both of the above look neat, but the MapTable has the obvious advantage that it enforces unique keys.
The `name` properties of the ArrayTable are not required by YAML to be unique, so we could easily have two bobs and create a conflict.

By assigning a schema to the data document, it is simple to designate a key field for an ArrayTable (`name` in this instance) and enforce uniqueness.

[source,yaml]
----
$schema:
  type: ArrayTable
  properties:
    _meta:
      key: name
----

With this, any SGYML supportive tooling, such as LiquiDoc, will detect and warn of any duplicate key values at runtime.

[NOTE]
To my knowledge, this cannot be done with JSON Schema.

When it comes to tabular data, you may come to prefer Arrays of Maps (ArrayTables) rather than Maps of Maps (MapTables).
It is somewhat easier to process ArrayTables in most programming languages, and they are more flexible and easier to maintain.

YAML introduces deep nesting to this approach, adding complication.
This is where YAML data is more like _programming data_ than it is like _spreadsheet or database data_.

While the latter often seems more attractive:

[source,yaml]
----
products:
  Flagshipper:
    versions:
      "2.3.1":
        release:
          date: 2024-11-12
          status: ga
      "2.3.0":
        release:
          date: 2024-09-22
          status: ga
    # lots more data
  Cloudy:
    version:
      "v1":
        release:
          status: ga
      "v2":
        release:
          date: 2025-05-05
          status: beta
        notice: |
          Available for preview to select customers.
----

This seems like a great way to organize visually, and it provides the most straightforward way of referencing an deeply nested parameter:

[source,liquid]
----
{% assign cloudy_v2_status = data.products.Cloudy.v2.release.status %}
----

Data object references do not get any cleaner than that.

The problem is, this is rarely how we find ourselves needing to assign variables for _tabular_ data in templating or programming languages.
Instead, we are usually filtering or iterating through this data.

Here is another way to handle the above YAML:

[source,yaml]
----
products:
  - name: Flagshipper
    versions:
      - code: "2.3.1"
        release:
          date: 2024-11-12
          status: ga
      - code: "2.3.0"
        release:
          date: 2024-09-22
          status: ga
  - name: Cloudy
    versions:
      - code: "v2"
        release:
          date: 2025-05-05
          status: beta
        notice: |
          Available for preview to select customers.
      - code: "v1"
        release:
          status: ga

----

This is slightly more verbose, and slightly less elegant, most will agree.
But it is still highly legible.
We can create better visual distinction with how we organize Array-formatted records.

[source,yaml]
----
products:
  - 
    name: Flagshipper
  
    versions:
      - 
        code: "2.3.1"
        
        release:
          date: 2024-11-12
          status: ga
# ...
----

With this arrangement, when you need to select a deeply nested property of a given item, you can do so with a loop:

[source,liquid]
----
{% assign cloudy = data.products | find: "name", "Cloudy" %}
{% assign cloudy_v2 = cloudy.versions | find: "code", "v2" %}
{% assign cloudy_v2_status = cloudy_v2.release.status %}
----

The above looks painfully verbose compared to the elegant one-liner we had before, to reference a deeply nested property in a given MapTable.

When it comes to finding a deeply nested property in any unknown record, the MapTable approach requires iteration instead of ``find``ing.

.Select the product that had a release date of 2025-05-05 from the MapTable
[source,liquid]
----
{% for product in data.products %}
  {% for version in product %}
    {% if version.release.date == "2025-05-05" %}
      {{ product.name }}
      {{ version.code }}
      {{ version.release.status }}
    {% endif %}
  {% endfor %}
{% endfor %}
----

Oof.
That's making our three lines for the ArrayTable `find` filters look pretty good.

.Select the product that had a release date of 2025-05-05 from the ArrayTable
[source,liquid]
----
{% for product in data.products %}
  {% assign version = product.versions | find: "release.date", "2025-05-05" %}
  {{ product.name }}
  {{ product.versions.code }}
  {{ product.versions.release.status }}
{% endfor %}

Some full-fledged programming languages like Ruby and Python have ways to perform these kinds of dynamic queries in deeply nested data far more elegantly.

[source,ruby]
----
data.products.select { |product| product.versions.any? { |version| version.release.date == "2025-05-05" } }


Now let us instead look at what it's like to iterate through each table type to express the data from certain types.

.Loop through the ArrayTable, showing all and only the data for products with `ga` status
[source,liquid]
----
{% for product in data.products %}
  {% for version in product.versions %}
    {% if version.release.status == "ga" %}
      {{ product.name }}
      {{ version.code }}
      {{ version.release.date }}
    {% endif %}
  {% endfor %}
{% endfor %}
----

.Loop through the MapTable, showing all and only the data for products with `ga` status
[source,liquid]
----
{% for product in data.products %}
  {% assign product_name = product[0] %}
  {% assign product_data = product[1] %}
  {% for version in product_data.versions %}
    {% if version.release.status == "ga" %}
      {{ product_name }}
      {{ version.code }}
      {{ version.release.date }}
    {% endif %}
  {% endfor %}
{% endfor %}
----

Here the more verbose and complicated syntax is required by the MapTable.

LiquiDoc and SGYML enable the best of both worlds.
For any object that is schemed as an ArrayTable or MapTable, conversion is as easy as a Liquid filter called `retab`.

Use `{% assign products_map = data.products | retab: 'name' %}` to convert a MapTable to an ArrayTable with the key converted to a `name` property for each record.

Or use `{% assign products_array = data.products %}` to convert an ArrayTable to a MapTable.
Since we schematized our ArrayTable with a key field, the `name` property will be used as the key for each record, by default.
(The `name` property also remains in place but is redundant after transformation.)

Now you may author YAML documents the way you see fit and convert your tables inline, as needed.

[.case]_Without SGYML_, you might wish to design your tabular data with the complication of iterating through nested records 



== Strategies for Designing Semantic Data Structures