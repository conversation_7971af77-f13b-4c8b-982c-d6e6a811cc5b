{%- assign show_headers = vars.show_headers | default: true %}
{%- assign header_level = vars.header_level | default: 2 %}
{%- assign show_kinds   = vars.show_kinds | default: "scalar,composite" %}
{%- assign show_kinds   = show_kinds | split: "," %}
{%- assign types = data.types %}
{%- capture header_syntax %}
=
{%-   for lvl in 1..4 %}
{%-     if lvl == header_level %}
{%-       continue %}
{%-     else -%}=
{%-     endif -%}
{%-   endfor -%}
{%- endcapture %}
{%- assign header_syntax = header_syntax | strip %}

{%- for kind in show_kinds %}
{%-   if show_headers %}
{{ header_syntax }}{{ kind | capitalize }} Data Types
{%-   endif %}
{%-   assign classes = data.types | where: 'kind', kind %}
{%-   if format == 'listing' %}
{%-     for class in classes %}
* {{ class.type }}
{%-       if class.variants %}
{%-         for variant in class.variants %}
** {{ variant.type }}
{%-           if variant.variants %}
{%-             for sst in variant.variants %}
*** {{ sst.type }}
{%-        endfor %}
{%-           endif %}
{%-         endfor %}
{%-       endif %}
{%-     endfor %}
{%-   elsif format == 'annotated' %}
{%-     for class in classes %}
{{ header_syntax }}= {{ class.type }} Type Class
{{ class.desc }}
{{ header_syntax }}== {{ class.type }} variants
{%        for variant in class.variants %}
{{ variant.type }}::
+
--
{{ variant.desc }}
--

{%-         if variant.variants %}
{%-           for sst in variant.variants %}
{{ sst.type }}:::
+
--
{{ sst.desc }}
--

{%-             if sst.variants %}
{%-               for ssst in sst.variants %}
{{ ssst.type }} ({{ sst.type }}):::
+
--
{{ ssst.desc }}
--
{%-               endfor %}
{%-             endif %}
{%-           endif %}
{%-         endfor %}
{%-       endif %}
{%-     endfor %}
{%-   endif %}
{%- endfor %}