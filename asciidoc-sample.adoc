= Sample AsciiDoc Document
Author Name
v1.0, 2024-04-04
:toc:

== Introduction

A brief introduction paragraph. AsciiDoc is a plain text human readable/writable document format that can be converted to many other formats.

=== Goals

* Understand AsciiDoc basics
* Explore various block types
* Generate documentation

== Section Level 1

=== Section Level 2

==== Section Level 3

===== Section Level 4

====== Section Level 5

Paragraph text under section level 5. AsciiDoc supports up to five levels of nesting for sections.

== Paragraphs

A single paragraph is simply one or more lines of text.

[sidebar]
.Cool Fact
****
Sidebars are blocks that provide supplementary information.
****

== Lists

=== Unordered List

* Item 1
* Item 2
** Nested Item 2.1
** Nested Item 2.2
* Item 3

=== Ordered List

. First item
. Second item
.. Nested item 1
.. Nested item 2
. Third item

=== Description List

CPU:: The brain of the computer.
Hard drive:: Permanent storage for operating system and/or user files.
RAM:: Random Access Memory.

== Blocks

=== Quote Block

[quote, <PERSON>]
____
Imagination is more important than knowledge. For knowledge is limited, whereas imagination embraces the entire world, stimulating progress, giving birth to evolution.
____

=== Code Block

[source,ruby,roles="first-role,second-role"]
----
puts 'Hello, world!'
----

=== Admonition Block

[.note-role]
NOTE: Be sure to save your work frequently.

WARNING: Do not expose the device to water or extreme temperatures.

== Tables

[options="header"]
|===
| Name | Age | City
| John | 25 | New York
| Jane | 29 | San Francisco
| Doe  | 32 | Chicago
|===

== Images

image::http://example.com/images/logo.png[Logo,100,100]

== Links

http://asciidoctor.org[Asciidoctor]

https://github.com[GitHub]
