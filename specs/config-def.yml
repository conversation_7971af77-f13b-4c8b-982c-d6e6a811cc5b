properties:
  _meta:
    desc: |
      The metadata settings for the configuration file.
    properties:
      markup:
        type: String
        desc: |
          The markup format used in strings in this configuration file.
          May be `asciidoc` or `markdown`.

          This probably matters less than you might imagine, as ReleaseHx will use AsciiDoc-style `+++_italic_+++` and `+++*bold*+++` syntax, and there should not be much call for divergent syntax like for links or images.
          All default values are cross-compatible.
        dflt: 'markdown'
      slug_type:
        type: String
        desc: |
          The format of slugs used in your application, for use with `sluggerize` Liquid filter.

          Must be 'kebab' or 'snake'.
        dflt: 'kebab'
      tplt_lang:
        type: String
        desc: |
          The default format used in fields of `Template` type.
          Must be 'liquid' or 'erb'.
        dflt: 'liquid'
  api: # the user's actual API
    desc: |
      The API or file source for the issues.
    properties:
      from:
        type: String
        desc: |
          The type of API or file to use for the issues source.
          May be `jira`, `github`, `gitlab`, `rhyml`, or `git`.
        cmmt: Issue source API type (jira, github, gitlab, rhyml, git)
        dflt: json
        docs: |
          The `jira`, `github`, and `gitlab` types are all REST APIs that return an individualized JSON payload upon request, to be converted to RHYML format.

          Alternately, you may use a file directly written in <<rhyml,RHYML-style YAML>> (`rhyml`) or use the <<raw-git>> approach (`git`).

          If you wish to mix Git and API sources, this field should still reference the API.
          Use `git` only if you will solely derive content from Git commits.
      href:
        type: URL
        desc: |
          The base URL for the API or JSON file.
          Only required if for remote APIs (not RHYML).
      auth:
        desc: |
          Properties related to API authentication.

          This block should be unnecessary if you use a supported API (JIRA, GitHub, GitLab), unless you wish to use differently named environment variables for API credentials.
        properties:
          mode:
            desc: |
              The type of authentication to use.

              Options are: `basic`, `token`, `bearer`, `header`, `query`, `none`.
          user_env:
            type: String
            desc: |
              Name of the environment variable containing the API username.
            dflt: RELEASEHX_API_USER
          key_env:
            type: String
            desc: |
              Name of the environment variable containing the API key or token.
            dflt: RELEASEHX_API_KEY
          org_env:
            type: String
            desc: |
              Name of the environment variable containing the organization credential.
            dflt: RELEASEHX_API_ORG
          header:
            type: String
            desc: |
              The header to use for authentication.
              Only used if `api.auth.mode` is `header`.
    
  sources:
    desc: |
      Details about content origination, as well as markup sources and conversion.
    properties:
      summ:
        type: String
        desc: |
          The source of the summary (Changelog) content.
          Must be `issue_heading`, `custom_field`, or `commit_message`.

          If `issue_heading`, the summary or title field will be used.
          If `commit_message`, the first line of the Git commit will be used.
        dflt: issue
      head:
        type: String
        desc: |
          The source of release-note headlines, when it is not the same as the summary.
          
          Unless a `head` is available in the RHYML source, the `summ` will be used.
          By default, ReleaseHx does not generate a `head` property for work items.

          Potential values: `issue_heading`, `release_note_heading`, or `commit_message_heading`.
        docs: |
          When set to *`issue_heading`*, the RHYML `head` property will derive from the issue title or summary.
          
          When set to *`release_note_heading`* or *`commit_message_heading`*, the `head` property will derive from the first line of the release note or commit message, respectively, so long as it matches the Regular Expression set in `pppty.release_note_heading_pattern`.
          When set to `commit_message_heading`, the RHYML `head` property will derive from the first line of the commit message.
      note:
        type: String
        desc: |
          The source of the release notes content.
          Must be `issue_body`, `custom_field`, or `commit_message`.
        
          Defaults to `issue_body` for GitHub and GitLab, but to `custom_field` for JIRA.
      note_custom_field:
        type: String
        desc: |
          The name of the custom field to use for the release notes content.
          Only used if `sources.note` is `custom_field`.

          This purposely has no default, as you will probably have to look up the actual field ID, which will be something like `customfield_10010`.
      note_pattern:
        type: RegExp
        desc: |
          The Regular Expressions pattern to match in the body of an issue or commit message, after which all content is considered the release `note` matter.

          Defaults to a Markdown or AsciiDoc header or HTML comment with the case-insensitive string `release note` in it.

          Uses Capture group `note` in the Regular Expression to establsh the entire note content.

          See the `sources.head_pattern` property for details on extracting a heading (`head` in RHYML) from the `note` content.
        dflt: /^((#|=)+ (Draft )?Release Note.*)|(\<!-- (draft )?release note --\>)\n+(?<note>\w(.|\n)+)/gmi
      head_pattern:
        type: RegExp
        desc: |
          The Regular Expressions pattern to match in the `note` text to be used to establish a heading for the note (`head`).
          This text is removed from the `note` value during a draft operaton, if the pattern matches.

          Defaults to a Markdown or AsciiDoc header or HTML comment with the case-insensitive string `release note` in it.

          The `head` capture group is snipped from text matching this pattern.
        dflt: /^(?<head>[A-Z].*[^.!])\n\n[A-Z].*/gm
      markup:
        type: String
        desc: |
          The origin markup format for notes.
          May be `markdown` or `asciidoc`.
        dflt: markdown
      engine:
        type: String
        desc: |
          The markup converter to use for the issues.
          Defaults to `asciidoctor` for AsciiDoc and `redcarpet` for Markdown.
          Options include `asciidoctor`, `redcarpet`, `commonmarker`, `kramdown`, or `pandoc`.

  extensions:
    desc: Default file extensions.
    properties:
      markdown:
        desc: File extension for Markdown drafts.
        type: String
        dflt: 'md'
      asciidoc:
        desc: File extension for AsciiDoc drafts.
        type: String
        dflt: 'adoc'
      yaml:
        desc: File extension for YAML drafts.
        type: String
        dflt: 'yml'
  
  types:
    type: Map
    desc: |
      Issue types to include in the release history, in the order of display.

      List as many as you wish to match up with corresponding metadata at the source.
    properties:
      feature:
        desc: |
          A new capability, functionality, or interface element.
        properties:
          slug: &type_slug_ppty
            type: String
            desc: |
              The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.
            dflt: feature
          text: &type_text_ppty
            type: String
            desc: |
              The display label for the type in the release history output.
              Defaults to the capitalized key name.
            dflt: New feature
          head: &type_head_ppty
            type: String
            desc: |
              The header for the type in the release history output.
              Defaults in templates to the `text` property pluralized.
            dflt: New features
          icon: &type_icon_ppty
            type: String
            desc: |
              The icon to use for issues of this type.
            dflt: plus-square-o
      bug:
        desc: |
          A fix for a previously reported issue.
        properties:
          slug:
            <<: *type_slug_ppty
            dflt: bug
          text:
            <<: *type_text_ppty
            dflt: Bug fix
          head:
            <<: *type_head_ppty
            dflt: Bug fixes
          icon:
            <<: *type_icon_ppty
            dflt: bug
      improvement:
        desc: |
          An enhancement to an existing capability, functionality, or interface element.
        properties:
          slug:
            <<: *type_slug_ppty
            dflt: improvement
          text:
            <<: *type_text_ppty
            dflt: Improvement
          head:
            <<: *type_head_ppty
            dflt: Improvements
          icon:
            <<: *type_icon_ppty
            dflt: wrench
      documentation:
        desc: |
          An update to the documentation.
        properties:
          slug:
            <<: *type_slug_ppty
            dflt: documentation
          text:
            <<: *type_text_ppty
            dflt: Documentation
          head:
            <<: *type_head_ppty
            dflt: Docs Changes
          icon:
            <<: *type_icon_ppty
            dflt: book
      <type_name>:
        desc: |
          The corresponding issue type.

          The key should be a simple string for referencing the slug in RHYML and ReleaseHx templates.
          This is what will be entered in a change's `type` property in RHYML.
        properties:
          slug:
            <<: *type_slug_ppty
            dflt: null
          text: &type_text_ppty
            type: String
            desc: |
              The display label for the type in the release history output.
              Defaults to the capitalized key name.
            dflt: null
          head:
            <<: *type_head_ppty
            dflt: null
          icon:
            <<: *type_icon_ppty
            dflt: null

  parts:
    type: Map
    desc: |
      The map of product components to include in the release history, in the order of display.

      List as many as you wish to match up with corresponding metadata at the source.
    properties:
      <part_name>:
        desc: |
          The corresponding product component.

          The key should be a simple string for referencing the slug in RHYML and ReleaseHx templates.
          This is what will be entered in a change's `part` property in RHYML.
        type: Map
        properties:
          slug:
            type: String
            desc: |
              The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.

              [NOTE]
              This technically does not have to be a "`Slug`" String, if the system permits spaces.
              It just needs to exactly match whatever String the remote API returns to represent the label/tag.
          text:
            type: String
            desc: |
              The display text for the component in the release history output.
              Defaults to the capitalized key name.
          head:
            type: String
            desc: |
              The header for the component in the release history output.
              Defaults in templates to the `text` property pluralized.
          icon:
            type: String
            desc: |
              The icon to use for issues that affect this component.

  tags:
    type: Array
    desc: |
      Handling for tags, labels, or toggles associated with source Issues.

      Subordinate property keys (other than `include` and `exclude`) represent individual tag names, with the default set documented here.
      The property `<your_tag_name>` represents arbitrarily named tags, any number of which you are welcome to add.

      This block serves to filter out any unrelated labels/tags ingested from APIs during the conversion from payload to RHYML draft.
      Only tags with their own property here will be ported from the issue source to the RHYML change record's `tags` Array.
    properties:
      _include:
        type: Array
        desc: |
          The tags, labels, or toggles that trigger inclusion in the release history.
        dflt: [highlight, deprecation, removal, breaking, experimental, changelog]
      _exclude:
        type: Array
        desc: |
          The list of tags that cause a change/issue to be excluded from the release history.
        dflt: []
      highlight:
        desc:
          The tag, label, or toggle that indicates an issue is to be highlighted or prioritized in sorts.
        properties:
          head: &tag_head_ppty
            desc: How this tag will display as a grouping title.
            type: String
            dflt: Highlights
          text: &tag_text_ppty
            desc: How this tag will display as a label.
            type: String
            dflt: highlight
          slug: &tag_slug_ppty
            type: String
            desc: |
               The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.

               [NOTE]
               This technically does not have to be a "`Slug`" String, if the system permits spaces.
               It just needs to exactly match whatever String the remote API returns to represent the label/tag.
            dflt: highlighted
          icon: &tag_icon_ppty
            type: String
            desc: |
              The icon to use for issues so-tagged.
            dflt: star
          groupable: &tag_groupable_ppty
            type: Boolean
            desc: |
              Whether this tag can be used to group issues in the release history.
            dflt: true
      deprecation:
        desc:
          The tag, label, or toggle that indicates an issue includes a feature discontinuation announcement.
        properties:
          head: 
            <<: *tag_head_ppty
            dflt: Deprecations
          text:
            <<: *tag_text_ppty
            dflt: Deprecated
          slug:
            <<: *tag_slug_ppty
            dflt: deprecation
          icon:
            <<: *tag_icon_ppty
            dflt: exclamation-triangle
          groupable: *tag_groupable_ppty
      breaking:
        desc:
          The tag, label, or toggle that indicates a potentially disruptive change.
        properties:
          head:
            <<: *tag_head_ppty
            dflt: Breaking Changes
          text:
            <<: *tag_text_ppty
            dflt: Breaking
          slug:
            <<: *tag_slug_ppty
            dflt: breaking
          icon:
            <<: *tag_icon_ppty
            dflt: exclamation-triangle
          groupable: *tag_groupable_ppty
      removal:
        desc:
          The tag, label, or toggle that indicates a change includes a feature removal.
        properties:
          head:
            <<: *tag_head_ppty
            dflt: Feature Removals
          text:
            <<: *tag_text_ppty
            dflt: Removed
          slug:
            <<: *tag_slug_ppty
            dflt: removal
          icon:
            <<: *tag_icon_ppty
            dflt: sign-out
          groupable:
            <<: *tag_groupable_ppty
      security:
        desc: |
          The tag, label, or toggle that indicates a change includes a security-related alteration.
        properties:
          head:
            <<: *tag_head_ppty
            dflt: Security Fixes
          text:
            <<: *tag_text_ppty
            dflt: Security
          slug:
            <<: *tag_slug_ppty
            dflt: security
          icon:
            <<: *tag_icon_ppty
            dflt: shield
          groupable: *tag_groupable_ppty
      experimental:
        desc:
          The tag, label, or toggle that indicates a feature that is not yet stable or may not be permanent.
        properties:
          head:
            <<: *tag_head_ppty
            dflt: Experimental Features
          text:
            <<: *tag_text_ppty
            dflt: Experimental
          slug:
            <<: *tag_slug_ppty
            dflt: experimental
          icon:
            <<: *tag_icon_ppty
            dflt: flask
          groupable: *tag_groupable_ppty
      internal:
        desc:
          The tag, label, or toggle that indicates an issue documents a feature that is not intended for "`public`" use or visibility.
        properties:
          head:
            <<: *tag_head_ppty
            dflt: Internal Changes
          text:
            <<: *tag_text_ppty
            dflt: Internal
          slug:
            <<: *tag_slug_ppty
            dflt: internal
          icon:
            <<: *tag_icon_ppty
            dflt: lock
          groupable: *tag_groupable_ppty
      changelog:
        desc: |
          The tag, label, or toggle that indicates an issue should appear in the Changelog even if it does not have a Release Note.

          There is no icon associated with changelog-tagged issues, as it is only declaring that an issue belongs in the Changelog.
        properties:
          slug:
            <<: *tag_slug_ppty
            dflt: changelog
          groupable:
            <<: *tag_groupable_ppty
            dflt: false
      release_note_needed:
        desc:
          The tag, label, or toggle that indicates an issue requires a release note to be written.
          This is used by the `rhx` CLI to determine which issues to fetch for release notes.
          
          This tag can be automatically removed, and it does not show up in published Changelog or Release Notes (unless you add it).
        properties:
          slug:
            <<: *tag_slug_ppty
            dflt: release_note_needed
          groupable:
            <<: *tag_groupable_ppty
            dflt: false
      <your_tag_name>:
        desc:
          Unlimited custom tags of your choosing, associated with a tag, label, or toggle in the Issues system _or_ manually assignable in RHYML.
        docs: |
          These property represents any arbitrarily named tags you might wish to add.
          No `slug` sub-property is available because the name of this property should exactly match your tag/label.
        properties:
          head:
            <<: *tag_head_ppty
            dflt: null
          text:
            <<: *tag_text_ppty
            dflt: null
          icon:
            <<: *tag_icon_ppty
            dflt: null
          groupable:
            <<: *tag_groupable_ppty
            dflt: null
  
  links:
    desc: |
      Base paths for building links to online references like issues (web) and commits (git).
    properties:
      web:
        type: Liquid
        desc: |
          The URL template for the web links in the release history listings.
          May include `{{ ticketid }}` as placeholders.
      git:
        type: Liquid
        desc: |
          The URL template for the git links in the release history listings.
          May include `{{ githash }}` as placeholders.
      usr:
        type: Liquid
        desc: |
          The URL template for the contributor homepages in the release history listings.
          May include `{{ username }}` as placeholders.
  
  paths:
    desc: |
      The configuration for the paths to include in the release history listings.
    properties:
      templates_dir:
        type: Path
        desc: |
          The path to the templates directory.
        dflt: _templates
      drafts_dir:
        type: Path
        desc: |
          The path to the output directory for generated drafts (YAML, Markdown, AsciiDoc).
        dflt: _output
      publish_dir:
        type: Path
        desc: |
          The path to the output directory for rendered files (HTML, PDF).
        dflt: _publish
      custom_mappings_dir:
        type: String
        desc: |
          The path to the directory containing user-defined API mappings.

          ReleaseHx checks here first for a file named `<api_from_name>.yml`, where `<api_from_name>` is set in the `api.from` property.
          If no file is found, the mapping is expected to be supplied by the gem (see `<GEM_ROOT>/mappings/`).
        dflt: _mappings
      draft_filename:
        type: FileName
        desc: |
          The filename template for the draft files.
        docs: |
          May include `{{ version }}` and `{{ format_ext }}` as placeholders, where `format_ext` is determined at file-write time and based on preferences defined in the `config.extensions` block.
        dflt: '{{ version }}.{{ format_ext }}'
        templating:
          delay: true
          default: liquid
      publish_filename:
        type: FileName
        desc: |
          The filename template for the published files.
        docs: |
          May include `{{ version }}` and `{{ format_ext }}` as placeholders, where `format_ext` is `.html` or `.pdf` and does not get rendered until the outfile is written.

          Published file extensions must be: `.html`, `.pdf`.
        dflt: 'release-history-{{ version }}.{{ format_ext }}'
        templating:
          delay: true
          default: liquid
  
  # templates:
  #   desc: |
  #     Patterns for dynamically forming strings.

  #     By default, these use the template engine defined in `_meta.tplt_lang`, which defaults to `liquid`.

  #     You may override the stated or implied `_meta.tplt_lang` setting by prepending the relevant node value with `!erb` or `!liquid`, as needed.
    # properties:

  modes:
    desc: |
      Default settings for `rhx` command executions.
    properties:
      wrapped:
        type: Boolean
        desc: |
          Include (or exclude) head, header, and footer elements when rendering to HTML.
        dflt: false
      html_frontmatter:
        type: Boolean
        desc: |
          Include frontmatter in the rendered HTML.

          See the `templates.page_frontmatter` property for details.
        dflt: true
      markdown_frontmatter:
        type: Boolean
        desc: |
          Include frontmatter in Markdown drafts.

          Uses the `templates.markdown_frontmatter` template.
        dflt: false
      asciidoc_frontmatter:
        type: Boolean
        desc: |
          Include frontmatter in AsciiDoc drafts.

          Uses the `templates.asciidoc_frontmatter` template.
      fetch:
        type: String
        desc: |
          What to fetch when gathering issues from API.

          Valid entries:

          * `all-tagged` -- fetches issues with `release_note_needed` tag.
        dflt: notes-only
      remove_excess_lines:
        type: Integer
        desc: |
          Reduces _N_+ consecutive blank lines to _N_ lines.
        dflt: 1
  
  rhyml:
    desc: |
      Settings related to RHYML data objects and documents.
    properties:
      # authoring properties
      markup:
        type: String
        desc: |
          The markup format for the `note` or `memo` properties of RHYML objects.

          Change to `asciidoc` to convert upstream Markdown to AsciiDoc.

          This setting can be set (and overridden) with the `_config.markup` property in any given RHYML document.
        dflt: markdown
      # drafting properties
      chid:
        type: Slug
        desc: |
          The template for automatic change ID/slug construction, if available at draft-time.
          (These settings may be argued in the `rhx` CLI using `--date DATE` and `--hash SHA256`.)
        docs: |
          A liquid template with access to local variables including:

          Release variables::
          
          * `release.code`
          * `release.date`
          * `release.hash`

          Work/change item variables::

          * `change.tick`
          * `change.hash`
          * `change.type`
          * `change.part`
          * `change.summ`

          The template established here is only for drafting `chid` slugs.
          It is not used down the line to validate `chid` entries, which will be valid as long as they are Slug-formatted strings.

        dflt: |
          {{- change.tick }}-{{ change.summ | truncate: 20 | slugify }}
        templating:
          delay: true
          default: liquid
      empty_notes:
        type: String
        desc: |
          What to do for issues that lack a release note but have the `release_note_needed` tag.

          * `skip` the issue when drafting notes (can update with `--amend`)
          * `empty` include the issue with an empty note
          * `dump` the complete issue body/description and commit message as the `note` property
          * `ai` generate a note using generative AI
        dflt: skip
      max_parts:
        type: Integer
        desc: |
          The maximum number of affected _part_ categories that can be recorded for a single change.

          When `0`, _part_ records are disables for all changes.
          When `1`, only one _part_ is allowed per change (String).
          When `2` or more, a single affiliated _part_ category may be recorded using the `part` property, but more than one must be recorded using the `parts` property (Array).
        dflt: 1
      pasterize_summ:
        type: Boolean
        desc: |
          Whether to convert verbs in the `summ` property to past tense when drafting.
          Replaces common words like `adds` with `added`, `fix` with `fixed`, `builds` with `built`, etc.
        dflt: false
      pasterize_head:
        type: Boolean
        desc: |
          Whether to convert verbs in the `head` property to past tense when drafting.
          Replaces common words like `adds` with `added`, `fix` with `fixed`, `builds` with `built`, etc.
        dflt: false
      git_version_tag:
        type: Liquid
        desc: |
          For "`raw Git`" setups, this is the Git-tag label format for the version.
          Uses the variable `version` as the _argued version_.
        dflt: 'v{{ version }}'
        docs: |
          All commits from (1) the previous instance of a tag matching the `templates.git_version_tag_pattern` expression to (2) the instance of the tag matching the _argued version_, all (3) from the `templates.git_branch_name`, will be collected as potential entries for Changelog/Release Notes.

          Take the following example: 
          _this property_ is set to the default template and the sibling `git_version_tag_pattern` property is set to its default.

          Running `rhx 1.1.2 --yaml` will collect _all_ commits prior to the _argued_ version tag (`v1.1.2`) and the previous Git tag that matches the pattern.

          Tags like `v1.1.1-alpha` will not operate as a boundary unless they are incorporated into the pattern.
      git_version_tag_pattern:
        type: RegExp
        desc: |
          The Regular Expressions pattern to count as a previous version in the Git history, for "`raw Git`" setups.
        dflt: /^v\d+\.\d+\.\d+$/
        docs: |
          The 
      git_branch_name:
        type: Liquid
        desc: |
          For "`raw Git`" setups, this is the Git-branch name format for the determining version.
          All commits on this branch will be considered potential entries for Changelog/Release Notes.

          When using Git tags to determine version, this property should be set to a branch like `main` or `trunk` `release`.
        dflt: 'v{{ version }}'

  history:
    desc: |
      Configurations for the overall document, when applicable.
    properties:
      head:
        type: String
        desc: |
          The header for the release history output.
        dflt: Release History -- {{ release.code }} - {{ release.date }}
        templating:
          delay: true
          default: liquid
      htag:
        type: String
        desc: |
          The heading level (H1, H2, etc) for the release history header.
        dflt: h1
      markdown_frontmatter:
        type: Liquid
        desc: |
          Designates the content inserted at the top of Markdown files as document-level metadata.
        docs: |
          A Liquid template to be prepended at the top of Markdown draft files.

          Templates may contain the following variables, automatically generated, as applicable:

          * `date` (DateTime)
          * `version` (String)
          * `title` (String)          
        dflt: &markdown_frontmatter_tplt |
          ---
          title: Release History for {{ release.code }}
          version: {{ release.code }}
          date: {{ release.date }}
          ---
      asciidoc_frontmatter:
        type: Liquid
        desc: |
          Designates the way front-matter is inserted at the top of AsciiDoc files.
          Several variables are available to templates.

          AsciiDoc frontmatter templates may also contain AsciiDoc attribute placeholders.
        dflt: |
          :page-title: Release History for {{ release.code }}
          :page-version: {{ release.code }}
          :page-date: {{ release.date }}
      html_frontmatter:
        type: Liquid
        desc: |
          Designates the way front-matter is inserted at the top of _unwrapped_ rendered HTML.
        docs: |
          The `frontmatter` property is a Liquid template that is inserted at the top of the rendered HTML file.

          It may include `{{ title }}`, `{{ version }}`, `{{ date }}`, as well as any `vars`-scoped variables as you pass in.
        dflt: *markdown_frontmatter_tplt
      items:
        desc: |
          Settings pertaining to displayed items across Changelog and Release Notes sections.

          Most of these settings can be defined separately for each section under <<conf_ppty_changelog>> and <<conf_ppty_notes>>.
        properties:
          allow_redundant: &allow_redundant_ppty
            type: Boolean
            desc: |
              Whether to allow duplicate entries in a given section, for instance across groups for `part:group` sorts where a change affects multiple parts.
            dflt: false
          issue_links: &web_links_ppty
            type: Boolean
            desc: |
              Whether to include web links in item metadata.

              Requires `links.web` to be defined.
            dflt: false
          git_links: &git_links_ppty
            type: Boolean
            desc: |
              Whether to include git links in item metadata.

              Requires `links.git` to be defined.
            dflt: false
          metadata_labels: &metadata_labels_ppty
            type: String
            desc: |
              If and where to display icons in relation to labels in item metadata.

              Use `before` or `after` to choose a spot, `none` or `'$nil'` to disable.
            dflt: before
          metadata_icons: &metadata_icons_ppty
            type: Boolean
            desc: |
              Whether to include icons for metadata in item metadata.
            dflt: before

  changelog:
    desc: |
      The configuration for the changelog output.
    properties:
      head:
        type: String
        desc: |
          The header for the changelog output.
        dflt: Changelog
      text:
        type: String
        desc: |
          The text for the changelog output.
          Change to `null` to hide.
        dflt: |
          Summaries of all user-facing changes made since the previous release.
      htag:
        type: String
        desc: |
          The heading level (H1, H2, etc) for the changelog section header.
        dflt: h2
      spot:
        type: Integer
        desc: |
          Where in the document to place the changelog (`1` = top, `2` = bottom).
        dflt: 2
      sort:
        type: Array
        desc: |
          The sort order for the changelog output.
        docs: &sort_docs_property |
          Indicate whether you wish to _group_ output by this sort criterion, or else just order by that criteria, with or without a label denoting the criterion.

          If `<criterion>:group` (default), the output will be grouped into sections with the group instance 
          If `<criterion>:grouping1`, the criterion will be added to the first of 2 grouping tiers into which the output will be divided.
          If `<criterion>:grouping2`, the criterion will be added to the second of 2 grouping tiers into which the output will be divided.
          If `<criterion>:label`, the output will be order by the criterion, with a label indicating the criterion.
          If `<criterion>:none`, the output will be ordered by the criterion, with no label.

          For example:

          [source,yaml]
          ----
          sort:
            - 'part:group'
            - 'type:label'
          ----

          Would produce something like:

          [source,markdown]
          ----
          ## Web UI

          - Feature description or summary [New feature]

          - Another feature description or summary [New feature]

          - Improvement description or summary [Improvement]
          
          ## Backend
          
          - Feature description or summary [New feature]

          - Bug fix description or summary [Bug Fixes]
          ----

          For 2-tiered grouping arrangements, use somethng like:

          [source,yaml]
          ----
          sort:
            - 'part:grouping1'
            - 'type:grouping2'
          ----

          This would output for instance:

          [source,markdown]
          ----
          ## Web UI

          ### New features
          - Feature description or summary
          - Another feature description or summary

          ### Improvements
          - Improvement description or summary
          ----

          You may also use tag-based groupings, but the tags must be listed explicitly.

          [source,yaml]
          ----
          sort:
            - 'breaking:group'
            - 'deprecation:group'
            - 'type:group'
            - 'part:label'
          ----

          This would output something akin to:

          [source,markdown]
          ----
          ## Breaking Changes
          - We are breaking this thing! [Web UI]

          ## Deprecations
          - We are deprecating this thing! [Backend]

          ## New features
          - Feature description or summary [Web UI]
          - Another feature description or summary [Web UI]

          ## Improvements
          - Improvement description or summary [Backend]
          ----

        dflt: ['part:grouping1']
      items: # was items
        desc: |
          Settings that affect the frame/shape and arrangement of individual changelog entries.
        properties:
          frame:
            type: String
            desc: |
              The layout for the changelog entry display.

              Can be `ordered`, `unordered`, or `paragraph`.
            dflt: table-cols-1
          allow_redundant: *allow_redundant_ppty
          git_links: *git_links_ppty
          issue_links: *web_links_ppty
          metadata_labels: *metadata_labels_ppty
          metadata_icons: *metadata_icons_ppty

  notes:
    desc: |
      The configuration for the Release Notes listing section.
    properties:
      head:
        type: String
        desc: |
          The header for the notes output.
        dflt: Release Notes
      text:
        type: String
        desc: |
          The text for the release notes output.
          Change to `null` to hide.
        dflt: |
          Descriptions of any specially notable changes or additions since the previous release.
      htag:
        type: String
        desc: |
          The heading level (H1, H2, etc) for the release notes section header.
        dflt: h2
      spot:
        type: Integer
        desc: |
          Where in the document to place the Release Notes relative to the Changelog.
        dflt: 1
      sort:
        type: Array
        desc: |
          The sort *order* for the release notes output.
        docs: *sort_docs_property
        dflt: ['highlight:grouping1', 'deprecation:grouping1', 'removal:grouping1', 'breaking:grouping1', 'type:grouping1', 'part:grouping2']
      items:
        desc: |
          Settings that affect the frame/shape and arrangement of individual release-note item displays.
        properties:
          frame:
            type: String
            desc: |
              The layout for the release-note item display.

              Can be `table-cols-1`, `table-cols-2`, `desc-list`, or `admonition`.
            dflt: table-cols-1
          allow_redundant: *allow_redundant_ppty
          git_links: *git_links_ppty
          issue_links: *web_links_ppty
          metadata_labels: *metadata_labels_ppty
          metadata_icons: *metadata_icons_ppty
