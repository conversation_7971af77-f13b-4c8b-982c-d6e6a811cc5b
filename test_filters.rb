#!/usr/bin/env ruby
# Test script to verify Liquid filters are working

require_relative 'lib/releasehx/rhyml/adapter'
require 'liquid'

puts "Testing Liquid filter registration..."

# Initialize the adapter to trigger filter registration
mapping = { 'changes_array_path' => 'issues' }
config = {}
adapter = ReleaseHx::RHYML::Adapter.new(mapping: mapping, config: config)

# Test the filters by creating a simple template
template_content = <<~LIQUID
  Test Sourcerer filters:
  - sluggerize: {{ "Hello World!" | sluggerize }}
  - plusify: {{ "line1\n\n\nline2" | plusify }}
  - demarkupify: {{ "*bold* and `code`" | demarkupify }}
  
  Test RhymlFilters:
  - pasterize: {{ "fix the bug" | pasterize }}
LIQUID

begin
  # Call the private method to initialize filters
  adapter.send(:initialize_liquid_filters)
  
  # Parse and render the template
  template = Liquid::Template.parse(template_content)
  result = template.render({})
  
  puts "SUCCESS: Filters are working!"
  puts result
rescue => e
  puts "ERROR: #{e.class}: #{e.message}"
  puts e.backtrace.first(5).join("\n")
end
