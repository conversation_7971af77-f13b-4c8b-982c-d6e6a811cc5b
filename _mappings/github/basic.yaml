_config:
  path_lang: jmespath
  tplt_lang: liquid
  desc: GitHub Issues API to RHYML (checkbox-based tags)
  note: Extracts tags from checkbox lists in issue body

changes_array_path: issues

tick:
  path: number

type:
  path: labels[?name=='enhancement'].name | [0]
  tplt: '{% if path %}improvement{% else %}{{ labels[0].name | default: "task" }}{% endif %}'

parts:
  path: labels[?contains(name, 'ui') || contains(name, 'api') || contains(name, 'security') || contains(name, 'performance') || contains(name, 'accessibility')].name

hash:
  path: id
  tplt: '{{ path | append: "000" | slice: 0, 7 }}'

summ:
  path: title

head:
  path: title

note:
  path: body

tags:
  path: body
  tplt: |
    {%- assign checkbox_tags = "" | split: "," -%}
    {%- if path contains "- [x] highlighted" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "highlighted" -%}
    {%- endif -%}
    {%- if path contains "- [x] changelog" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "changelog" -%}
    {%- endif -%}
    {%- if path contains "- [x] breaking" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "breaking" -%}
    {%- endif -%}
    {%- if path contains "- [x] deprecation" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "deprecation" -%}
    {%- endif -%}
    {%- if path contains "- [x] security" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "security" -%}
    {%- endif -%}
    {%- if path contains "- [x] internal" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "internal" -%}
    {%- endif -%}
    {{ checkbox_tags | join: "," | split: "," }}

lead:
  path: assignee.login
