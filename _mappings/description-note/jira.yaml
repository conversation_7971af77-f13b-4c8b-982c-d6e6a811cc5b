_config:
  path_lang: jmespath
  tplt_lang: liquid
  desc: JIRA API to RHYML (customfield-based notes)
  note: Requires config.sources.note_custom_field

changes_array_path: issues

tick:
  path: key

type:
  path: fields.issueType.name
  tplt: '{{ path | downcase }}'

hash:
  path: fields.development.commits[0].sha

part:
  path: fields.components[0].name

summ:
  path: fields.summary

note:
  path: "fields.description"

part:
  path: fields.components[0].name

tags:
  path: fields.labels

lead:
  path: fields.assignee.displayName