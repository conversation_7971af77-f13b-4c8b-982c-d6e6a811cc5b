_config:
  path_lang: jmespath
  tplt_lang: liquid
  desc: GitLab Issues API to RHYML (checkbox-based tags)
  note: Extracts tags from checkbox lists in issue description

changes_array_path: issues

tick:
  path: iid

type:
  path: labels[?contains(@, 'enhancement')] | [0]
  tplt: '{% if path %}improvement{% else %}{{ labels[0] | default: "task" }}{% endif %}'

parts:
  path: labels[?contains(@, 'collaboration') || contains(@, 'search') || contains(@, 'database') || contains(@, 'ai') || contains(@, 'configuration')]

hash:
  path: merge_commit_sha

summ:
  path: title

head:
  path: title

note:
  path: description

tags:
  path: description
  tplt: |
    {%- assign checkbox_tags = "" | split: "," -%}
    {%- if path contains "- [x] highlighted" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "highlighted" -%}
    {%- endif -%}
    {%- if path contains "- [x] changelog" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "changelog" -%}
    {%- endif -%}
    {%- if path contains "- [x] breaking" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "breaking" -%}
    {%- endif -%}
    {%- if path contains "- [x] deprecation" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "deprecation" -%}
    {%- endif -%}
    {%- if path contains "- [x] removal" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "removal" -%}
    {%- endif -%}
    {%- if path contains "- [x] security" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "security" -%}
    {%- endif -%}
    {%- if path contains "- [x] experimental" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "experimental" -%}
    {%- endif -%}
    {%- if path contains "- [x] internal" -%}
      {%- assign checkbox_tags = checkbox_tags | push: "internal" -%}
    {%- endif -%}
    {{ checkbox_tags | join: "," | split: "," }}

lead:
  path: assignee.username
