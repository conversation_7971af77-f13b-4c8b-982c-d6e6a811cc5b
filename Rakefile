require 'rake'
require 'yaml'
require_relative 'lib/sourcerer'

BUILDER_NAME = 'releasehx-builder'
VERSION_LINE_REGEX = /^:this_prod_vrsn:\s+(.*)$/

task :prebuild do
  srcrr_config = YAML.safe_load_file('sourcerer-config.yml', symbolize_names: true, aliases: true)

  Sourcerer::Builder.generate_prebuild(**srcrr_config)
  Sourcerer.render_templates(srcrr_config[:templates])
  Sourcerer.generate_manpage('docs/manpage.adoc', 'build/docs/releasehx.1')
end

desc 'Build and tag multi-arch Docker image for releasehx'
task :buildx do
  ensure_buildx_builder
  version = extract_version

  sh "docker buildx build --platform linux/amd64,linux/arm64 " \
     "--build-arg RELEASEHX_VERSION=#{version} " \
     "-t docopslab/releasehx:latest " \
     "-t docopslab/releasehx:#{version} " \
     "."
end

desc 'Build the gem (run `prebuild` first)'
task :prebundle => :prebuild do
  sh 'gem build releasehx.gemspec'
end

def extract_version
  File.readlines('README.adoc').each do |line|
    return line.match(VERSION_LINE_REGEX)[1].strip if line.match?(VERSION_LINE_REGEX)
  end
  raise 'Version not found in README.adoc'
end

def ensure_buildx_builder
  builders = `docker buildx ls`
  return if builders.include?(BUILDER_NAME)

  puts "Creating buildx builder '#{BUILDER_NAME}'..."
  sh "docker buildx create --name #{BUILDER_NAME} --driver docker-container --use"
  sh "docker buildx inspect --builder #{BUILDER_NAME} --bootstrap"
end
