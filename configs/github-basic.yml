api:
  from: github
  version: 3
  label: GitHub Issues API v3
  format: json

paths:
  drafts_dir: drafts
  payloads_dir: issues
  templates_dir: _templates
  custom_mappings_dir: _mappings

sources:
  summ: issue_heading
  note: issue_body
  note_pattern: '/^## Release Note\s*\n+(?<note>(.|\n)+)/mi'
  markup: markdown
  engine: redcarpet

parts:
  ui:
    head: User Interface
    icon: desktop
    text: UI
  api:
    head: API
    icon: server
    text: API
  security:
    head: Security
    icon: shield
    text: Security
  performance:
    head: Performance
    icon: tachometer
    text: Performance
  accessibility:
    head: Accessibility
    icon: universal-access
    text: Accessibility

links:
  web: https://github.com/example/project/issues/{{ tick }}
  git: https://github.com/example/project/commit/{{ hash }}
  usr: https://github.com/{{ vars.lead }}

modes:
  asciidoc_frontmatter: true
  markdown_frontmatter: true
  remove_excess_lines: 2

rhyml:
  pasterize_head: true
  chid: "{{ release.code | replace: '.', '_' | upcase }}-{{ change.tick }}"
  empty_notes: skip

history:
  items:
    issue_links: true
    git_links: false  # GitHub Issues API doesn't provide commit hashes
    metadata_icons: true

changelog:
  head: What's Changed
  text: |
    Summary of all changes in this release.
  items:
    frame: unordered
    git_links: false  # No commit hashes available
    metadata_labels: after

notes:
  spot: 1
  items:
    frame: desc-list
    allow_redundant: true

tags:
  _include: ['highlighted', 'changelog', 'breaking', 'deprecation', 'security']
  _exclude: ['internal', 'wontfix', 'duplicate']

  highlighted:
    head: Highlights
    icon: star
    text: Featured changes

  breaking:
    head: Breaking Changes
    icon: exclamation-triangle
    text: Breaking changes

  deprecation:
    head: Deprecations
    icon: clock-o
    text: Deprecated features

  security:
    head: Security Fixes
    icon: shield
    text: Security improvements

  enhancement:
    head: Enhancements
    icon: arrow-up
    text: Improvements

  bug:
    head: Bug Fixes
    icon: bug
    text: Bug fixes

  documentation:
    head: Documentation
    icon: book
    text: Documentation updates
