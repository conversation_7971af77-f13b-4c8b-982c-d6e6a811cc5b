api:
  from: jira
  version: 2
  label: JIRA v2 API
  format: json

paths:
  drafts_dir: drafts
  payloads_dir: issues
  templates_dir: _templates
  custom_mappings_dir: _mappings

sources:
  summ: issue_heading
  note: custom_field
  note_custom_field: customfield_10010
  markup: markdown
  engine: redcarpet

parts:
  webui:
    head: Web UI
    icon: desktop
    text: Web UI
  api:
    head: API
    icon: server
    text: API
  installer:
    head: Installer
    icon: cloud-download
    text: Installer
  internal:
    head: Internal
    icon: lock
    text: Internal

links:
  web: https://jira.example.com/browse/{{ tick }}
  git: https://github.com/DocOps/releasehx-demo/commit/{{ hash }}
  usr: https://example.com/staff/profiles/{{ vars.lead }}

modes:
  asciidoc_frontmatter: true

rhyml:
  pasterize_head: true
  chid: "{{ release.code | replace: '.', '_' | upcase }}-{{ change.tick }}"

history:
  items: 
    empty_groups: |
      No changes in this {{ group_type }} category.

# Different sorting arrangement - type first, then part
changelog:
  head: Changelog
  text: |
    Summaries of all user-facing changes made since the previous release.
  sort:
    - 'type:grouping1'
    - 'part:grouping2'
  items:
    frame: paragraph

notes:
  head: Release Notes
  text: |
    Descriptions of any specially notable changes or additions since the previous release.
  spot: 2
  sort:
    - 'breaking:grouping1'
    - 'deprecation:grouping1'
    - 'highlighted:grouping1'
    - 'type:grouping2'
  items:
    frame: table-cols-2
    allow_redundant: true

tags:
  _include: ['release_note_needed', 'changelog', 'highlighted', 'breaking', 'deprecation']
  _exclude: ['internal', 'wontfix']
  
  highlighted:
    head: Highlighted
    icon: star
    text: Notable features and improvements
    
  breaking:
    head: Breaking Changes
    icon: exclamation-triangle
    text: Changes that may require action
    
  deprecation:
    head: Deprecations
    icon: clock-o
    text: Features marked for future removal
    
  bug:
    head: Bug Fixes
    icon: bug
    text: Issues resolved in this release
    
  improvement:
    head: Improvements
    icon: arrow-up
    text: Enhancements and optimizations
    
  task:
    head: Tasks
    icon: check
    text: Maintenance and internal changes
