= SchemaGraphy, SGYML, and Graphy
// tag::globals[]
:schemagraphy_repo_www: https://github.com/DocOps/schemagraphy
:schemagraphy_www: https://aylstack.docopslab.org/schemagraphy
:schemagraphy_docs_www: {schemagraphy_www}/docs
:schemagraphy_data_schemas_spec_www: {schemagraphy_www}/specifications/data-schemas
:schemagraphy_text_schemas_spec_www: {schemagraphy_www}/specifications/text-schemas
:schemagraphy_sgyml_spec: {schemagraphy_www}/specifications/sgyml
:schemagraphy_sgyml_data_types_www: {schemagraphy_www}/specifications/sgyml/data-types
:schemagraphy_urix_spec_www: {schemagraphy_www}/specifications/urix
:sgyml_spec_www: {schemagraphy_www}/specifications/sgyml
:urix_spec_www: {schemagraphy_www}/specifications/urix
:this_repo_www: {schemagraphy_repo_www}
:this_docs_www: {schemagraphy_docs_www}
:xref_data-types: {schemagraphy_docs_www}/data-types
:dash-dash-dash: ---
:docksh_vrsn: 0.1.0
:proj_vrsn_this: 0.1.0
:proj_date_this: 2024-03-01
:proj_vrsn_next: 0.2.0
:proj_slug_this: schemagraphy
:schemagraphy_docs_www_version: {schemagraphy_docs_www}/{this_version_slug}
// end::globals[]
:document: website
ifdef::env-github[]
:document: README
:this_version_slug: latest
endif::[]
:proj_docs_www_vrsn: {schemagraphy_docs_www_version}

SchemaGraphy is a universal plaintext schema-definition language and utility for YAML-formatted documents that govern structured content (AsciiDoc, restructuredText) and semi-structured data (YAML, JSON, XML, CSV, native data objects) across programming languages and runtimes (Java, Ruby, JavaScript, Python, Rust, etc).

SGYML is a slight extension of standard YAML, including a re-stated data-typing system and some dynamic features such as `$ref` object-transclusion properties.

== In This Repository

The SchemaGraphy project hosts four (4) specification documents as well as the Ruby tooling that implements them:

* SchemaGraphy YAML-based Markup Language (*SGYML*) link:{sgyml_spec_www}[Specification] (draft)
* SchemaGraphy *Data Schemas* link:{schemagraphy_data_schemas_spec_www}[Specification] (draft)
* SchemaGraphy *Text Schemas* Specification (not-yet drafted) (link:{schemagraphy_repo_www}/tree/text-schema[development branch])
* *URIx* String (URI fragment) link:{urix_spec_www}[Specification] (draft)

Also contained here, the Graphy API and graphy CLI that can be used to apply these schemas to data and content files, as well as perform other operations.

// tag::features[]
* Add [.buzz]*transclusion* (`$ref`) capability to YAML documents
* Govern [.buzz]*serialized, nested data* objects, including default properties and _inter-object relationships_.
* Constrain [.buzz]*AsciiDoc*, [.buzz]*Markdown*, [.buzz]*restructuredText*, and any other lightweight markup _text formatting_ to meet your custom standards.
* _Extend_ basic YAML with cross-references.
* Generate [.buzz]*linter definitions* based on your schemas.
* Automatically [.buzz]*generate documentation* for APIs, config files, and marked-up datasets.
* Even [.buzz.nokey]*validate HTML and rich-test* output of your source markup!
* Port to and from third-party schema models like [.buzz]*JSON Schema** and [.buzz]*GraphQL*.
* [.buzz.nokey]*Render HTML forms* for data collection with one click.
* Ingest [.buzz.nokey]*default values* from a config file definition.
* [.buzz.nokey]*Generate stubs/drafts* data and documents.
* All with [.buzz.nokey]*human-friendly YAML* files and objects, with AsciiDoc supported internally!
// end::features[]

=== Subject Clarification

This repository is a "`monorepo`" for a few different projects, including their specifications and parser utilities.

*SchemaGraphy* is the YAML-formatted markup syntax for the SchemaGraph framework.
The framework starts with a pair of schema-definition models with largely overlapping/parallel syntaxes -- one for handling _data_ markup, the other for _text_ content formatted with lightweight markup.

The simple YAML schemas are fun to write, and the graphy CLI utility makes them easy to test and apply.


*SGYML* is the YAML extension format that adds simple features and modified naming conventions to the YAML 1.2 specification.

*URIx* is a means of referencing data objects nested inside other objects or documents.

All of these components use the SGYML data-typing system, as defined in `gem/lib/_schemas/data-types.yml` and documented at link:{proj_docs_www_vrsn}/data-types.

This codebase contains the capacity to build Ruby parser libraries/utilities for these various subjects, along with generating their technical specification documents, sourced here as AsciiDoc/YAML files.

Use the `graphy` CLI to exploit all of the related functionality, or use the various APIs in the schemagraphy gem in your own Ruby tools.

== Usage

The *SchemaGraph system* can be applied to data or text, mostly when sourced in flat files with standardized lightweight markup, but it has at least some hope of handling arbitrary or loosely "`flavored`" formats like Markdown.
Most commonly, we are talking about data files in JSON, YAML, XML, and CSV, as well as content formatted with AsciiDoc or a well-maintained Markdown library.

Define your *data objects* with SchemaGraphs and use them to enforce adherence as new data is added to an Array or other enumerable, or when changes are made to any standalone Map or other complex data object.

Or, define your *textual content* with SchemaGraphs and use them to govern new contributions to a document or docset.

You can even generate stub/outline data objects or structured texts, using defaults and requirements defined in a schema.


The *SchemaGraphy YAML-based Markup Language (SGYML)* can be used with or without SchemaGraphy data schemas.

One of SGYML's dependencies is the *URIx* format, which establishes a powerful means of referencing or transcluding nested data objects or schemas.

=== The SchemaGraphy Schema Concept

SchemaGraphs are data objects (typically sourced in YAML format for convenience) in a particular structure that Graphy can use to apply to other data objects, generate dummy objects, or generate forms for entering new instances of a class of objects.

While data files can nest SchemaGraphs for one or more object in a `$schema` property, they can also be stored in a separate file and either referenced in the `$schema` property or else applied at runtime when the data to be minded is loaded.

Content schemas are always in an external file, sometimes referenced in a comment at the top of the governed document.

A nested SchemaGraph can be as simple as:

[source,yaml,subs="attributes"]
----
$schema:
  type: Map
  properties:
    _meta: # metadata about properties
      req: [signers,forms,departments] # required properties
    signers: # first actual property
      type: ArrayTable
      properties:
        _meta:
          key: id
          idx: [id] # index property
          req: [name,email,dept]
          acc: [role,active] # additional accepted property
        id: # first nested property
          type: Slug
        email:
          type: Email
          rules:
            regexp: /^[\w\-]{2,20}@ourco.com$/
        name: # object class/type
          type: String
          rules:
            regexp: /[A-Z][a-z\-\s]+/
        role:
          type: String
          options: [standard,special]
          default: standard
          context:
            has_one: '#roles'
        dept:
          type: Slug
          context:
            has_one: '#departments'
        active:
          type: Boolean
          default: true
    roles:
      type: ArrayTable
      properties:
        _meta:
          key: role
          req: [name]
        name:
          type: String
        context:
          has_many: '#signers'
  departments:
    type: MapTable
    properties:
      _meta:
        idx: slug
        req: [building]
        acc: [manager]
      building:
        type: Integer
        desc: The building number where the department is located.
      manager:
        type: Slug
        desc: The ID of the department manager.
        rules:
          nullable: true
        context: 
          has_one: signer

    forms:
      type: Table
      properties:
        _meta:
          req: [name,date]
        name:
          type: String
        date:
          type: Date

# The above code governs the below object
{dash-dash-dash}
signers:
  - id: bjones
    name: Barb Jones
    email: <EMAIL>
    dept: art
  - id: ggrady
    name: Greg Grady
    email: <EMAIL>
    dept: art
  - id: jsmith
    name: John Smith
    email: <EMAIL>
    role: special
    dept: accounting
    active: false

roles:
  - slug: standard
    name: Standard Signer
  - slug: special
    name: Special Signer

forms:
  Form1:
    date: 2020-01-01
  Form2:
    date: 2020-01-02

departments:
  art:
    building: 53
    manager: ggrady
  accounting:
    building: 22
----

Applying the above schema to its associated payload with `join` capability activated would generate this ArrayTable object for the `signers` object:

[source,yaml]
----
signers:
  - id: bjones
    name: Barb Jones
    email: <EMAIL>
    role:
      slug: standard
      name: Standard Signer
    dept:
      art:
        building: 53
        manager:
          id: ggrady
          name: Greg Grady
          email: <EMAIL>
          role: standard
          dept: null # recursion prevented
    active: true
  - id: ggrady
    name: Greg Grady
    email: <EMAIL>
    role:
      slug: standard
      name: Standard Signer
    dept:
      art:
        building: 53
        manager:
          id: ggrady
          name: Greg Grady
          email: <EMAIL>
          role: standard
          dept: null # recursion prevented
    active: true
  - id: jsmith
    name: John Smith
    email: <EMAIL>
    role: special
    dept:
      accounting:
        building: 22
        manager: null
    active: false
----

This operation is triggered in a runtime preparser, meaning you pass a text-based data source through the schema validator and parser at the point of ingestion.
You either call on SchemaGraphy to do this in your own Ruby script, or you use the Graphy CLI to apply the schema to a data file so you can ingest a YAML or JSON version in the next step.

[NOTE]
Schemas can also be stored in external files and applied at the time of parsing.

Because the parsing step inferred default values and joined data, the resulting object is fully formed with nested paths for easy reference.

[source,liquid]
----
{% assign active_signers = signers | where: 'active', true %}
{% for signer in active_signers %}
  {% assign dept_slug = signer.dept[0] %}
  <div class="signer">
    <h3>{{ signer.name }} </h3>
    <p>{{ signer.role.name }}, {{ dept | capitalize }} Department</p>
    <dl>
      <dt>Email</dt>
      <dd>{{ signer.email }}</dd>
      <dt>Building</dt>
      <dd>{{ signer.dept[dept_slug]['building'] }}</dd>
      <dt>Manager</dt>
      <dd>
        {% if signer.dept[dept_slug]['manager'] %}
          {% if signer.dept[dept_slug]['manager']['id'] == signer.id %}
            Self
          {% else %}
            {{ signer.dept[dept_slug]['manager']['name'] }} ({{ signer.dept[dept_slug]['manager']['email'] }})
          {% endif %}
        {% else %}
          None
        {% endif %}
      </dd>
    </dl>
  </div>
{% endfor %}
----

The above template would render the following HTML:

[source,html]
----
<div class="signer">
  <h3>Barb Jones</h3>
  <p>Standard Signer, Art Department</p>
  <dl>
    <dt>Email</dt>
    <dd><EMAIL></dd>
    <dt>Building</dt>
    <dd>53</dd>
    <dt>Manager</dt>
    <dd>Greg Grady (<EMAIL>)</dd>
  </dl>
</div>
<div class="signer">
  <h3>Greg Grady</h3>
  <p>Standard Signer, Art Department</p>
  <dl>
    <dt>Email</dt>
    <dd><EMAIL></dd>
    <dt>Building</dt>
    <dd>53</dd>
    <dt>Manager</dt>
    <dd>Self</dd>
  </dl>
</div>
----

.Why Not Just Use JSON Schema?
ifdef::env-github[]
|===
a|
endif::[]
ifndef::env-github[]
****
endif::[]
There are numerous differnces between _SchemaGraph definitions_ and _JSON Schema definitions_.

. SchemaGraphy is designed for defining data objects that describe _user intrfaces_ for software applications as well as complex vocabularies for single-sourcing documentation and specification.

. SchemaGraphs can be written in YAML (in fact SGYML) and still be applied to JSON documents.

. Only Schemagraphy schemas instruct transforming governed data objects into *new data objects*.

. Only SchemaGraphs empower *joining* objects in 1-to-1, 1-to-many, or many-to-many relationshps (see <<joins-vs-refs>>).

. Only in SchemaGraphy can schemas permit multiple cross-object references _per property_.

. SchemaGraphy enables more complex handling of multi-type fields.

. SchemaGraphy designates properties for documentation/specification, functional code, and user-interface semantics, helping your schemas (and their subject matter) be self-documenting and even functional.
ifdef::env-github[]
|===
endif::[]
ifndef::env-github[]
****
endif::[]

==== Additional Uses

SchemaGraphs can be used to auto-generate forms or commandline interfaces, validate entries, and feed linters.

==== Data Pre-processing

Graphy will _render_, _resolve_, _parse_, and _validate_ YAML documents and to some extent JSON documents and their contained data objects.
Before the content is ingested as data, Graphy will:

. render any content formatted with templating syntax/tags
. dereference any `$ref` pointers and the like

At this point, the source document can be loaded into a native data object.
This includes standard YAML processing, such as alias resolution, tag parsing, and so forth.

Next, Graphy will:

. validate the data object against the schema
. insert any implied (default) properties and values
. optionally embed any data from joined objects

Graphy does use platform-specific code to evaluate data objects, though for Strings this is largely pattern-matching procedures.
To port or customize SGYML data types, you need a language-specific instance of `schemas/native/data-type-rules.yml`.

==== Governed Object Syntax

The Graphy utility does _not_ examine or enforce the _syntax_ of data objects as stored as strings in YAML, JSON, or XML documents.
For instance, it will not check if your JSON arrays start with a `[` character followed by a newline (`\n` symbol), or such.

The validation aspect simply applies to the data represented by such documents.
Graphy only examines the structure and substance of data, as ingested native objects.

A _linter_ is recommended for ensuring the syntax of your source files.
SchemaGraphy definitions can include patterns that can be exported to configure a linter.

=== Data Schemas

SchemaGraphy data schemas are used to define semi-structured data objects, including those containing nested objects, either from native data objects or generically formatted data from JSON, YAML, XML, etc.

[NOTE]
For actually governing JSON or XML, their respective native formats and associated schema tools are not bettered by any aspect of SchemaGraphy.
The only advantage of SchemaGraphy is that its definition format (YAML) is more accessible, and it is "`good enough" for fairly simple objects.

Naturally, Graphy converts any ingested data to native format, such as Ruby objects.
But SchemaGraphy introduces a universal range of data types and formats that will allow you to define and control data with SchemaGraphy no matter where you use it, so long as the language has a Graphy port.

Data schemas enable _governance_ and _inference_.
They constrain how a given data object and its properties can be formed, and they establish default values and joins between objects so non-explicit data can be inserted where it belongs.

==== Data Governance

Data schemas are mainly used to define two broad types of objects: Maps and Tables.

A map is any set of key-value pairs in a row.
These are called Mappings in YAML and Objects in JSON, but other languages refer to them as Dictionaries, Structs, or Hashes.

If the value of a given property in a Map is another Map, it is to be defined by a subschema.

Tables are serialized datasets that are expected to have common properties across all "`records`", which you may also think of as "`rows`".

There are two types of Tables in SchemaGraphy parlance: ArrayTables and MapTables.
An ArrayTable is structured as an Array of Maps, while a MapTable is structured as a Map of Maps.

For all such Tables, the dataset as a whole as well as the expected and accepted properties each record must or may contain can all be defined in detail.

[NOTE]
If a schema defines the `type` of a governed object to be a `Table`, rather than a `MapTable` or an `ArrayTable`, then the type of the governed object is up to the user.

==== Data Inference

There are two ways in which SchemaGraphy _infers_ data into schema-governed objects.

The first is by inserting default values where any property is missing.
This inference is an _automatic_ part of the parsing process.

Inference of joined data is an _optional_ aspect of parsing, enabled by default.
It essentially resolves any joins after other forms of parsing are complete, inserting data from other objects where it is implied by the schema and referenced in a given property.

[[joins-vs-refs]]
.Schema Joins vs. Object References
****
SchemaGraphy/SGYML offer two ways to link data from different objects during the multi-step parsing process.
You can impose a "`join`" relationship in the governing schema, or you can use a `$ref` directive in the governed data.

Taking the latter first, a `$ref` or `$refs` property appearing anywhere in a data document instructs the processor to insert the referenced data object in place (from an external document or elsewhere in the current document).
This operation is performed early in the process, _before_ the subject data document is ingested and converted to native data objects.

Whereas instructing a join in the schema conveys a relationship between any instance of governed data and any so-designated data object that matches the join details.
Such a join occurs later in the process, after all data documents have been resolved and ingested as data.

A schema-instructed join is helpful if you want to provide users of your schema-defined format with a shorthand for making standard references between objects you've set out of them to use.

On the other hand, `$ref`/`$refs` directives are for use in either schema-governed _or_ ungoverned SGYML data documents for arbitrary transclusion of data objects.
****

=== Content Schemas

SchemaGraphs are not just for governing data objects.

When minding lightweight content markup, SchemaGraphs are used to define the structure of a governed text document (or a portion thereof), including the order and size of sections, the types of content allowed in each section, and the syntax and namespaces allowed across entire document sets.

Not only can you constrain how the source for given text is formatted, you can also validate the output in HTML source and even at the point of rich-text rendering (checking patterns as if rendered in a browser).
This way you can always be sure to get sourcing and results up to any formatting standard you choose.

=== Many Ways to Assign SchemaGraphy Schemas

Schemas can be stored 

. in the same file as the data or content they govern
. in a separate file referenced from the subject file
. in a separate file applied as an argument at runtime

==== Embedded Schemas

If you want to store your schemas with your content, you have a few options for data objects and a couple of options for text documents.

===== Data Objects

In either YAML or JSON, there are two options for expressing a schema alongside the data it governs.
A third option is available for YAML only.

====== 2-Up Array

One is to use a two-item Array, with either of the two items being the schema, starting with the `$schema` key, and the other assumed to be the governed object.
In YAML, the `---` delimeter (on its own line) can be used to separate the two objects as array items.

[.testable#listing-embedded-schema-array-yaml]
[source,yaml]
.Payload and $schema as Array Nodes in a single YAML document
----
# Schema definition above the `---` separator
$schema:
  # SchemaGraphy-formatted model of the governed data Map
---
# The governed data object as a Map, MapTable, or ArrayTable
# Starting immediately beneath the `---` separator
----

[.testable#listing-embedded-schema-array-json]
[source,json]
.Payload and $schema as Array Nodes in a single JSON document
----
[
  {
    "$schema": {
      // SchemaGraphy-formatted model of the governed data Map
    }
  },
  {
    // The governed data object as a Map, MapTable, or ArrayTable
  }
]
----

====== Explicit $payload Property

An object made up of a Map with two first-tier keys can be used, with `$schema` being one key and `$payload` being another.

[source,yaml]
.$payload and $schema in a single YAML document
----
$schema:
  # SchemaGraphy-formatted model of the governed data Map
$payload:
  # The governed data object as a Map, Mapable, or ArrayTable
----

[source,json]
.$payload and $schema in a single JSON document
----
{
  "$schema": {
    // SchemaGraphy-formatted model of the governed data Map
  },
  "$payload": {
    // The governed data object as a Map, MapTable, or ArrayTable
  }
}
----

====== Commented-out $schema

If you're using YAML, you can use a commented-out schema, as a way of maintaining your payload object as the root object of the file.
Just use `#` at the beginning of each line of a properly formatted schema in YAML syntax.

[source,yaml]
.Commented-out $schema
----
# $schema:
#   # SchemaGraphy-formatted model of the governed data Map
first_param_of_some_governed_Map:
second_param_of_some_governed_Map:
----

If you permit commented-out schemas, the parser must check the top of the string for `# $schema`.

Graphy caches schemas, which can be applied to the governed object after it is parsed by a templating engine itself.

If a file-embedded schema partial contains Liquid templating syntax for dynamic parsing, any variables will need to be passed at runtime.

===== Text Documents

AsciiDoc documents can accommodate a schema in commented-out YAML at the top of the file, as long as the schema is indicated on the first line of the file.

[source,asciidoc]
.Commented-out $schema
----
// $schema:
//   <SchemaGraphy-formatted model of the governed document>
:nextup: Frontmatter or AsciiDoc document attributes
// Other comments
= Title of the Document
:whatnow: The rest of the AsciiDoc document
----

Far more commonly, text subjects are governed by schemas in external files, which can thus govern more than one instance of schematized content.

==== Remote Schemas

Remote schemas are defined in YAML files that are referenced from the subject file or else imposed at runtime.

To reference a schema file, use one of the above methods for embedding the schema, but add a `$ref` key with a hash containing a `path` to the local file or a URL.
Add a `vars` key with a hash of variables to be passed to the schema at runtime.

.Example AsciiDoc file with reference to a remote schema
[source,asciidoc]
----
// $schema:
//   $ref:
//     path: path/to/schema.yml
//     vars:
//       var1: value1
//       var2: value2
= Title of the Document
:whatnow: The rest of the AsciiDoc document
----

.Example YAML file with reference to a remote schema
[source,yaml]
----
$schema:
  $ref:
    path: path/to/schema.yml
    vars:
      var1: value1
      var2: value2
# The governed data object as a Map, Mapable, or ArrayTable
----

Alternatively, keep all your data and content files as they are, and *apply remote schemas at runtime*.
This can be done using the CLI, with `graphy <subject-file> --schema <schema-path>`.
(See <<cli-invoke>> for more on the graphy CLI.)

More programmatically, use the Graphy gem API in your own Ruby script or gem, as demonstrated in <<ruby>>.

=== Data Schema Processing Order

Since SchemaGraphs are to be parsed by multiple interpreters, the order in which this occurs is critical.

[IMPORTANT]
The parsing process for YAML files governed by SchemaGraphy is _not_ the same as the parsing process for the SchemaGraphs themselves.
See <<governed-object-parsing>> to see how your actual _data_ will be parsed if you run it through tooling that supports SchemaGraphy, even if you do not apply a schema to your data.

The order of processing Schemas is as follows:

. import any external schema files denoted by `$schema.$include` properties
. parse any 3rd-party templating tags (such as Liquid) in the schema definition file
. resolve the file by following any `$ref` properties and populating them

Any subject data documents are processed as follows.

. render the subject file document for templating tags, which must yield a valid SGYML document
. dereference the subject file by following any `$ref` properties and populating them
. execute the schema instructions for validating or further parsing the subject document/object
. load the resulting strict YAML as data

Any implementation of SchemaGraphy must support these various formats, but it need not perform the template-parsing of subject documents.
A third-party application can be invoked, and then the cached schema applied to whatever object results from that parsing.

=== Mapping Objects Across Files

The `$ref`/`$refs` properties can be used to extend data objects across schemas.
Whether the main object is schema-governed or not, the `$ref` property can be used to extend the object with properties from another schema, as long as it is ingested by a SchemaGraphy parser.

[source,yaml]
.Mapped object using example
----
topics:
  $refs:
    - topics.a-m.yml
    - topics.n-z.yml
categories:
  $ref: categories.yml
volumes:
  $ref: volumes.yml
----

Assigning an Array of schema paths to the `$ref` property allows you to spread the object across multiple files, to be concatenated when parsed.

Nested objects can also be indicated using the colon separator and dot-delimited notation.

[source,yaml]
.Mapped object with nested objects
----
topics:
  $refs: 
    - ./content.a-m.yml#topics
    - ./content.n-z.yml#topics
categories:
  $ref: ./metadata.yml#categories
volumes:
  $ref: ./metadata.yml#volumes
----

==== Requiring/Prepending Files (or Objects) Inline

Use the `$require` keyword to transclude data from a data URI, using the same properties as `$ref`.

[source,yaml]
.Requiring files with root-level data object `$require` property
----
$require:
  path: ./path/to/other/file.yml
  vars:
    var1: value1
    var2: value2
----

If the value of `$require` is an Array, the files will be concatenated in the order they are listed.

[source,yaml]
.Requiring multiple files with root-level data object `$require` property
----
$require:
  - path: ./path/to/other/file1.yml
    vars:
      var1: value1
      var2: value2
  - path: ./path/to/other/file2.yml
    vars:
      var3: value3
      var4: value4
----

==== Appending Files (or Objects) Inline

Use the `$append` key to concatenate the contents an additional object from a URI, using the same properties as `$ref` and `$require`.

[source,yaml]
.Linking files with root-level data object `$append` property
----
$append:
  path: ./path/to/other/file.yml
  vars:
    var1: value1
    var2: value2
----

==== Embedding Objects Inline

Use the commented-out `# $ref:` key in any YAML file to embed a referenced object inline at any point in the document.

[source,yaml]
.Linking files with commented-out YAML
----
first_ppty: 1
# Embed a Map or Array object from another file
# $ref:
#   path: ./path/to/other/file.yml
second_ppty: 2
# Another embed without worrying the $ref key is duplicated
# $ref:
#   path: ./path/to/other/file2.yml
third_ppty: 3
----

This method also works to insert Arrays into Arrays.

[source,yaml]
.Embedding an Array within an Array using commented-out YAML
----
- first item
# Embed 7-item array from another file
# $ref:
#   path: ./7-dirty-words.json#words
- ninth item
----



=== Data and Text Documents Subject to Schemas

SchemaGraphy is mainly concerned with governing data objects or parsed/rendered content documents, but it can also be used to govern the source files themselves.

While SchemaGraphy does not lint these documents for style, it can be used to inform a linter, ensuring not just proper structure and content, but also proper syntax.

In terms of AsciiDoc files, for instance, SchemaGraphy does not ensure that your bulleted lists are written with an asterisk (`*`) instead of a hyphen (`-`), but it can constrain where bulleted lists are used in relation to a heading, for instance.

When it comes to YAML, SchemaGraphy will permit an ArrayList to be written as a bracketed (`[list,items,here]`) or with hyphenated items stacked vertically, as in.

[source,yaml]
----
- list
- items
- here
----

However, it can constrain the length of such a list, or what kind of contents are permitted as items.

// === Foldable Paths and Editable Backreferences

// This _optional_ parser enables one or both of the following:

// * The option to organize nested YAML mappings in the format of your choice.
// * Ue multi-tier variable substitutions that back-reference objects already stablished in the document.
// * The ability to modify values according using common operators.

// ==== Foldable Paths

// The concept of "`foldable paths`" refers to permitting nested Map objects (mappings in YAML parlance, objects in JSON) to keyed and referenced using more flexible paths.

// Any property defined in a SchemaGraph can be designated a foldable property path, which means it can be keyed as either a conventional nested object in JSON or YAML representation, or else as a flattened "`path`" denoted with hyphens or underscores.

// [source,yaml]
// .Two nested objects presented differently
// ----
// - first:
//     second:
//       third:
//         fourth: value
// - first-second-third-fourth: value
// - first_second_third_fourth: value
// ----

// Assuming the properties established for items in this ArrayTable are designated as _foldable_, the above would parse to:

// [source,yaml]
// .Parsed ArrayTable with foldable properties
// ----
// - first:
//     second:
//       third:
//         fourth: value
// - first:
//     second:
//       third:
//         fourth: value
// - first:
//     second:
//       third:
//         fourth: value
// ----

// If a given property's `map` meta property (`_meta/map` is set to `flat`), it must be keyed as a flat path, and the parser will not accept nesting.

// [source,yaml]
// .Foldable property with flat  
// ----
// - first_second_third_fourth: value
// ----

// ==== Backreferencing Foldable Paths

// When foldable paths are enabled, they can be referenced in a novel manner for variable substitution.
// The variable key is named to match the path of the object to be referenced, with the path delimited by underscores.

// [source,yaml]
// .Backreferencing foldable paths
// ----
// this:
//   that:
//     other:
//       thing:

// new_path: $this_that_other_thing
// ----

// ==== Manipulating a Backreference Variable

// Variablized backreferences can be modified using common operators, such as `+` (add), `-` (subtract), `*` (multiply), and `/` (divide).
// They also handle simple functions like `round()` (nearest integer), `floor()` (round down), and `ceil()` (round up).

==== Governed Document/Data Processing Sequence

When a SchemaGraph is applied to a data object, the object must first be _resolved_ and _loaded_.
Only then does the governing schema get applied for validation and further parsing.

[IMPORTANT]
If your YAML files have third-party templating markup mixed into them, this must be parsed and a valid YAML document produced.
SchemaGraphy only accepts valid YAML, even if some of it will be pre-processed as a document, prior to loading as data.

SchemaGraphy-supporting parsers must be able to _dereference_ specially formattted pointers and read externally sourced YAML or JSON (or XML or CSV, etc) data objects into placeholders denoted with `$ref` property keys.

SchemaGragraphy accepts valid YAML or JSON documents, on which it performs the following:

. dereference pointers such as `$ref` and `$append`, forming a valid YAML document with no pointers.

. parse and resolve any dynamic YAML syntax, such as anchors and aliases.

. load the resulting YAML document as a native data object.

. validate the data object against the schema

. render a data object with any schema-designated defaults filled in

. (optionally) write the rendered data object to a new YAML or JSON file


[IMPORTANT]
It is key to understand that documents linked through `$ref` or `$append` are inserted into the calling document as text, not as parsed data objects.

.Strings vs Data
****
It is critical that you understand what you are processing when you pass "`data`" to a SchemaGraphy parser or validator.
In all cases when you are reading a YAML or JSON "`document,`" the original source format is technically a String, not what we call a Map or Array.

SchemaGraphy first reads semi-structured data (XML, HCL, TOML, etc) as a multi-line String object, then in the case of YAML or JSON, it loads data as a raw object.

[NOTE]
This means it sees comments, and it can identify non-standard, pre-load elements like `$ref`.

If YAML-__invalid__ syntax is mixed into your file (such as Liquid templating in your YAML file or comments in a JSON file), it is obviously still a text document in need of parsing, though this phase will still result in yet another a text document, this time YAML-valid.
Crucially, *this phase is non-standard for SchemaGraphy*, so you will still need an appropriate templating engine/utility to parse any kind of Liquid, Mustache, Haml, etc templating code _outside of what we call "`templated fields`"_.

Only once your YAML or JSON document is _loaded_ into a native data object is the SchemaGraphy parser able to apply the schema to it, and that is the form of the subject that will be acted upon.

Also remember that only in YAML files does SchemaGraphy pre-scan for comments, and only in YAML and JSON files does SchemaGraphy look for keys such as `$ref:`, `$refs:`, or `$append`, and only if these features are not disabled in the schema.
****

==== Universal Data Types

SchemaGraphy's unified data-typing system aligns with that of YAML, itself designed to map to JSON and most programming languages' native data types.
We make some namespace changes, but the basics are intact.

Here are the main data types in SchemaGraphy compared to their counterparts in YAML and JSON:

[cols="2,2,2",options="header"]
|===
| SchemaGraphy | YAML | JSON

| String       | string (`str`)   | String
| Integer      | integer (`int`)  | Number
| Float        | float            | Number
| Boolean      | boolean (`bool`) | Boolean
| DateTime     | timestamp        | String
| Array        | sequence (`seq`) | Array
| Map          | mapping (`map`)  | Object
|===

Most of these types have multiple variants, or "`formats`", which allow more specific designation, as necessary.

For full details on supported data types and formats, see <<data-types>>.

[[ruby]]
=== Ruby API

The most powerful and readily extensible way to use SchemaGraphy is within a Ruby runtime program.

[source,ruby]
----
require 'SchemaGraphy'

# Read and load the schema file
schema_doc = File.read('path/to/schema.yml')
schema     = SchemaGraphy::Schema.new(schema_doc)
# Read the data file
data_doc = File.read('path/to/data.json')
# validate and parse the data object, filling in any defaults left out
parsed_data = data_doc.schemagraphy_parse(schema_doc)
----

.Downsides of a Ruby API
****
Right now, a Ruby runtime environment is unlikely to be a popular add for any product's build environment.

If they are comfortable adding it Dockerized, this may still be an acceptable solution for many teams.

Even still, most teams will not opt to build on Ruby's API, even with the Ruby runtime incorporated.

This means a few simple commands need to handle all the important build logic and produce robust, usable results.

Nevertheless, I had to pick a source language, and the only one that might broadly please at this juncture is Node.js, which is also bound to disappoint for plenty of users even still.
The whole point of this project was to _define_ something that might be broadly portable, even if I only start it in Ruby.

With Ruby installed or containerized, custom-scripted CLI commands can be used to apply SchemaGraphs to data and content files, rendering transformed objects and writing new YAML or JSON objects after validation and parsing for your build routine to use down the line.
****

[[cli-invoke]]
=== CLI Invocation

SchemaGraphy is meant to be coded into your applications, where the gem API (or its counterparts for non-Ruby languages) can be called as classes, methods, etc.

However, for more universal use, including in non-Ruby scripts, the Graphy gem also includes a commandline utility that can be used to manually apply SchemaGraphs to data and content files, as well as performing other operations.

Use the CLI on your local files via our interactive *<<Docker image,docker-install>>* for reliable execution.

With Graphy <<installed,installation>>, run `graphy --help` to see the commandline options.

== Installation

There are several ways to start using SchemaGraphy and Graphy.

=== Docker Install

Use our RubyZsh Docker image for the fastest up-and-running with our whole stack and the wider Ruby world.

Unless you have been given specific instructions on how to implement Ruby in order to be in sync with your team or organization, or unless you are already adept with Linux/Unix systems, the Docker image really should help newcomers to the world of development environments.

link:https://github.com/docops/rubyzsh-docker[Try RubyZsh with your own Gemfile], or use it to generate a AYL-stack `Gemfile`.

.Example AYL-stack `Gemfile` for technical documentation:
[source,Ruby]
----
gem 'clide' # requires graphy, liquidoc, asciidoctor, liquid
gem 'jekyll-asciidoc-ui' # requires jekyll, asciidoctor, liquid
----

.Example AYL-stack `Gemfile` for legal documentation:
[source,ruby]
----
gem 'metapara' # requires graphy, liquidoc, asciidoctor, liquid
----

These files will instruct Docker to preinstall these programs and their many dependencies, including asciidoctor, liquid, jekyll, schemagraphy, graphy, and a domain-specific CLI tool (clide or metapara).
All the commands you need to bootstrap your project are included.

Legal workers can run `metapara get --templates moatlaw-samples` to download Liquid-infused documents.

== Development

There are three major areas of this codebase that are open to contribution:

. The SchemaGraphy model and SDL (schema definition language).
. The core processing and validation engines.
. The Graphy API and CLI.