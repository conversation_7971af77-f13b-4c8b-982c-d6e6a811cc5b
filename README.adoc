= ReleaseHx
// tag::ai-prompt[]
// Collects AsciiDoc content for presenting to a generative AI prompt
// Other AI-only prompt matter could go here
// tag::globals[]
:docopslab_git_www: https://github.com/DocOps
:this_prod_slug: releasehx
:releasehx_prod_repo: {docopslab_git_www}/{this_prod_slug}
:releasehx_docs_www: https://docopslab.github.io/{this_prod_slug}
:releasehx_demo_repo: {docopslab_git_www}/releasehx-demo
:this_prod_repo: {releasehx_prod_repo}
:this_prod_vrsn: 0.1.0
:next_prod_vrsn: 0.2.0
:tagline: Generate formatted release histories from JIRA, GitHub, GitLab, YAML, or JSON sources.
:description: pass:q[CLI utility and Ruby API for generating structured release notes and changelog documents from various issue-tracking platforms or YAML definitions into plaintext drafts (**AsciiDoc**, **Markdown**, **YAML**) and rich-text output (**HTML** and **PDF**).]
:gem_config_definition_path: ./specs/config-def.yml
:app_default_config_path: ./releasehx-config.yml
:markdown_extensions: .md, .markdown
:asciidoc_extensions: .adoc, .ad, .asciidoc
:yaml_extensions: .yml, .yaml, .rhyml
:draft_source_file_types: AsciiDoc, Markdown, YAML
:draft_source_extensions: {markdown_extensions}, {asciidoc_extensions}, {yaml_extensions}
:rendered_file_types: HTML, PDF
:rendered_extensions: .html, .pdf
// Dependencies
// end::globals[]
:toc: macro
:toclevels: 3

{tagline}

{description}

[NOTE]
This README serves as the both landing page and documentation for *Version {this_prod_vrsn}*.


== Overview

Invoke simple commands with powerful results!

. *Check for issues with missing release notes*.
Includes any issues marked `release_note_needed` or similar.
+
[.prompt]
 rhx 2.1.0 --check
 > Missing required release notes:
 > - JIRA-3412: Fix the thing (JoePSmith)
 > - JIRA-3482: Add this cool thing (RaeMDoe)

. While you are waiting for Joe and Rae to get their outstanding notes entered into JIRA... *retrieve JIRA issues* for version 2.1.0.
[.prompt]
 rhx 2.1.0 --yaml

[NOTE]
You can edit your Release Notes in Markdown or AsciiDoc files, or in YAML files with AsciiDoc or Markdown text formatting.
Whatever you prefer.

. When Joe and Rae finally post their notes... *amend the YAML doc*.
+
[.prompt]
 rhx 2.1.0 --append

. *Generate a Markdown variant* to your website source directory.
+
[.prompt]
 rhx 2.1.0 --md

. *Generate a PDF* to a documents archive.
+
[.prompt]
 rhx 2.1.0 --pdf

Numerous backends enable conversions from any source format to HTML or PDF output.

ReleaseHx can convert data into content through various processes, which you can adapt to your preferred workflow.

....
API  → Markdown → HTML/PDF
API  → AsciiDoc → HTML/PDF
API  → YAML     → HTML/PDF
API  → YAML     → AsciiDoc → HTML/PDF
API  → YAML     → Markdown → HTML/PDF
YAML → AsciiDoc → HTML/PDF
YAML → Markdown → HTML/PDF
API  → HTML/PDF
YAML → HTML/PDF
....

OR, use your Markdown/AsciiDoc-based *static-site generator (SSG)* to perform the final rendering to HTML!
Save or move drafts to your SSG's source path, commit, and publish.
// end::ai-prompt[]

=== Features

ReleaseHx is already packed with capabilities.

// tag::features-list[]
// tag::ai-prompt[]

* Generate [.key]*Release Notes* and/or [.key]*Changelogs* from your Issues tracker or Git.
* Source in [.buzz]*JIRA*, [.buzz]*GitHub*, [.buzz]*GitLab*, [.key]*Git commits*, or our special YAML syntax: *RHYML*.
* Readily configurable to use *your Issue Management API*.
* Draft in [.key]*Markdown*, [.key]*AsciiDoc*, or [.key]*YAML*.
* Render to [.key]*HTML* or [.key]*PDF* with *Asciidoctor*, *Pandoc*, and other converters.
* Customize output with [.buzz]*Liquid* templates.
* Ensure qualifying issues all have release notes.
* *Link-back* from notes or Changelog entries to issues and Git commits.
* Use [.key]*Git* to track changes in your release history documents.
* Use [.buzz]*Docker* to run ReleaseHx in any environment.
* Auto-update drafts with late-breaking release notes (RHYML only).
* Group and sort entries by *issue type*, subject *component*, *contributor*, or various *tags*.
* Invoke labels/tags such as *breaking*, *deprecation*, *experimental*, *highlight*, and *internal*.
* Use Issue labels or *checkboxes* to indicate Changelog inclusion or Release Note requirement.
// end::ai-prompt[]
// end::features-list[]

You can begin editing in YAML and update the file without losing changes, then generate a final draft in Markdown or AsciiDoc.

 rhx 2.1.0 --yml
 rhx 2.1.0 --append
 rhx 2.1.0 --adoc

The second line adds any late-arriving issues from the cloud.
The third line uses that `.yml` file to generate a `.adoc` file.

toc::[]


== Getting Started

To use ReleaseHx in your own projects, you will need either Ruby or Docker.
Use either the `rhx` CLI utility or the ReleaseHx Ruby API.

The only other rerequisite is possibly Git, if you are drawing any content/metadata from Git commits.

=== Non-Ruby Users

If you are not already a Ruby user, the `rhx` utility may be best used from our Docker image.

[NOTE]
You will need *Docker Desktop* installed link:https://docs.docker.com/desktop/setup/install/mac-install/[directly on MacOS] or link:https://docs.docker.com/desktop/features/wsl/[with WSL2 backend on Windows].
For Linux, use the link:https://docs.docker.com/engine/install/[Docker Engine install docs] if you're not already using Docker.

With Docker installed and running...

. Pull the Docker image.
+
[.prompt]
 docker pull docopslab/releasehx

. Run the `rhx` command.
+
[.prompt]
 docker run -it --rm -v $(pwd):/workdir docopslab/releasehx 2.1.0 --md
+
This mounts your local directory to be writeable by the Docker container.
Everything after `rhx` accepts the standard <<rhx,arguments and options>> of the ReleaseHx utility.

[TIP]
====
Optionally alias the base Docker command.

[.prompt]
 alias rhx='docker run -it --rm -v $(pwd):/workdir docopslab/releasehx rhx'
====

Try the <<demo-configs-and-data,demo configs and data>> to get a feel for how ReleaseHx works.

=== Ruby Users

ReleaseHx can be installed as a Ruby gem, for either CLI or API usage.

Option 1: System-wide installation::
[.prompt]
 gem install releasehx

Option 2: Project installation::
+
--
. Add the following line to your `Gemfile`:
+
[source,ruby]
[subs="attributes+"]
----
gem 'releasehx'
----

. Install the gem.
+
[.prompt]
 bundle install

. Use `bundle exec rhx` to execute.
--

=== Configuration Basics

Once installed, you will need to configure ReleaseHx to connect to your issue-management service (IMS) and to define how you want to organize your release history output.

By convention, ReleaseHx is customized according to a file stored at the root of your project called `releasehx-config.yml`.

[NOTE]
To use an alternate config file, provide a path at runtime using `--config PATH/TO/FILE`.

If you are ready to set up your remote issue management system's API, skip to <<issue-sources>>.

==== Demo Configs and Data

If you want to play around with ReleaseHx before connecting it to your own Issues via API, use the `link:releasehx_demo_repo[DocOps/releasehx-demo]` repository as instructed in this section.

Alternately, skip to <<issue-sources>> to begin configuring your own project/environment.

. Clone the demo repository.
+
[.prompt]
 <NAME_EMAIL>:DocOps/releasehx-demo.git

. Run the `rhx` command with the `--config` option. For example:
+
[.prompt]
 rhx 1.1.0 --config releasehx-demo/configs/basic.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --md demo-1.1.0.md

In this example, the `rest-1.1.0.json` file fills in for the version 1.1.0 issue tickets in a JIRA project, drafting them as a local Markdown file.

The demo repo contains several JSON files that simulate the data returned by the JIRA, GitHub, and GitLab APIs.

// TODO: FIX THESE
.Demo data versions in `issues/` directory
[cols="4m,2,1",options="header"]
|===
| File path
| Note field
| Tags source

| jira-customfield-note-1.1.0.json
| custom field
| labels

| jira-description-note-1.1.0.json
| description field
| labels

| github-checkbox-tags-1.1.0.json
| description field
| checkboxes

| gitlab-checkbox-tags-1.1.0.json
| description field
| checkboxes

| generic-1.1.1.json
| description field
| labels

| generic-1.1.2.json
| description field
| labels

| generic-1.2.0.json
| description field
| labels
|===

The demo repository also comes with a number of configuration arrangements, which you may switch to or freely edit.

// TODO: FIX THESE
.Demo Configurations in `configs/`
[cols="2m,3"]
|===
| jira-customfield.yml
| Release Notes and Changelog, default sort

| jira-customfield-resorted.yml
| Same content as previous, differently sorted

| jira-customfield-changelog.yml
| Changelog only

| jira-customfield-heavy.yml
| Lots of modifications just to show off

| github-basic.yml
| Release Notes and Changelog, default sort

| gitlab-basic.yml
| Release Notes and Changelog, default sort

| kitchen-sink.yml
| Fully loaded w/ default settings & comments
|===

Try the following series of commands and steps to see ReleaseHx in action.

. Check for issues missing release notes.
+
[.prompt]
 rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --check
+
Out of the box, there are two issues in the `jira-customfield-note-1.1.0.json` file that are marked `release_note_needed` yet have no release note filled out.

. Generate a YAML draft.
+
[.prompt]
 rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --yaml

. Edit the YAML draft in any way, then save.

. Add release notes to the two issues that were missing them in `releasehx-demo/issues/rest-1.1.0.json`.

. Update the YAML draft with the new issues.
+
[.prompt]
 rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --append

. Generate a Markdown draft.
+
[.prompt]
 rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --md

. Edit the Markdown draft in any way.

. Generate HTML and PDF output.
+
[.prompt]
 rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --html --pdf

==== Custom Configuration

If any of these demo configs seems like the right arrangement for you, copy it to your project directory and modify it to further suit your needs.

.Example
[.prompt]
 cp releasehx-demo/basic-config.yml releasehx-config.yml

See the <<config-ref>> for an annotated list of available settings.

=== Recommended Strategy

If you came here without a strong opinion about how to approach managing and publishing a Release History, read our <<rh-strategy>>.

If that's *tl;dr*, here are some key points:

:rhstrategy_release-history: pass:q[Publish a unified document called something like "`Release History`", with entries for each sequential release of your product.]

:rhstrategy_changelog: pass:q[Publish a "`Changelog`" or "`Change Log`" containing summaries for all user-facing product changes.]

:rhstrategy_release-notes: pass:q[Publish a "`Release Notes`" section containing an entry for any user-facing product change that requires _explanation_ or deserves special _highlighting_.]

:rhstrategy_changelog-entry: pass:q[Use the *_summary/title field_* of your issue-management system as the draft Changelog entry.]

:rhstrategy_release-entry: pass:q[Use a *_custom field_* (JIRA) or the *issue body* to designate and draft a release note.]

:rhstrategy_tags: pass:q[Use labels in your issue-management system to "`tag`" issues as *_belonging in the Changelog_* or *_needing a release note_* draft.]

:rhstrategy_check: pass:q[Use `rhx &lt;v.r.sn&gt; --check` to ensure all issues with a release note requirement have a release note.]

:rhstrategy_draft-yaml: pass:q[Generate and edit a *_YAML draft_* of your Release History until all release notes are drafted.]

:rhstrategy_draft-markup: pass:q[Generate a *_final draft_* in the format you use for the rest of yur docs.]

:rhstrategy_publish: pass:q[*_Publish_* your Release History to your website or other distribution channels.]

Recommended output:;;
* {rhstrategy_release-history}
* {rhstrategy_changelog}
* {rhstrategy_release-notes}

Manage issues like so:;;
* {rhstrategy_changelog-entry}
* {rhstrategy_release-entry}
* {rhstrategy_tags}
* {rhstrategy_check}

Use these procedures for each version:;;
* {rhstrategy_draft-yaml}
* {rhstrategy_draft-markup}
* {rhstrategy_publish}

[[issue-sources]]
=== Establishing Issues Source

ReleaseHx is source agnostic.
Your release history can start in any of the following sources:

* <<jira-issues>>
* <<github-issues>>
* <<gitlab-issues>>
* <<custom-api,Any issue-tracking system with a REST API>>
* <<issues-commits,Any combination of Issues and Git commits>>
* <<raw-git,Git alone>>
* <<rhyml,A YAML document ("`RHYML`")>>

For each instance of RelaseHX, you will need to configure one source or combination of sources from which to derive Changelog and Release Note content and metadata.

The three cloud services (JIRA, GitHub, GitLab) are configured similarly.

.Example API configuration in `{app_default_config_path}`
[source,yaml]
----
api:
  from: jira
  href: https://jira.example.com/rest/api/2
----

[NOTE]
If you wish to configure an API other than Jira, GitHub, or GitLab, see <<custom-api>>.

You can store your API username and token/secret key locally, either as a shell environment variable or in text file not tracked by Git.

.Example ENV variable storage
[.prompt]
 export RELEASEHX_API_KEY=mysecrettoken
 export RELEASEHX_API_USER=<EMAIL>

[TIP]
====
To make an environment variable permanent, write it to your shell profile file, then source it.

.Example export to Zsh config
[source,shell]
----
echo "export RELEASEHX_API_KEY=mysecrettoken" >> ~/.zshrc
source ~/.zshrc
----
====

If you prefer not to mess around with your shell profile or environment variables, you can store your API credentials in a file.

.Example file storage in `RELEASEHX_API_CRED` file
....
mysecretpasswordhashjumble
<EMAIL>
....

The first line of the file is the token/key, and the second line is a username, if required.

If you use such a file, be sure to keep it out of Git tracking.

.Example `.gitignore` entry
 RELEASEHX_API_CRED

Note that the _names_ of these files and environment variables can be modified, in case you already have these API credentials stored that way.
See the sub-properties of [.ppty]*<<conf_ppty_api_auth>>*.

[[jira-issues]]
==== JIRA Issues

ReleaseHx can connect to the JIRA REST API to fetch issues that match the release version.

The Issue *summary* field is used to draft the Changelog/title, and a custom field called `release_note` is used for the Release note.

[TIP]
Alternatively, configure your custom field with <<conf_ppty_sources_note_custom_field>>.

In JIRA, what ReleaseHx calls "`tags`" can be assigned using either JIRA labels or checkbox custom fields.

[[github-issues]]
==== GitHub Issues

ReleaseHx can connect to the GitHub Issues API to fetch issues from the release version.

The issue *title* field is used to draft the Changelog/title, and any text in the body that follows text like `# Release Note` is used for the Release note.

In GitHub, what ReleaseHx calls "`tags`" can be assigned using labels or checkboxes embedded after the Release Note text.

[[gitlab-issues]]
==== GitLab Issues

ReleaseHx can connect to the GitLab Issues API to fetch issues from the release version.

The issue *title* field is used to draft the Changelog/title, and any text in the body that follows text like `# Release Note` is used for the Release note.

In GitLab, what ReleaseHx calls "`tags`" can be assigned using labels or checkboxes embedded after the Release Note text.

[[custom-api]]
==== Custom API

ReleaseHx is extensible.
While it officially supports JIRA, GitHub, and GitLab, you can configure it to connect to any issue-tracking system with a REST API.

Use the application config file to designate an API endpoint and any required authentication.
Then provide a mapping file at `_mappings/<api_from_name>.yml` (or the path configured at <<conf_ppty_paths_custom_mappings_dir>>) to define the data conversion to RHYML.

See <<custom-api-config>> for details.

[[issues-commits]]
==== Issues Plus Git Commits

Assuming your JIRA instance is integrated with your Git host (which is automatically the case if you use GitLab or GitHub Issues) and you are working in the related repository, you can connect issues to Git commits.

When sourcing Release Note content from Git commits, you will need a few config settings.

.Example `{app_default_config_path}` properties
[source,yaml]
----
sources:
  note: commit_message
  pattern: '^RELEASE NOTE:'
  summary: commit_message
----

If `note` is set to `commit_message`, ReleaseHx will extract the content from everything following the `pattern` text in the commit message referenced by the commit SHA/hash in the Issue metadata.
To use the entire "`body`" of the commit message as the note, set the `pattern` to `^`.

[NOTE]
ReleaseHx expects commit messages to be formatted with the summary/header on the first line, followed by a blank line, then the body.
Note content is expected to be the entire body or embedded at the end of it, delineated by the value of [.ppty]*<<conf_ppty_sources_note_pattern>>*.

If you are not using content from the Git commit as your Changelog/Release Notes entry material, the relationship is just for metadata.
Release notes and Changelog entries can reference and/or link to the Git commit when so configured.
To enable this, fill out the [.ppty]*<<conf_ppty_links_web>>* and [.ppty]*<<conf_ppty_links_git>>* properties in your config file.

During drafting, ReleaseHx will strip from the `note` property any lines that begin with `#`.

[[raw-git]]
==== Raw Git Commits

You can source a Release History entirely inside Git commits, with largely the same capacity, as long as you are willing to format your commits precisely.

See the explanation in <<issues-commits>> for designating Release Note content in a commit message "`body`", but note you may also replace the labels/tags aspect of issue-management systms, as follows.

Use `\#tagname` or `#tagkind:taglabel` strings to assign tags.
ReleaseHx can be configured to detect such tags anywhere in the commit message "`body`", but will strip from the `note` property content any lines that begin with `#`.

During drafting, ReleaseHx will drop from the final note any line that begins with a `#` character from the content, including a line that might be `#changelog`.

The limitations of working this way should be obvious.
It is difficult and typically unwise to edit commits after they have been merged, so tags and such would need to be added to the initial merge commit for the changed feature itself.

.Example Git commit string ReleaseHx can parse
....
commit a3b14d8e9c2f5e7b0d1a2c3d4e5f6a7b8c9d0e1
Author: John Smith <<EMAIL>>
Date:   Thu Mar 21 15:32:14 2024 -0400

Add new user authentication flow

Introduces a complete overhaul of the user authentication system.

RELEASE NOTE:
The login process now supports multi-factor authentication and single sign-on options. Users will need to re-authenticate on their next login to set up these new security features.

#breaking #highlight #type:improvement #component:auth #component:ui
....

This method requires more discipline on the part of developers, but it is arguably the most streamlined option, especially for working without an issue-tracking system.

[[rhyml]]
==== RHYML

Use a locally stored YAML file as the source of your Release History and Changelog content.
This is essentially the same document that is drafted from a REST API source by the `--yaml` flag for the `rhx` command.

[NOTE]
RHYML is referred to as an API source throughout most ReleaseHx documentation, even though it is not a remote/HTTP API.

All issues associated with a given release are nested together under a `work:` property without further hierarchy.
Since the entries are _data_, they can be oraganized here however you wish, then re-sorted upon generating a Markdown or AsciiDoc draft, or going directly to PDF or HTML.

The advantage of this method is working without an API.
Contributors can simply add their notes to a unified file via Git, and that file can be edited in place.

The RHYML content can be connected to Issues and Git commits, but only tangentially.
The summary and note content needs to be listed in the YAML document.
Links back to the source issue or commit are possible but optional.


== Usage

ReleaseHx can either be used as a commandline tool or as a Ruby library.
In both cases it can be powerfully configured with regard to its source matter and its output formats.

We will first look at what ReleaseHx can _do_, then we we will explore _how_ to make ReleaseHx achieve these goals.

=== Output Strategy

You can output ReleaseHx-generated histories as HTML or PDF, each with styling capabilities.

Other options include which history types to output: Changelog, Release Notes, or hybrid.
If outputting both sequentially, in which order, and what exactly to include in each resource.

[NOTE]
If all you ever want to report is a 1-line summary of changes, you just need a Changelog.

Some logic rules that may help you decide:

Upstream/Source rules::

. Any issue with a Release Note entry will also be included in the Changelog.
. For issues with no Release Note entry, a `changelog` label or checkbox is needed for inclusion.

Output rules/policies::

. Changelog entries will link to any corresponding release note.
. Changelogs and Release Notes sections may be organized and ordered distinctly.
. The entries within each section (Changelog or Release Notes) may be arranged and sorted according to different rules.

==== Output Configuration

For designating _what_ to output, the following blocks or properties in the config file are relevant:

`history`::
This block defines the overall document and establishes defaults that apply to `notes` and `changelog` sections.

`notes`::
This block defines the Release Notes output.

`changelog`::
This block defines the Changelog output.

==== Output Templating

Place templates in the directory established in `*paths.templates_dir*` in the config (defaults to `./_templates`).

Templates replace their namesakes built into the ReleaseHx application or API.

By default, ReleaseHx expects templates to be formatted in link:https://shopify.github.io[Liquid].

[NOTE]
ReleaseHx uses Liquid version `~> {liquid_version_minor}`, which is incompatible with Ruby apps still using Liquid 4, such as Jekyll and LiquiDoc.
For this reason, you may need to maintain separate Gemfiles in a repository where different apps use incompatible versions of Liquid.

=== Sourcing Strategy

Where will your release history content come from, and in what format(s) will you _edit_ it?

==== Task-tracking Source

If your team uses a supported issue-management system (JIRA, GitHub Issues, or GitLab Issues), you will almost certainly wish to integrate that platform.

Additionally, assuming your team uses Git, you _may_ wish to derive content from Git commits.
This is only the case if your commit messages are suited to including drafts of release notes, which is fairly rare.

Generally, you will derive change summaries, notes, and metadata from your IMS, but hybrid sourcing is readily configurable.
Since you operate ReleaseHx in your product repository, it _uses Git directly on your system_ rather than relying on your cloud-hosted Git service.

==== Local Flat-file Source

Once the relevant "`issues`" have been derived from your API or Git, they become "`changes`" in ReleaseHx terminology, and they are held as a data object in RHYML format.

At this point, these changes can be converted to YAML, Markdown, or AsciiDoc so you can tinker with them.
This process is called "`drafting`" -- it compiles your changes into one document.

The recommended procedure is to use YAML at this phase.
Only the YAML format maintains the changes as data and as a single source of truth.
If you make a change in the RHYML/YAML document, you can still readily convert to Markdown or AsciiDoc at any point.

Edit-at-source method::
However, if you are confident in the overall shape of your issues at the source, drafting directly to Markdown or AsciiDoc, or even converting directly to HTML or PDF, are available options.
+
Indeed, _if you wish to do all of your editing in the IMS interface_, this is the way to go.
You can generate Markdown or HTML, review, and make further changes to the IMS issues, then regenerate.

[[rh-strategy]]
=== Recommended Strategy for Release Documentation

We stand behind the following design principles, but ReleaseHx can enable all this and more.

We highly recommend the sites link:https://keepachangelog.com[keep a changelog] and link:https://common-changelog.org[Common Changelog] for guidance.

For guidance on Release Notes authoring, check out link:https://www.releasepad.io/the-complete-guide-to-release-notes-what-are-they-and-what-are-they-used-for/[ReleasePad's "`Complete Guide`"] and link:https://www.prodpad.com/blog/release-notes/[ProdPad's "`How-to`" article].

{rhstrategy_release-history}::
The constituent documents, Changelog and Release Notes, are both part of the Release History.
The recommended order is Changelog first, Release Notes second, with links from applicable Changelog entries to corresponding Release Notes.
+
The hybrid strategy would basically be an annotated Changelog, where every designated summary is listed with relevant metadata release note, when available.

{rhstrategy_changelog}::
Any and all changes that affect users _must_ be listed here.
+
A separate, more complete changelog can be published for developers, with issues marked `internal` also displayed, possibly annotated as such.
Using `rhx &lt;v.r.sn&gt; --md --internal` will include internal issues in the (Markdown) draft.

{rhstrategy_release-notes}::
The general rule of thumb is that any change that is not obvious from its summary should have a release note.
+
Release notes can be fairly involved, including short bulleted lists or tables.
Anything longer than a few sentences or a short list or table should link to documentation or a release appendix.

{rhstrategy_changelog-entry}::
Alternatively, use the first line of Git commits or the release-note body as the Changelog entry draft.
+
If original entries are in present tense or imperative, you can use the [.ppty]*<<conf_ppty_rhyml_pasterize_summ>>* property to convert verbs to past tense.

{rhstrategy_release-entry}::
A distinct field is optimal, but on any platform you can demarcate release-note content with a comment like `<!-- release note +++-->+++` or a heading like `## Release note`.

{rhstrategy_tags}::
JIRA supports custom checkboxes, and GitHub/GitLab enable Markdown checboxes, all of which ReleaseHx can scan.
+
Also mark issues with tags like `breaking`, `deprecation`, `experimental`, `highlight`, and `internal`.

{rhstrategy_check}::
As long as you have marked relevant issues with the `release_note_needed` tag, you can use the `--check` option to ensure all issues with that tag have a release note.
+
The exact tag is configurable at [.ppty]*<<conf_ppty_tags_release_note_needed>>*.

{rhstrategy_draft-yaml}::
This means generating an RHYML document and editing it in place if release notes are still streaming in from the IMS.
Use `--append` to integrate last-minute release notes before generating a final draft.

{rhstrategy_draft-markup}::
When all the release notes have been added, generate a final draft in your preferred lightweight markup format.
This is the best place to perform a final once-over and see the content more or less as it will be published.
+
Generate rich-text drafts as needed.
These are easy to overwrite.

{rhstrategy_publish}::
ReleaseHx makes it possible to render full web pages, but you probably want to situate the content in your static-site generator.
+
If you wish to edit in one markup format (Markdown or AsciiDoc) but your SSG expects the other format, you can use the flags `--html --no-wrap --frontmatter` to render the innards of a page, which most SSGs can publish wrapped in an HTML layout.

==== CI/CD Strategy Caveats

If your team works in a continuous-deployment environment, you may wish to maintain one ongoing Release History.

To do so, modify your API request template with some logical filter, and always use the `--append` option, drafting to YAML.

Continuous-deplyment environments will likely get better treatment in this application prior to the 1.0 release.
I just don't have enough experience with them to predict the optimal workflow.

[[rhx]]
=== The `rhx` Utility

For usage outside (or within) a Ruby development environment, ReleaseHx provides the `rhx` commandline tool.

.Help screen
// tag::helpscreen[]
// tag::helpscreen_attrs[]
:cli_option_message_md: Draft to Markdown
:cli_option_message_adoc: Draft to AsciiDoc
:cli_option_message_yaml: Draft to YAML
:cli_option_message_html: Render to HTML
:cli_option_message_pdf: Render to PDF
:cli_option_message_json: Use a JSON source file instead of API
:cli_option_message_config: Config location (default: ./releasehx-config.yml)
:cli_option_message_mapping: Alternate API mapping location
:cli_option_message_fetch: Refresh data from source
:cli_option_message_date: Use a specific date for the release
:cli_option_message_hash: Assign a commit hash to the release
:cli_option_message_append: Add any new issues to the end of local YAML source
:cli_option_message_over: Overwrite any existing files without prompting
:cli_option_message_check: Find issues with missing release note
:cli_option_message_emptynotes: Set/reverse policy on issues "awaiting notes"
:cli_option_message_internal: Include issues marked internal or likewise
:cli_option_message_wrap: Render HTML with/out head and body tags
:cli_option_message_frontmatter: Render or draft with/out frontmatter
:cli_option_message_manpage: Show the full manpage documentation
:cli_option_message_verbose: Express each step to console
:cli_option_message_debug: Express each step and dump data to console
:cli_option_message_quiet: Suppress all output, including warnings

// end::helpscreen_attrs[]
[subs="attributes+"]
....
Usage: rhx VERSION|FILE [options]

Options:
  --md [PATH]            {cli_option_message_md}
  --adoc, --ad [PATH]    {cli_option_message_adoc}
  --yaml, --yml [PATH]   {cli_option_message_yaml}
  --html [PATH]          {cli_option_message_html}
  --pdf [PATH]           {cli_option_message_pdf}
  --json PATH            {cli_option_message_json}

  --config PATH          {cli_option_message_config}
  --mapping PATH         {cli_option_message_mapping}
  --fetch                {cli_option_message_fetch}
  --date DATE            {cli_option_message_date}
  --hash, --sha SHA256   {cli_option_message_hash}
  --append               {cli_option_message_append}
  --over, --force        {cli_option_message_over}
  --check, --scan        {cli_option_message_check}
  --empty, -e [RULE]     {cli_option_message_emptynotes}
  --internal             {cli_option_message_internal}
  --[no-]wrap            {cli_option_message_wrap}
  --[no-]frontmatter     {cli_option_message_frontmatter}

  --manpage, --man       {cli_option_message_manpage}
  --verbose              {cli_option_message_verbose}
  --debug                {cli_option_message_debug}
  --quiet                {cli_option_message_quiet}
....
// end::helpscreen[]

See <<cli-options-ref>> for detailed descriptions of each option.

[[workflows]]
==== Potential Workflows

ReleaseHx enables several workflow combinations for drafting and rendering release histories.

....
API → Markdown
API → Markdown → HTML
API → Markdown → PDF
API → AsciiDoc
API → AsciiDoc → HTML
API → AsciiDoc → PDF
API → YAML     → HTML
API → YAML     → AsciiDoc → HTML
API → YAML     → AsciiDoc → PDF
API → YAML     → Markdown → HTML
API → YAML     → Markdown → PDF
....

The *API* element of the above workflows could be a *RHYML* file, in which case the *API → YAML* conversions are unnecessary, as the YAML step in those workflows is a proper RHYML document already.

.Why RHYML drafts?
****
Some reasons you might wish to use *RHYML (YAML) drafts* as an interim state:

If you expect late-arriving issues, but you want to get started copy editing the ones that exist.

Going directly from YAML to HTML gives more control over the final output, as Markdown and even AsciiDOc are quite limited in the semantic HTML they can produce.

You may even come to prefer editing serialized short content in YAML, as I have come to.
****

Most of the workflow cases can be executed using fairly straightforward command combinations.

Take this *API → Markdown → HTML/PDF* workflow for instance:

. Ensure all release notes are added in the IMS source.
+
 rhx 2.1.0 --check

. Create a Markdown draft from API data.
+
 rhx 2.1.0 --md

. Edit and save the Markdown draft.

. Render HTML and PDF files from the Markdown draft.
+
 rhx 2.1.0 --html --pdf

But for cases where you wish to draft/edit in YAML and then in Markdown or AsciiDoc, such as *API → YAML → AsciiDoc → HTML/PDF*, the following series of steps is exemplary:

. Create a YAML draft from API data.
+
 rhx 2.1.0 --yaml

. Edit and save the YAML draft.

. Add any newly annotated issues to the end of the YAML draft.
+
 rhx 2.1.0 --append

. Create an AsciiDoc draft from the YAML draft.
+
 rhx 2.1.0 --adoc
+
NOTE: ReleaseHx looks for `2.1.0.yml` and creates an AsciiDoc draft like `2.1.0.adoc`.

. Edit that draft as AsciiDoc and save.

. Render HTML and PDF from the AsciiDoc draft.
+
 rhx 2.1.0 --html --pdf
+
NOTE: ReleaseHx finds both `2.1.0.yml` and `2.1.0.adoc`, choosing the latter.

It is also possible to *source directly in RHYML* files and draft to Markdown or AsciiDoc or else directl to HTML/PDF.

[[cli-options-ref]]
==== CLI Options Reference

The following options are available for the `releasehx`/`rhx` commands.

// tag::cli_options[]
// tag::ai-prompt[]
:drafts_path_key: pass:q[Where `&lt;drafts_dir>` is the value of `paths.drafts_dir` in the config, and `&lt;template>` is the value of `templates.drafts_filename` in the config.]

*--adoc, --ad* [_PATH_]::
{cli_option_message_adoc}.
Outputs to `<drafts_dir>/<template>` or `<PATH>`.
+
{drafts_path_key}

*--append*::
{cli_option_message_append}.
+
When drafting in YAML, adds new issues to the end of the file.
Be sure to save edits before appending.

*--check, --scan*::
{cli_option_message_check}.
:cli_option_message_check_long: Scans issues for those missing release notes and reports findings.
{cli_option_message_check_long}

*--config* _PATH_::
{cli_option_message_config}.
Use the configuration file at the specified path instead of the default location (`{app_default_config_path}`).

*--debug*::
{cli_option_message_debug}.

*--empty, -e* [_RULE_]::
{cli_option_message_emptynotes}.
+
Argue a specific drafting policy, or argue the "`opposite`" policy, for handling issues that are marked as `release_note_needed` but no note is provided.
Set a specific rule (`skip`, `empty`, `dump`, or `ai``) to have ReleaseHx include the issue when converting issues to changes, even if no expected note content has been aded.
+
Using `-e dump` will draft the issue with the entire issue body and commit message as the note content, for any qualifing change entry.
Whereas `-e ai` will use generative AI to draft note properties from issue body and commit message.
+
Otherwise use just `--empty` or `-e` to toggle between `skip` and `empty`, if either of those is your default in [.ppty]*<<conf_ppty_rhyml_empty_notes>>*.
If your default is `blank`, `dump`, or `ai`, using `--empty` or `-e` with no argument will toggle a `skip` policy.

*--fetch*::
{cli_option_message_fetch}.
+
Retrieves fresh data rather than using cached/draft files when converting to HTML/PDF.
Typically used like:
+
 rhx 1.1.0 --fetch --html
+
The fetch procedure does write a cached RHYML document before generating final output.

*--frontmatter, --no-frontmatter*::
{cli_option_message_frontmatter}.
When generating drafts or rendering HTML output, _include_ (`--frontmatter`) or _exclude_ (`--no-frontmatter`) frontmatter.

*--html* [_PATH_]::
Renders to HTML from default or designated source.
Writes to `<output_path>/<template>` or `<PATH>`.

*--internal*::
{cli_option_message_internal}.
Include issues marked as internal or similarly restricted when drafting content.
Has no effect on render operations.

*--json* _PATH_::
{cli_option_message_json}.
+
Override the configured API connection and use static JSON data from the designated file.
+
This file is formatted as a response from the API configured at `api.from`.
This is a pre-RHYML data form.

*--md* [_PATH_]::
{cli_option_message_md}.
Outputs to `<drafts_dir>/<template>` or `<PATH>`.
+
{drafts_path_key}

*--manpage, --man*::
{cli_option_message_manpage}.
Includes this options reference and other documentation, all in the terminal.
+
TIP: Use `q` to quit back to prompt.

*--mapping*::
{cli_opton_message_mapping}.
File must be a valid RHYML mapping config, usually stored at `_mapping/<apiname>.yaml`.
+
The mapping base directory can be changed in [.ppty]*<<conf_ppty_paths_custom_mappings_dir>>*, but this option must include a complete relative or absolute path.

*--over, --force*::
{cli_option_message_over}.
When writing files, overwrite existing files without prompting for confirmation.

*--pdf* [_PATH_]::
Renders to PDF from default or designated source.
Writes to `<output_path>/<template>` or `<PATH>`.

*--scan, --check*::
{cli_option_message_check}.
{cli_option_message_check_long}.

*--verbose*::
{cli_option_message_verbose} during execution.

*--wrap, --no-wrap*::
{cli_option_message_wrap}.
When rendering HTML, _include_ (`--wrap`) or _exclude_ (`--no-wrap`) the `<head>` and `<body>` tags and their content.
For use when the opposite value is set in the config file ([.ppty]*<<conf_ppty_modes_wrapped>>*).

*--quiet*::
{cli_option_message_quiet}.

*--yaml, --yml* [_PATH_]::
{cli_option_message_yaml}.
Outputs to `<drafts_dir>/<template>` or `<PATH>`.
+
{drafts_path_key}
// end::cli_options[]
// end::ai-prompt[]

[[advanced-rhyml]]
=== Advanced RHYML

The RHYML syntax is designed specifically for tracking product changes and collecting them as "`releases`".

RHYML is also designed particularly for YAML so it can be read, edited, and even authored by humans, including non-programmers.

A complete Release History is a collection of planned product releases as, including patch releases, so RHYML has blocks for major/minor releases and for their subordinate patch releases.

The full RHYML structure is:

[source,yaml]
----
releases: # Optional key to contain multiple releases
  - # Array (sequence) of releases
    code: 1.2.1 # Required key for an individual release
    memo: |
      A note of any length, formatted as Markdown (default) or AsciiDoc (configured).
    changes:    # Required key for Array of changes
      -         # Array (seqence) of changes
        chid: 1234 # change ID
        tick: 5678 # issue ticket ID
        hash: abcdef0123456789abcdef0123456789abcdef01
        type: feature
        part: auth
        summ: "Add new user authentication flow"
        note: |
          The login process now supports multi-factor authentication and single sign-on options.
          Users will need to re-authenticate on their next login to set up these new security features.
        tags: # issue labels
          - breaking
          - highlight
          - component:ui
----

However, here we will focus on the matter that ReleaseHx deals with: individual, sequential _releases_, which are identically structured whether for major/minor or patch releases.

The two required properties for a release are `code` (version ID) and `changes`, but a release can also have a `date` and a `memo`.

Memos can be formatted with Markdown or AsciiDoc.
This format must be set either in an individual RHYML file or configured at [.ppty]*<<conf_ppty_rhyml_markup>>*.

[[config-ref]]
=== Configuration Reference

// tag::config-ref[]

include::build/docs/config-reference.adoc[]

// end::config-ref[]

[[sample-config]]
=== Sample Configurations

[source,yaml]
----
include::build/docs/sample-config.yml[]
----

[[custom-api-config]]
==== Custom API Configuration

// tag::custom-api-config[]

ReleaseHx's API connections are extensible.
As long as you can map the JSON payload returned by your preferred issue-management system provider, you are welcome to add it.

[NOTE]
If you add a non-standard API, please consider contributing it upstream to the ReleaseHx project.

// TODO: Document custom mapping

// end::custom-api-config[]

=== RHYML Schema

// tag::rhyml-schema[]

// end::rhyml-schema[]

=== Templating Guide

ReleaseHx generally uses enhanced *Liquid 4 templates* to generate new files and content from RHYML and configuration data.

Notably, it employs *link:https://jekyllrb.com/docs/liquid/[Jekyll's extended tags and filters]*, as well as some additional tag and several filters provided by Sourcerer.

Here we document the custom filters added by the Sourcerer module and ReleaseHx itself.

==== Custom Liquid Tags

ReleaseHx uses Jekyll's version of the `include` tag, rather than Liquid's.
It also supports Jekyll's `include_relative` tag.

link:https://jekyllrb.com/docs/includes/[See Jekyll's docs for more information.]

This works essentially like Liquid 5's `render` tag, which is not available in ReleaseHx.

ReleaseHx supports a tag called `embed` which takes no arguments and works exactlly like Liquid's `include` tag.

The included file has access to all the variables in the parent template and passes any newly created or modified variables back to affect any subsequent content in the parent template.

==== Custom Liquid Filters

These filters can be added to link:https://shopify.github.io/liquid/[Liquid's master list of filters] and link:https://jekyllrb.com/docs/liquid/filters[Jekyll's extended filters].
Jekyll filters always supercede same-named Liquid filters, including `where`.

plusify::
Replace double line breaks (`+++\n\n+++`) with `+++\n+\n+++`.
example:::
`{{ note | plusify }}`

md_to_asciidoc::
Uses Kramdown-AsciiDoc (Kramdoc) to convert Markdown to AsciiDoc.
arguments:::
wrap::::
How to handle line wrapping.
Can be 'preserve', 'ventilate', or 'none'.
The `ventilate` option presents places all sentences on their own lines.
example:::
`{{ note | md_to_asciidoc: "ventilate" }}`

render::
Renders a string as a Liquid template with the provided variables.
arguments:::
vars::::
A hash of variables to pass to the template.
example:::
`{{ note | render: vars }}`

indent::
Indents each line of the input by the specified number of spaces.
arguments:::
spaces::::
The number of spaces to indent by.
line1::::
If true, also indents the first line.
example:::
`{{ note | indent: 2, line1: true }}`

sgyml_type::
+
--
Returns a string representing the SGYML _kind_ and _class_ of the input, separated by a colon (`:`).

Response will be one of the following:

* `Null:nil`
* `Scalar:String`
* `Scalar:Number`
* `Scalar:DateTime`
* `Scalar:Boolean`
* `Enumerable:Array`
* `Enumerable:ArrayList`
* `Enumerable:Map`
* `unknown:unknown`
--
example:::
`{{ id | type_check }}`

ruby_class::
Returns the Ruby class name of the input.
example:::
`{{ id | ruby_class }}`

demarkupify::
Simplifies any Markdown and AsciiDoc syntax in the inline input.
+
Strips `*` and `_` quotes, simplifies `+++"`+++` and `+++'`+++` quotes and UTF-8 curly quotes, and removes all backticks.
example:::
`{{ note | demarkupify }}`

pasterize::
Converts select verbs in the input from present/imperative to past tense.
+
Replaces common terms like `add`/`adds` with `added`, `fix`/`fixes` with `fixed`, `build`/`builds` with `built`, etc.
example:::
`{{ summary | pasterize }}`

inspect_yaml::
Returns a YAML representation of the input for debugging purposes.
example:::
`{{ changes | inspect_yaml }}`

[[releasehx-api]]
=== ReleaseHx API

Full documentation is coming, but for now just an overview of the classes and methods introduced by this gem.

==== Classes

Release::
A collection of changes made to the product since the last release.
Includes release metadata and an Array of Change objects.

Change::
The record of a single user-facing change made to the product.
Includes metadata about the Release to which it belongs.


== Development

ReleaseHx is free, open source, and *open for contributions*.
Get in touch or open an issue to get involved.

The remainder of this document refers to _how ReleaseHx is made_ rather than how to use it for your own product.

=== Background

ReleaseHx is my fourth or fifth time tackling this problem, though in previous cases I had the misfortune (luxury?) of solving it for one company at a time, and never in such a robust way that the results would be worth open sourcing.

All of my employers and clients in the past 10 years needed a system like this, and some of them paid me to make them.
But before all that, I inherited a Python script that converted JIRA issue fields into AsciiDoc, for rendering to PDF and HTML.

Using and hacking at that script for years of documenting GA releases for an entire enterprise software product made me appreciate the need for cloud sourcing and automation, for tne benefit of all stakeholders.

I also spent years hanging in the #release-notes channel of the link:https:writethedocs.org/slack[Write the Docs Slack], over and over again advising technical writers on this broad problem space.
"`How do you convert JIRA tickets to release notes?`" or "`How do you automate a changelog from Git or GitHub Issues?`" are typical inquiries there.

The short answer is:

. Use your team's preferred scripting language to:
[lowerroman]
.. connect to the REST API, then
.. download issues for the upcoming release.
. Use your team's favorite template engine to render notes or log entries into your preferred lightweight markup.
.. Markdown *or*
.. AsciiDoc
. Edit the content manually in Markdown/AsciiDoc, then:
.. commit it to your existing docs SSG *or* 
.. push it to Confluence or the product's deployment pipeline

Easier said than done, especially for TWs working with scarce developer resources.

I am unaware of any utility that serves this need broadly and flexibly, and in all these years, nobody has ever recommended one in WTD Slack.
Hopefully, ReleaseHx will fill much of this void in an adaptable and repeatable fashion.

[[ddd]]
=== Docs-driven Development

The ReleaseHx gem is an example of README-first development.
Not only is the README written in AsciiDoc, and not only is documentation done first in the README during early development, but much of the documentation and product data is single-sourced in the README as AsciiDoc _attributes_.

This unorthodox approach requires some explanation.

At the very beginning of the build procedure for the application's Ruby gem, (1) data is ingested and (2) snippets are harvested from the `README.adoc` file for internal or user-facing purposes in the application.

For instance, the application version number is derived from the attribute `:this_prod_vrsn:` in the README.
See, I can even place it here without fear that it will ever fall out of step:
*{this_prod_vrsn}*.

Additionally, the help screen itself is sourced here in this README and included verbatim -- after rendering with link:https://asciidoctor.org[Asciidoctor] -- in the CLI's output.

The application help and manpage documentation is also sourced in this way.
(Use `rhx --help` or `rhx --man` to reveal.)

This capability is provided by the Sourcerer module introduced in this gem but intended to be spun off into it own gem for use in all my (and any of your) Ruby projects in the future.

=== Issue-data Mapping

// tag::mapping-table[]
// tag::ai-prompt[]
.Upstream/source issue data mapping table
[cols="1a,1a,1a,1a,1a",options=header]
|===

| JIRA | GitHub | GitLab | RHYML | Ruby/Liquid

| N/A
| N/A
| N/A
| `chid`
| `chid`

| `key`
| `number`
| `iid`
| `tick`
| `tick`

| `commit` (custom field, if available)
| associated commit hash (via PR merge or commit references)
| commit hash from merge request or commit ref
| `hash`
| `hash`

| `issuetype` or label matching slug in `types`
| `type` or label matching slug in `types`
| `issue_type` or label matching slug in `types`
| `type`
| `type`

| `component`
| label: `component:<component>`
| label: `component::<component>` or scoped labels
| `part`
| `part`

| `summary`
| `title`
| `title`
| `summ`
| `summ`

| N/A
| N/A
| N/A
| `head`
| `head`

| _custom field_
| `+++## Release Note+++` in body
| `+++## Release Note+++` in body
| `note`
| `note`

| label: `deprecation `
| label: `deprecation `
| label: `deprecation`
| `tags: [deprecation]`
| `tags['deprecation']` / `deprecation` Boolean

| label: `breaking`
| label: `breaking`
| label: `breaking`
| `tags: [breaking] `
| `tags['breaking']` / `breaking` Boolean

| label: `experimental`
| label: `experimental`
| label: `experimental`
| `tags: [experimental]`
| `tags['experimental']` / `experimental` Boolean

| label: `highlight`
| label: `highlight`
| label: `highlight`
| `tags: [highlight]`
| `tags['highlight']` / `highlight` Boolean

| label: `release_note_needed`
| label: `release_note_needed`
| label: `release_note_needed`
| N/A
| N/A

| label: `changelog`
| label: `changelog`
| label: `changelog`
| N/A
| N/A

| `fixVersions`
| `milestone`
| `milestone`
| nested context
| `version`

| `assignee`
| `assignee`
| `assignee`
| `lead `
| `lead`

|===
// end::ai-prompt[]
// end::mapping-table[]

=== Codebase Structure

Here is a model of the ReleaseHx gem's codebase.

// tag::ai-prompt[]
.File tree of key files
[source,text,subs="attributes+"]
....
Dockerfile                       # Docker image definition
releasehx.gemspec                # Gem definition
README.adoc                      # <{counter:ftco}> Single source of truth
build/                           # Untracked, ephemeral path for generated assets
docs/
└── manpage.adoc                 # <{counter:ftco}> Source for CLI manual page
specs/
    ├── config-def.yml            # <{counter:ftco}> Configuration definition
    ├── rhyml-schema.yaml         # <{counter:ftco}> RHYML schema definition
    └── rhyml-mapping-schema.yaml # <{counter:ftco}> API -> RHYML definition
lib/
├── releasehx.rb                 # Application core
├── schemagraphy.rb              # Special YAML handling
├── sourcerer.rb                 # <{counter:ftco}> Single-sourcing tool
├── liquid.rb                    # <{counter:ftco}> Liquid customizations
├── releasehx/
│   ├── cli.rb
│   ├── configuration.rb         # <{counter:ftco}> CFGYML parsing
│   ├── generated.rb             # "Pre-build" material (untracked)
│   ├── rhyml/                   # module RHYML
│   │   ├── change.rb            # class Change
│   │   ├── release.rb           # class Release
│   │   ├── adapter.rb           # maps from JSON using a mapping file
│   │   ├── loader.rb            # loads RHYML YAML or JSON from disk
│   │   ├── validator.rb         # validates against schema
│   │   └── renderer.rb          # to_h, to_yaml, to_liquid, etc.
│   ├── sgyml/helpers.rb         # module SgymlHelpers
│   ├── generators/
│   │   ├── parsers.rb           # <{counter:ftco}> Draft processes
│   │   └── renderers.rb         # <{counter:ftco}> Rich-text conversion processes
│   ├── templates/               # Docs-generating templates
│   │   ├── cfgyml/              # Docs/sample templates for config
│   │   ├── api-mapping/         # Docs for API -> RHYML mapping
│   │   └── rhyml/               # RHYML, Markdown, AsciiDoc conversion templates
│   └── utilities/
│       ├── git.rb               # Git integration (Rugged)
│       ├── asciidocify-md.rb    # Markdown to AsciiDoc
│       └── validators.rb        # Check special YAML files (RHYML, CFGYML)
├── schemagraphy/
│   ├── loader.rb                # `SchemaGraphy::Loader`
│   ├── tag_utils.rb             # `detag`, `tag_of`, etc
│   ├── templating.rb            # Definess handling of parsable YAML nodes
│   └── schema_utils.rb          # `get_schema_defaults`, etc
└── sourcerer/
    ├── builder.rb               # Writes snippets to files at build time
    └── plaintext-converter.rb   # Pre-processes AsciiDoc source files
....
// end::ai-prompt[]

[.callouts]
. Used to generate a terminal manual page.

. See <<config-def>>.

. See <<rhyml>>.

. See <<custom-api-config>>.

. See <<sourcerer>>.

. See <<config-def>>.

. See <<issue-sources>>.

. See <<rhyml>>.

. Parsers use Liquid to generate YAML, Markdown, and AsciiDoc drafts.

. Renderers use Asciidoctor, Pandoc, or other converters to generate HTML and PDF.

. Templates for draft and output markup, which users can override.

. See <<ddd>>.

[[sourcerer]]
=== Sourcerer

This gem introduces a module called Sourcerer, by which AsciiDoc files can be parsed and their contents harvested for use in the application build.
The module also handles some Liquid template processing.

[NOTE]
Sourcerer is intended to be spun off as its own gem once it successfully proves the concept in this gem.
It will probably be called _AsciiSourcerer_ and may replace an older and unmaintained utility of mine called LiquiDoc.

It is invoked in the `releasehx.gemspec` file, establishing global namespaces: 

* `ReleaseHx::ATTRIBUTES[:globals]` (derived from this `README.adoc` file)
* `ReleaseHx.read_built_snippet(:<name>)` (such as `:helpscreen`)

The Sourcerer module also generates files like `build/docs/manpage.adoc`, which generates the formatted terminal manual, using content from `build/docs/config-reference.adoc` and this README (`tags="cli_options"`, for instance).

It also generates an AsciiDoc-formatted configuration reference and a sample config using the `config-def.yml` file.

This is mostly just showing off what Sourcerer can do, and hopefully setting into habit some best practices for my more complicated apps.

Sourcerer is also where integration with Jekyll's extensions of Liquid occurs, bringing ReleaseHx's templating powers closely in line with how Jekyll's work, as described in <<custom-liquid>>.

=== SchemaGraphy

This gem also introduces a module that derives from an unreleased gem I have been working on for some years: SchemaGraphy.

SchemaGraphy is basically an extension of YAML, enabling Ruby developers and end users more broadly to powerfully interpret and schematize YAML-based data.
Most relevant to our case, as enabled by the `SchemaGraphy` module in this gem, is its handling of *YAML custom tags* as well as what I am calling *"`templated fields`"*, where the value of a YAML node is a String that is intended to be further processed by a templating engine like Liquid or ERB, either immediatly upon ingest or later in the runtime stack, when it can be mixed with additional data.

SchemaGraphy facilitates handling these and other quirky power-ups we use with our fully valid YAML files, so low-code users can pass some dynamism along in their YAML configs and so forth.

==== Custom YAML Tag Handling

To enable end users to pass meta-instructions along with their data, wherever it will make sense to do so, SchemaGraphy offers a straightforward handling system.

Wherever you parse YAML-formatted data using `.load_yaml_with_tags`, custom-tagged Scalar nodes are converted into Maps like so:

.User's YAML
[source,yaml]
----
some_property: !liquid "{{ some_value }}"
----

.Converted data TagMap
[source,yaml]
----
some_property:
  __tag__: liquid
  value: "{{ some_value }}"
----

Developers may therefore conditionally interpret ingested data based on user-defined classifications, wherever the developer supports such things.

Whether a Scalar has been transformed into a TagMap, you can resolve it using:

[source,ruby]
----
SchemaGraphy::TagUtils.detag(some_property)
# Or, with a local alias
detag = ->(val) { SchemaGraphy::TagUtils.detag(val) }
detag(some_property)
----

When tags are used this way, to convey a syntax/engine for processing a template or other dynamic content, SchemaGraphy can even help us handle the content in the manner designated by the tag.
This will come up again in <<templated-fields,the next section>>.

[NOTE]
This capability is only available on Scalar node values.
For now, tags applied to other compound node types (Arrays/sequences, Maps/mappings) will be ignored by SchemaGraphy interpreters.

[WARNING]
When you use `load_yaml_with_tags`, you will encounter errors downstream if a user places a tag on a node where you do not expect it.

[[templated-fields]]
==== Templated Property Values in YAML

We are calling these "`templated fields`" to specify that we are talking about enabling end users to use Liquid, ERB, or eventually other templating syntaxes in YAML node values.

In so doing, developer are able to designate that the value of certain YAML nodes should be handled by a templating engine, as well as when and how.

We'll look at how this is done in <<templated-fields-handling>>.
For now, the point is that sometimes files like `config-def.yml` or an API-mapping file call for a little more runtime dynamism than a low-code solution like pure YAML can support.

Therefore, when the value of a user-configurable or environment-deterimined "`setting`" is a string that must be generated from data defined outside that field, we parse and render the template at runtime, using data from the environment or elsewhere.
For now, it is up to our calling code to provide the appropriate variables to the template depending on the context.

[[config-def]]
==== Configuration Definition (CFGYML)

All user-configurable settings have a definition, if not also a default value.
For single-sourcing purposes, these are defined in a YAML format called CFGYML -- a configuration-file modeling language.

The file is at `{gem_config_definition_path}`.
It is used to establish the literal default settings carried by the applicaton, and also to document those settings for the user.

This practice lets developers give end users extremely detailed configurations, always well documented.

The basic schema is somewhat straightforward.
Essentially, you're nesting Map objects within a YAML key `properties`, and each property (setting) of the defined config file can be described and constrained.

Each setting can have a type, description (`desc`), default (`dflt`), and templated-field instructions (`templating`).
If the setting is itself a of type `Map` (YAML "`mapping`", JSON "`object`"), its own nested parameters can be estalished with a `properties:` block.

For now, you can designate the type, which you will have to enforce in your code, as well as a default value.

[[sgyml-schemas]]
==== SGYML Schemas

Similar to but more complicated than CFGYML definition files are SchemaGraphy schema files.
This is a partially specified, partially developed, and as-yet-incomplete syntax for designating and constraining YAML documents.

ReleaseHx at this time makes active use of only minimal aspects of these schemas, all of whcih are contained in the `spects/` directory at the root of the gem source.

Each of the YAML formats used by ReleaseHx has its own schema in the repo.
The cfgyml-schema.yaml file will eventually be spun off, but the `rhyml-schema.yaml` and `rhyml-mapping-schema.yaml` files will stay here, defining valid formts for the types of files they apply to.

Since SchemaGraphy itself is still unreleased, CFGYML as introduced in this gem offers only a subset of what it will enable down the road.

Once SchemaGraphy is generally available, this gem will call it as a dependency.
At that point, a file like `config-def.yml` (CFGYML) will be able to impose a more detailed `$schema` for any property.

[[templated-fields-handling]]
==== Dynamic Templated-field Handling

The most powerful function of SchemaGraphy schemas that is now available in ReleaseHx is the ability to instruct how temlated fields should be processed at different stages, and also to parse and render them as needed.

Templated-field handling can be established between a combination of (1) CFGYML definition files or SGYML schema files and (2) configuration files to be applied at runtime.

Developers can designate a given property to be `type: Template` in a schema or definition.
This "`typing`" can be a trigger for downstream parsing/rendering of the template.

[NOTE]
Liquid uses these two stages.
The _parse_ operation copiles a template into a `Liquid::Template` object.
The _render_ operation applies a dataset to the loaded template, generating a String with Liquid tags resolved.

[[nyi]]
==== Not Yet Implemented

Most aspcts of SchemaGraphy/SGYML are not yet available in ReleaseHx, but some are worth pointing out.

data types::
As of now, the `type` node of any property in `config-def.yml` is not particularly functional.
I do have a whole table of "`data types`" in SGYML, most of which are extremely self-explanatory and drawn from fairly platonic, cross-language terms.
+
However, these are entirely unenforced in ReleaseHx -- for now, data still has to be type checked explicitly in the Ruby code, and user configs are not validated against any kind of typing system.

schema docs::
The schema files do not yet generate complete reference docs for the subject files that they govern.
So for instance, you'll have to read files like `rhyml-schema.yaml` and `rhyml-mapping-schema.yaml` directly to understand the format of RHYML files.
