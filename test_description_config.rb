#!/usr/bin/env ruby
# Test script to verify the description-note configuration works

require_relative 'lib/releasehx'

puts "Testing description-note configuration..."

config_path = '../releasehx-demo/configs/jira-description.yml'
json_path = '../releasehx-demo/issues/jira-description-note-1.1.0.json'
mapping_path = '../releasehx-demo/_mappings/description-note/jira.yaml'

begin
  # Load configuration
  config = ReleaseHx::ConfigLoader.load_file(config_path)
  puts "✓ Config loaded successfully"
  
  # Load mapping
  mapping = ReleaseHx::RHYML::MappingLoader.load(mapping_path)
  puts "✓ Mapping loaded successfully"
  
  # Load JSON data
  json_data = JSON.parse(File.read(json_path))
  puts "✓ JSON data loaded successfully"
  
  # Test adapter
  adapter = ReleaseHx::RHYML::Adapter.new(mapping: mapping, config: config)
  release = adapter.to_release(
    json_data, 
    release_code: '1.1.0',
    release_date: '2024-11-01',
    scan: true
  )
  
  puts "✓ Adapter processed successfully"
  puts "Release: #{release.code}"
  puts "Changes: #{release.changes.size}"
  
  # Test pattern extraction
  changes_with_notes = release.changes.select { |c| c.note && !c.note.strip.empty? }
  puts "Changes with notes: #{changes_with_notes.size}"
  
  if changes_with_notes.any?
    puts "\nFirst change with note:"
    change = changes_with_notes.first
    puts "  Tick: #{change.tick}"
    puts "  Summary: #{change.summ}"
    puts "  Note: #{change.note[0..100]}..."
  end
  
rescue => e
  puts "ERROR: #{e.class}: #{e.message}"
  puts e.backtrace.first(5).join("\n")
end
