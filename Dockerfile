FROM ruby:3.2-slim

ARG RELEASEHX_VERSION
LABEL org.opencontainers.image.version=$RELEASEHX_VERSION

# Install necessary build tools and dependencies (minimal footprint)
RUN apt-get update -qq && apt-get install -y --no-install-recommends \
  build-essential \
  libpq-dev \
  && rm -rf /var/lib/apt/lists/*

# Set app directory for build context
WORKDIR /app

# Copy local gem source and install it
COPY . .
RUN gem install rake
RUN rake prebuild && \
  gem build releasehx.gemspec && \
  gem install releasehx-*.gem && \
  rm -rf /app

# Set runtime working dir to isolated mount point
WORKDIR /workdir

# Default entrypoint and fallback command
ENTRYPOINT ["rhx"]
CMD ["--help"]
