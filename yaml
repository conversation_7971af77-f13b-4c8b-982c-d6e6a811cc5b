code: "1.1.0"
date: 2025-04-07
memo: |
  
changes:

  - chid: 
    type: improvement
    part: webuisumm: Add inline editing to dashboard widgets
    note: |
      Introduces an interactive editor directly in the dashboard, allowing users to modify widgets without navigating away.

This significantly streamlines common tasks.
    tags:
      - "featured"
      - "changelog"
      - "release_note_needed"
    lead: julie
  - chid: 
    type: improvement
    part: apisumm: Add rate limiting headers to public API
    note: |
      The API now includes standard `X-RateLimit-*` headers in all responses to better inform consumers of usage thresholds.

> **Note:** The default rate limit remains unchanged.
    tags:
      - "breaking"
      - "changelog"
      - "release_note_needed"
    lead: devin
  - chid: 
    type: bug
    part: webuisumm: Fix modal overflow on small screens
    note: |
      Modals now scroll correctly on devices with < 600px screen width.
    tags:
      - "changelog"
      - "release_note_needed"
    lead: sam
  - chid: 
    type: improvement
    part: apisumm: Deprecate v1 endpoints for user settings
    note: |
      The following v1 endpoints are now deprecated:

- `GET /api/v1/settings/user`
- `PUT /api/v1/settings/user`

Use `/api/v2/settings/profile` instead.
    tags:
      - "deprecation"
      - "release_note_needed"
    lead: amir