#!/usr/bin/env ruby
# frozen_string_literal: true

require 'yaml'
require 'liquid'
require_relative '../lib/liquid'
require_relative '../lib/releasehx/support/jekyll'

# Setup: plugin and template paths
plugin_dir  = File.expand_path('../lib/releasehx/jekyll_plugins', __dir__)
template_dir = File.expand_path('../_templates', __dir__)
fallback_dir = File.expand_path('../lib/templates', __dir__)

# Bootstrap Jekyll
site = ReleaseHx::JekyllBootstrapper.load_plugins(plugin_dirs: [plugin_dir])

# Sample input variables
vars = {
  'name' => 'Brian',
  'date' => '2025-04-25',
  'message' => "This **is** a sample\n\nmultiline message.",
  'slug' => 'Hello World!'
}

# Render template file
template_path = File.join(template_dir, 'test-sample.liquid')
abort("No such template: #{template_path}") unless File.exist?(template_path)
template_text = File.read(template_path)

puts "TEMPLATE TEXT:"
puts template_text.inspect
puts "======="

template = Liquid::Template.parse(template_text)
output = template.render(
  vars,
  registers: {
    site: site,
    file_system: ReleaseHx::Liquid::FileSystemWithFallback.new(template_dir, fallback_dir)
  }
)

puts "=== Rendered Output ==="
puts output
puts "======================="
