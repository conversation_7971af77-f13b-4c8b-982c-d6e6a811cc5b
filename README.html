<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Asciidoctor 2.0.23">
<meta name="description" content="CLI utility and Ruby API for generating structured release notes and changelog documents from various issue-tracking platforms or YAML definitions into plaintext drafts (<strong>AsciiDoc</strong>, <strong>Markdown</strong>, <strong>YAML</strong>) and rich-text output (<strong>HTML</strong> and <strong>PDF</strong>).">
<title>ReleaseHx</title>
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700">
<style>
/*! Asciidoctor default stylesheet | MIT License | https://asciidoctor.org */
/* Uncomment the following line when using as a custom stylesheet */
/* @import "https://fonts.googleapis.com/css?family=Open+Sans:300,300italic,400,400italic,600,600italic%7CNoto+Serif:400,400italic,700,700italic%7CDroid+Sans+Mono:400,700"; */
html{font-family:sans-serif;-webkit-text-size-adjust:100%}
a{background:none}
a:focus{outline:thin dotted}
a:active,a:hover{outline:0}
h1{font-size:2em;margin:.67em 0}
b,strong{font-weight:bold}
abbr{font-size:.9em}
abbr[title]{cursor:help;border-bottom:1px dotted #dddddf;text-decoration:none}
dfn{font-style:italic}
hr{height:0}
mark{background:#ff0;color:#000}
code,kbd,pre,samp{font-family:monospace;font-size:1em}
pre{white-space:pre-wrap}
q{quotes:"\201C" "\201D" "\2018" "\2019"}
small{font-size:80%}
sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}
sup{top:-.5em}
sub{bottom:-.25em}
img{border:0}
svg:not(:root){overflow:hidden}
figure{margin:0}
audio,video{display:inline-block}
audio:not([controls]){display:none;height:0}
fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}
legend{border:0;padding:0}
button,input,select,textarea{font-family:inherit;font-size:100%;margin:0}
button,input{line-height:normal}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
input[type=checkbox],input[type=radio]{padding:0}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
textarea{overflow:auto;vertical-align:top}
table{border-collapse:collapse;border-spacing:0}
*,::before,::after{box-sizing:border-box}
html,body{font-size:100%}
body{background:#fff;color:rgba(0,0,0,.8);padding:0;margin:0;font-family:"Noto Serif","DejaVu Serif",serif;line-height:1;position:relative;cursor:auto;-moz-tab-size:4;-o-tab-size:4;tab-size:4;word-wrap:anywhere;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}
a:hover{cursor:pointer}
img,object,embed{max-width:100%;height:auto}
object,embed{height:100%}
img{-ms-interpolation-mode:bicubic}
.left{float:left!important}
.right{float:right!important}
.text-left{text-align:left!important}
.text-right{text-align:right!important}
.text-center{text-align:center!important}
.text-justify{text-align:justify!important}
.hide{display:none}
img,object,svg{display:inline-block;vertical-align:middle}
textarea{height:auto;min-height:50px}
select{width:100%}
.subheader,.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{line-height:1.45;color:#7a2518;font-weight:400;margin-top:0;margin-bottom:.25em}
div,dl,dt,dd,ul,ol,li,h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6,pre,form,p,blockquote,th,td{margin:0;padding:0}
a{color:#2156a5;text-decoration:underline;line-height:inherit}
a:hover,a:focus{color:#1d4b8f}
a img{border:0}
p{line-height:1.6;margin-bottom:1.25em;text-rendering:optimizeLegibility}
p aside{font-size:.875em;line-height:1.35;font-style:italic}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{font-family:"Open Sans","DejaVu Sans",sans-serif;font-weight:300;font-style:normal;color:#ba3925;text-rendering:optimizeLegibility;margin-top:1em;margin-bottom:.5em;line-height:1.0125em}
h1 small,h2 small,h3 small,#toctitle small,.sidebarblock>.content>.title small,h4 small,h5 small,h6 small{font-size:60%;color:#e99b8f;line-height:0}
h1{font-size:2.125em}
h2{font-size:1.6875em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.375em}
h4,h5{font-size:1.125em}
h6{font-size:1em}
hr{border:solid #dddddf;border-width:1px 0 0;clear:both;margin:1.25em 0 1.1875em}
em,i{font-style:italic;line-height:inherit}
strong,b{font-weight:bold;line-height:inherit}
small{font-size:60%;line-height:inherit}
code{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;font-weight:400;color:rgba(0,0,0,.9)}
ul,ol,dl{line-height:1.6;margin-bottom:1.25em;list-style-position:outside;font-family:inherit}
ul,ol{margin-left:1.5em}
ul li ul,ul li ol{margin-left:1.25em;margin-bottom:0}
ul.circle{list-style-type:circle}
ul.disc{list-style-type:disc}
ul.square{list-style-type:square}
ul.circle ul:not([class]),ul.disc ul:not([class]),ul.square ul:not([class]){list-style:inherit}
ol li ul,ol li ol{margin-left:1.25em;margin-bottom:0}
dl dt{margin-bottom:.3125em;font-weight:bold}
dl dd{margin-bottom:1.25em}
blockquote{margin:0 0 1.25em;padding:.5625em 1.25em 0 1.1875em;border-left:1px solid #ddd}
blockquote,blockquote p{line-height:1.6;color:rgba(0,0,0,.85)}
@media screen and (min-width:768px){h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2}
h1{font-size:2.75em}
h2{font-size:2.3125em}
h3,#toctitle,.sidebarblock>.content>.title{font-size:1.6875em}
h4{font-size:1.4375em}}
table{background:#fff;margin-bottom:1.25em;border:1px solid #dedede;word-wrap:normal}
table thead,table tfoot{background:#f7f8f7}
table thead tr th,table thead tr td,table tfoot tr th,table tfoot tr td{padding:.5em .625em .625em;font-size:inherit;color:rgba(0,0,0,.8);text-align:left}
table tr th,table tr td{padding:.5625em .625em;font-size:inherit;color:rgba(0,0,0,.8)}
table tr.even,table tr.alt{background:#f8f8f7}
table thead tr th,table tfoot tr th,table tbody tr td,table tr td,table tfoot tr td{line-height:1.6}
h1,h2,h3,#toctitle,.sidebarblock>.content>.title,h4,h5,h6{line-height:1.2;word-spacing:-.05em}
h1 strong,h2 strong,h3 strong,#toctitle strong,.sidebarblock>.content>.title strong,h4 strong,h5 strong,h6 strong{font-weight:400}
.center{margin-left:auto;margin-right:auto}
.stretch{width:100%}
.clearfix::before,.clearfix::after,.float-group::before,.float-group::after{content:" ";display:table}
.clearfix::after,.float-group::after{clear:both}
:not(pre).nobreak{word-wrap:normal}
:not(pre).nowrap{white-space:nowrap}
:not(pre).pre-wrap{white-space:pre-wrap}
:not(pre):not([class^=L])>code{font-size:.9375em;font-style:normal!important;letter-spacing:0;padding:.1em .5ex;word-spacing:-.15em;background:#f7f7f8;border-radius:4px;line-height:1.45;text-rendering:optimizeSpeed}
pre{color:rgba(0,0,0,.9);font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;line-height:1.45;text-rendering:optimizeSpeed}
pre code,pre pre{color:inherit;font-size:inherit;line-height:inherit}
pre>code{display:block}
pre.nowrap,pre.nowrap pre{white-space:pre;word-wrap:normal}
em em{font-style:normal}
strong strong{font-weight:400}
.keyseq{color:rgba(51,51,51,.8)}
kbd{font-family:"Droid Sans Mono","DejaVu Sans Mono",monospace;display:inline-block;color:rgba(0,0,0,.8);font-size:.65em;line-height:1.45;background:#f7f7f7;border:1px solid #ccc;border-radius:3px;box-shadow:0 1px 0 rgba(0,0,0,.2),inset 0 0 0 .1em #fff;margin:0 .15em;padding:.2em .5em;vertical-align:middle;position:relative;top:-.1em;white-space:nowrap}
.keyseq kbd:first-child{margin-left:0}
.keyseq kbd:last-child{margin-right:0}
.menuseq,.menuref{color:#000}
.menuseq b:not(.caret),.menuref{font-weight:inherit}
.menuseq{word-spacing:-.02em}
.menuseq b.caret{font-size:1.25em;line-height:.8}
.menuseq i.caret{font-weight:bold;text-align:center;width:.45em}
b.button::before,b.button::after{position:relative;top:-1px;font-weight:400}
b.button::before{content:"[";padding:0 3px 0 2px}
b.button::after{content:"]";padding:0 2px 0 3px}
p a>code:hover{color:rgba(0,0,0,.9)}
#header,#content,#footnotes,#footer{width:100%;margin:0 auto;max-width:62.5em;*zoom:1;position:relative;padding-left:.9375em;padding-right:.9375em}
#header::before,#header::after,#content::before,#content::after,#footnotes::before,#footnotes::after,#footer::before,#footer::after{content:" ";display:table}
#header::after,#content::after,#footnotes::after,#footer::after{clear:both}
#content{margin-top:1.25em}
#content::before{content:none}
#header>h1:first-child{color:rgba(0,0,0,.85);margin-top:2.25rem;margin-bottom:0}
#header>h1:first-child+#toc{margin-top:8px;border-top:1px solid #dddddf}
#header>h1:only-child{border-bottom:1px solid #dddddf;padding-bottom:8px}
#header .details{border-bottom:1px solid #dddddf;line-height:1.45;padding-top:.25em;padding-bottom:.25em;padding-left:.25em;color:rgba(0,0,0,.6);display:flex;flex-flow:row wrap}
#header .details span:first-child{margin-left:-.125em}
#header .details span.email a{color:rgba(0,0,0,.85)}
#header .details br{display:none}
#header .details br+span::before{content:"\00a0\2013\00a0"}
#header .details br+span.author::before{content:"\00a0\22c5\00a0";color:rgba(0,0,0,.85)}
#header .details br+span#revremark::before{content:"\00a0|\00a0"}
#header #revnumber{text-transform:capitalize}
#header #revnumber::after{content:"\00a0"}
#content>h1:first-child:not([class]){color:rgba(0,0,0,.85);border-bottom:1px solid #dddddf;padding-bottom:8px;margin-top:0;padding-top:1rem;margin-bottom:1.25rem}
#toc{border-bottom:1px solid #e7e7e9;padding-bottom:.5em}
#toc>ul{margin-left:.125em}
#toc ul.sectlevel0>li>a{font-style:italic}
#toc ul.sectlevel0 ul.sectlevel1{margin:.5em 0}
#toc ul{font-family:"Open Sans","DejaVu Sans",sans-serif;list-style-type:none}
#toc li{line-height:1.3334;margin-top:.3334em}
#toc a{text-decoration:none}
#toc a:active{text-decoration:underline}
#toctitle{color:#7a2518;font-size:1.2em}
@media screen and (min-width:768px){#toctitle{font-size:1.375em}
body.toc2{padding-left:15em;padding-right:0}
body.toc2 #header>h1:nth-last-child(2){border-bottom:1px solid #dddddf;padding-bottom:8px}
#toc.toc2{margin-top:0!important;background:#f8f8f7;position:fixed;width:15em;left:0;top:0;border-right:1px solid #e7e7e9;border-top-width:0!important;border-bottom-width:0!important;z-index:1000;padding:1.25em 1em;height:100%;overflow:auto}
#toc.toc2 #toctitle{margin-top:0;margin-bottom:.8rem;font-size:1.2em}
#toc.toc2>ul{font-size:.9em;margin-bottom:0}
#toc.toc2 ul ul{margin-left:0;padding-left:1em}
#toc.toc2 ul.sectlevel0 ul.sectlevel1{padding-left:0;margin-top:.5em;margin-bottom:.5em}
body.toc2.toc-right{padding-left:0;padding-right:15em}
body.toc2.toc-right #toc.toc2{border-right-width:0;border-left:1px solid #e7e7e9;left:auto;right:0}}
@media screen and (min-width:1280px){body.toc2{padding-left:20em;padding-right:0}
#toc.toc2{width:20em}
#toc.toc2 #toctitle{font-size:1.375em}
#toc.toc2>ul{font-size:.95em}
#toc.toc2 ul ul{padding-left:1.25em}
body.toc2.toc-right{padding-left:0;padding-right:20em}}
#content #toc{border:1px solid #e0e0dc;margin-bottom:1.25em;padding:1.25em;background:#f8f8f7;border-radius:4px}
#content #toc>:first-child{margin-top:0}
#content #toc>:last-child{margin-bottom:0}
#footer{max-width:none;background:rgba(0,0,0,.8);padding:1.25em}
#footer-text{color:hsla(0,0%,100%,.8);line-height:1.44}
#content{margin-bottom:.625em}
.sect1{padding-bottom:.625em}
@media screen and (min-width:768px){#content{margin-bottom:1.25em}
.sect1{padding-bottom:1.25em}}
.sect1:last-child{padding-bottom:0}
.sect1+.sect1{border-top:1px solid #e7e7e9}
#content h1>a.anchor,h2>a.anchor,h3>a.anchor,#toctitle>a.anchor,.sidebarblock>.content>.title>a.anchor,h4>a.anchor,h5>a.anchor,h6>a.anchor{position:absolute;z-index:1001;width:1.5ex;margin-left:-1.5ex;display:block;text-decoration:none!important;visibility:hidden;text-align:center;font-weight:400}
#content h1>a.anchor::before,h2>a.anchor::before,h3>a.anchor::before,#toctitle>a.anchor::before,.sidebarblock>.content>.title>a.anchor::before,h4>a.anchor::before,h5>a.anchor::before,h6>a.anchor::before{content:"\00A7";font-size:.85em;display:block;padding-top:.1em}
#content h1:hover>a.anchor,#content h1>a.anchor:hover,h2:hover>a.anchor,h2>a.anchor:hover,h3:hover>a.anchor,#toctitle:hover>a.anchor,.sidebarblock>.content>.title:hover>a.anchor,h3>a.anchor:hover,#toctitle>a.anchor:hover,.sidebarblock>.content>.title>a.anchor:hover,h4:hover>a.anchor,h4>a.anchor:hover,h5:hover>a.anchor,h5>a.anchor:hover,h6:hover>a.anchor,h6>a.anchor:hover{visibility:visible}
#content h1>a.link,h2>a.link,h3>a.link,#toctitle>a.link,.sidebarblock>.content>.title>a.link,h4>a.link,h5>a.link,h6>a.link{color:#ba3925;text-decoration:none}
#content h1>a.link:hover,h2>a.link:hover,h3>a.link:hover,#toctitle>a.link:hover,.sidebarblock>.content>.title>a.link:hover,h4>a.link:hover,h5>a.link:hover,h6>a.link:hover{color:#a53221}
details,.audioblock,.imageblock,.literalblock,.listingblock,.stemblock,.videoblock{margin-bottom:1.25em}
details{margin-left:1.25rem}
details>summary{cursor:pointer;display:block;position:relative;line-height:1.6;margin-bottom:.625rem;outline:none;-webkit-tap-highlight-color:transparent}
details>summary::-webkit-details-marker{display:none}
details>summary::before{content:"";border:solid transparent;border-left:solid;border-width:.3em 0 .3em .5em;position:absolute;top:.5em;left:-1.25rem;transform:translateX(15%)}
details[open]>summary::before{border:solid transparent;border-top:solid;border-width:.5em .3em 0;transform:translateY(15%)}
details>summary::after{content:"";width:1.25rem;height:1em;position:absolute;top:.3em;left:-1.25rem}
.admonitionblock td.content>.title,.audioblock>.title,.exampleblock>.title,.imageblock>.title,.listingblock>.title,.literalblock>.title,.stemblock>.title,.openblock>.title,.paragraph>.title,.quoteblock>.title,table.tableblock>.title,.verseblock>.title,.videoblock>.title,.dlist>.title,.olist>.title,.ulist>.title,.qlist>.title,.hdlist>.title{text-rendering:optimizeLegibility;text-align:left;font-family:"Noto Serif","DejaVu Serif",serif;font-size:1rem;font-style:italic}
table.tableblock.fit-content>caption.title{white-space:nowrap;width:0}
.paragraph.lead>p,#preamble>.sectionbody>[class=paragraph]:first-of-type p{font-size:1.21875em;line-height:1.6;color:rgba(0,0,0,.85)}
.admonitionblock>table{border-collapse:separate;border:0;background:none;width:100%}
.admonitionblock>table td.icon{text-align:center;width:80px}
.admonitionblock>table td.icon img{max-width:none}
.admonitionblock>table td.icon .title{font-weight:bold;font-family:"Open Sans","DejaVu Sans",sans-serif;text-transform:uppercase}
.admonitionblock>table td.content{padding-left:1.125em;padding-right:1.25em;border-left:1px solid #dddddf;color:rgba(0,0,0,.6);word-wrap:anywhere}
.admonitionblock>table td.content>:last-child>:last-child{margin-bottom:0}
.exampleblock>.content{border:1px solid #e6e6e6;margin-bottom:1.25em;padding:1.25em;background:#fff;border-radius:4px}
.sidebarblock{border:1px solid #dbdbd6;margin-bottom:1.25em;padding:1.25em;background:#f3f3f2;border-radius:4px}
.sidebarblock>.content>.title{color:#7a2518;margin-top:0;text-align:center}
.exampleblock>.content>:first-child,.sidebarblock>.content>:first-child{margin-top:0}
.exampleblock>.content>:last-child,.exampleblock>.content>:last-child>:last-child,.exampleblock>.content .olist>ol>li:last-child>:last-child,.exampleblock>.content .ulist>ul>li:last-child>:last-child,.exampleblock>.content .qlist>ol>li:last-child>:last-child,.sidebarblock>.content>:last-child,.sidebarblock>.content>:last-child>:last-child,.sidebarblock>.content .olist>ol>li:last-child>:last-child,.sidebarblock>.content .ulist>ul>li:last-child>:last-child,.sidebarblock>.content .qlist>ol>li:last-child>:last-child{margin-bottom:0}
.literalblock pre,.listingblock>.content>pre{border-radius:4px;overflow-x:auto;padding:1em;font-size:.8125em}
@media screen and (min-width:768px){.literalblock pre,.listingblock>.content>pre{font-size:.90625em}}
@media screen and (min-width:1280px){.literalblock pre,.listingblock>.content>pre{font-size:1em}}
.literalblock pre,.listingblock>.content>pre:not(.highlight),.listingblock>.content>pre[class=highlight],.listingblock>.content>pre[class^="highlight "]{background:#f7f7f8}
.literalblock.output pre{color:#f7f7f8;background:rgba(0,0,0,.9)}
.listingblock>.content{position:relative}
.listingblock code[data-lang]::before{display:none;content:attr(data-lang);position:absolute;font-size:.75em;top:.425rem;right:.5rem;line-height:1;text-transform:uppercase;color:inherit;opacity:.5}
.listingblock:hover code[data-lang]::before{display:block}
.listingblock.terminal pre .command::before{content:attr(data-prompt);padding-right:.5em;color:inherit;opacity:.5}
.listingblock.terminal pre .command:not([data-prompt])::before{content:"$"}
.listingblock pre.highlightjs{padding:0}
.listingblock pre.highlightjs>code{padding:1em;border-radius:4px}
.listingblock pre.prettyprint{border-width:0}
.prettyprint{background:#f7f7f8}
pre.prettyprint .linenums{line-height:1.45;margin-left:2em}
pre.prettyprint li{background:none;list-style-type:inherit;padding-left:0}
pre.prettyprint li code[data-lang]::before{opacity:1}
pre.prettyprint li:not(:first-child) code[data-lang]::before{display:none}
table.linenotable{border-collapse:separate;border:0;margin-bottom:0;background:none}
table.linenotable td[class]{color:inherit;vertical-align:top;padding:0;line-height:inherit;white-space:normal}
table.linenotable td.code{padding-left:.75em}
table.linenotable td.linenos,pre.pygments .linenos{border-right:1px solid;opacity:.35;padding-right:.5em;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
pre.pygments span.linenos{display:inline-block;margin-right:.75em}
.quoteblock{margin:0 1em 1.25em 1.5em;display:table}
.quoteblock:not(.excerpt)>.title{margin-left:-1.5em;margin-bottom:.75em}
.quoteblock blockquote,.quoteblock p{color:rgba(0,0,0,.85);font-size:1.15rem;line-height:1.75;word-spacing:.1em;letter-spacing:0;font-style:italic;text-align:justify}
.quoteblock blockquote{margin:0;padding:0;border:0}
.quoteblock blockquote::before{content:"\201c";float:left;font-size:2.75em;font-weight:bold;line-height:.6em;margin-left:-.6em;color:#7a2518;text-shadow:0 1px 2px rgba(0,0,0,.1)}
.quoteblock blockquote>.paragraph:last-child p{margin-bottom:0}
.quoteblock .attribution{margin-top:.75em;margin-right:.5ex;text-align:right}
.verseblock{margin:0 1em 1.25em}
.verseblock pre{font-family:"Open Sans","DejaVu Sans",sans-serif;font-size:1.15rem;color:rgba(0,0,0,.85);font-weight:300;text-rendering:optimizeLegibility}
.verseblock pre strong{font-weight:400}
.verseblock .attribution{margin-top:1.25rem;margin-left:.5ex}
.quoteblock .attribution,.verseblock .attribution{font-size:.9375em;line-height:1.45;font-style:italic}
.quoteblock .attribution br,.verseblock .attribution br{display:none}
.quoteblock .attribution cite,.verseblock .attribution cite{display:block;letter-spacing:-.025em;color:rgba(0,0,0,.6)}
.quoteblock.abstract blockquote::before,.quoteblock.excerpt blockquote::before,.quoteblock .quoteblock blockquote::before{display:none}
.quoteblock.abstract blockquote,.quoteblock.abstract p,.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{line-height:1.6;word-spacing:0}
.quoteblock.abstract{margin:0 1em 1.25em;display:block}
.quoteblock.abstract>.title{margin:0 0 .375em;font-size:1.15em;text-align:center}
.quoteblock.excerpt>blockquote,.quoteblock .quoteblock{padding:0 0 .25em 1em;border-left:.25em solid #dddddf}
.quoteblock.excerpt,.quoteblock .quoteblock{margin-left:0}
.quoteblock.excerpt blockquote,.quoteblock.excerpt p,.quoteblock .quoteblock blockquote,.quoteblock .quoteblock p{color:inherit;font-size:1.0625rem}
.quoteblock.excerpt .attribution,.quoteblock .quoteblock .attribution{color:inherit;font-size:.85rem;text-align:left;margin-right:0}
p.tableblock:last-child{margin-bottom:0}
td.tableblock>.content{margin-bottom:1.25em;word-wrap:anywhere}
td.tableblock>.content>:last-child{margin-bottom:-1.25em}
table.tableblock,th.tableblock,td.tableblock{border:0 solid #dedede}
table.grid-all>*>tr>*{border-width:1px}
table.grid-cols>*>tr>*{border-width:0 1px}
table.grid-rows>*>tr>*{border-width:1px 0}
table.frame-all{border-width:1px}
table.frame-ends{border-width:1px 0}
table.frame-sides{border-width:0 1px}
table.frame-none>colgroup+*>:first-child>*,table.frame-sides>colgroup+*>:first-child>*{border-top-width:0}
table.frame-none>:last-child>:last-child>*,table.frame-sides>:last-child>:last-child>*{border-bottom-width:0}
table.frame-none>*>tr>:first-child,table.frame-ends>*>tr>:first-child{border-left-width:0}
table.frame-none>*>tr>:last-child,table.frame-ends>*>tr>:last-child{border-right-width:0}
table.stripes-all>*>tr,table.stripes-odd>*>tr:nth-of-type(odd),table.stripes-even>*>tr:nth-of-type(even),table.stripes-hover>*>tr:hover{background:#f8f8f7}
th.halign-left,td.halign-left{text-align:left}
th.halign-right,td.halign-right{text-align:right}
th.halign-center,td.halign-center{text-align:center}
th.valign-top,td.valign-top{vertical-align:top}
th.valign-bottom,td.valign-bottom{vertical-align:bottom}
th.valign-middle,td.valign-middle{vertical-align:middle}
table thead th,table tfoot th{font-weight:bold}
tbody tr th{background:#f7f8f7}
tbody tr th,tbody tr th p,tfoot tr th,tfoot tr th p{color:rgba(0,0,0,.8);font-weight:bold}
p.tableblock>code:only-child{background:none;padding:0}
p.tableblock{font-size:1em}
ol{margin-left:1.75em}
ul li ol{margin-left:1.5em}
dl dd{margin-left:1.125em}
dl dd:last-child,dl dd:last-child>:last-child{margin-bottom:0}
li p,ul dd,ol dd,.olist .olist,.ulist .ulist,.ulist .olist,.olist .ulist{margin-bottom:.625em}
ul.checklist,ul.none,ol.none,ul.no-bullet,ol.no-bullet,ol.unnumbered,ul.unstyled,ol.unstyled{list-style-type:none}
ul.no-bullet,ol.no-bullet,ol.unnumbered{margin-left:.625em}
ul.unstyled,ol.unstyled{margin-left:0}
li>p:empty:only-child::before{content:"";display:inline-block}
ul.checklist>li>p:first-child{margin-left:-1em}
ul.checklist>li>p:first-child>.fa-square-o:first-child,ul.checklist>li>p:first-child>.fa-check-square-o:first-child{width:1.25em;font-size:.8em;position:relative;bottom:.125em}
ul.checklist>li>p:first-child>input[type=checkbox]:first-child{margin-right:.25em}
ul.inline{display:flex;flex-flow:row wrap;list-style:none;margin:0 0 .625em -1.25em}
ul.inline>li{margin-left:1.25em}
.unstyled dl dt{font-weight:400;font-style:normal}
ol.arabic{list-style-type:decimal}
ol.decimal{list-style-type:decimal-leading-zero}
ol.loweralpha{list-style-type:lower-alpha}
ol.upperalpha{list-style-type:upper-alpha}
ol.lowerroman{list-style-type:lower-roman}
ol.upperroman{list-style-type:upper-roman}
ol.lowergreek{list-style-type:lower-greek}
.hdlist>table,.colist>table{border:0;background:none}
.hdlist>table>tbody>tr,.colist>table>tbody>tr{background:none}
td.hdlist1,td.hdlist2{vertical-align:top;padding:0 .625em}
td.hdlist1{font-weight:bold;padding-bottom:1.25em}
td.hdlist2{word-wrap:anywhere}
.literalblock+.colist,.listingblock+.colist{margin-top:-.5em}
.colist td:not([class]):first-child{padding:.4em .75em 0;line-height:1;vertical-align:top}
.colist td:not([class]):first-child img{max-width:none}
.colist td:not([class]):last-child{padding:.25em 0}
.thumb,.th{line-height:0;display:inline-block;border:4px solid #fff;box-shadow:0 0 0 1px #ddd}
.imageblock.left{margin:.25em .625em 1.25em 0}
.imageblock.right{margin:.25em 0 1.25em .625em}
.imageblock>.title{margin-bottom:0}
.imageblock.thumb,.imageblock.th{border-width:6px}
.imageblock.thumb>.title,.imageblock.th>.title{padding:0 .125em}
.image.left,.image.right{margin-top:.25em;margin-bottom:.25em;display:inline-block;line-height:0}
.image.left{margin-right:.625em}
.image.right{margin-left:.625em}
a.image{text-decoration:none;display:inline-block}
a.image object{pointer-events:none}
sup.footnote,sup.footnoteref{font-size:.875em;position:static;vertical-align:super}
sup.footnote a,sup.footnoteref a{text-decoration:none}
sup.footnote a:active,sup.footnoteref a:active,#footnotes .footnote a:first-of-type:active{text-decoration:underline}
#footnotes{padding-top:.75em;padding-bottom:.75em;margin-bottom:.625em}
#footnotes hr{width:20%;min-width:6.25em;margin:-.25em 0 .75em;border-width:1px 0 0}
#footnotes .footnote{padding:0 .375em 0 .225em;line-height:1.3334;font-size:.875em;margin-left:1.2em;margin-bottom:.2em}
#footnotes .footnote a:first-of-type{font-weight:bold;text-decoration:none;margin-left:-1.05em}
#footnotes .footnote:last-of-type{margin-bottom:0}
#content #footnotes{margin-top:-.625em;margin-bottom:0;padding:.75em 0}
div.unbreakable{page-break-inside:avoid}
.big{font-size:larger}
.small{font-size:smaller}
.underline{text-decoration:underline}
.overline{text-decoration:overline}
.line-through{text-decoration:line-through}
.aqua{color:#00bfbf}
.aqua-background{background:#00fafa}
.black{color:#000}
.black-background{background:#000}
.blue{color:#0000bf}
.blue-background{background:#0000fa}
.fuchsia{color:#bf00bf}
.fuchsia-background{background:#fa00fa}
.gray{color:#606060}
.gray-background{background:#7d7d7d}
.green{color:#006000}
.green-background{background:#007d00}
.lime{color:#00bf00}
.lime-background{background:#00fa00}
.maroon{color:#600000}
.maroon-background{background:#7d0000}
.navy{color:#000060}
.navy-background{background:#00007d}
.olive{color:#606000}
.olive-background{background:#7d7d00}
.purple{color:#600060}
.purple-background{background:#7d007d}
.red{color:#bf0000}
.red-background{background:#fa0000}
.silver{color:#909090}
.silver-background{background:#bcbcbc}
.teal{color:#006060}
.teal-background{background:#007d7d}
.white{color:#bfbfbf}
.white-background{background:#fafafa}
.yellow{color:#bfbf00}
.yellow-background{background:#fafa00}
span.icon>.fa{cursor:default}
a span.icon>.fa{cursor:inherit}
.admonitionblock td.icon [class^="fa icon-"]{font-size:2.5em;text-shadow:1px 1px 2px rgba(0,0,0,.5);cursor:default}
.admonitionblock td.icon .icon-note::before{content:"\f05a";color:#19407c}
.admonitionblock td.icon .icon-tip::before{content:"\f0eb";text-shadow:1px 1px 2px rgba(155,155,0,.8);color:#111}
.admonitionblock td.icon .icon-warning::before{content:"\f071";color:#bf6900}
.admonitionblock td.icon .icon-caution::before{content:"\f06d";color:#bf3400}
.admonitionblock td.icon .icon-important::before{content:"\f06a";color:#bf0000}
.conum[data-value]{display:inline-block;color:#fff!important;background:rgba(0,0,0,.8);border-radius:50%;text-align:center;font-size:.75em;width:1.67em;height:1.67em;line-height:1.67em;font-family:"Open Sans","DejaVu Sans",sans-serif;font-style:normal;font-weight:bold}
.conum[data-value] *{color:#fff!important}
.conum[data-value]+b{display:none}
.conum[data-value]::after{content:attr(data-value)}
pre .conum[data-value]{position:relative;top:-.125em}
b.conum *{color:inherit!important}
.conum:not([data-value]):empty{display:none}
dt,th.tableblock,td.content,div.footnote{text-rendering:optimizeLegibility}
h1,h2,p,td.content,span.alt,summary{letter-spacing:-.01em}
p strong,td.content strong,div.footnote strong{letter-spacing:-.005em}
p,blockquote,dt,td.content,td.hdlist1,span.alt,summary{font-size:1.0625rem}
p{margin-bottom:1.25rem}
.sidebarblock p,.sidebarblock dt,.sidebarblock td.content,p.tableblock{font-size:1em}
.exampleblock>.content{background:#fffef7;border-color:#e0e0dc;box-shadow:0 1px 4px #e0e0dc}
.print-only{display:none!important}
@page{margin:1.25cm .75cm}
@media print{*{box-shadow:none!important;text-shadow:none!important}
html{font-size:80%}
a{color:inherit!important;text-decoration:underline!important}
a.bare,a[href^="#"],a[href^="mailto:"]{text-decoration:none!important}
a[href^="http:"]:not(.bare)::after,a[href^="https:"]:not(.bare)::after{content:"(" attr(href) ")";display:inline-block;font-size:.875em;padding-left:.25em}
abbr[title]{border-bottom:1px dotted}
abbr[title]::after{content:" (" attr(title) ")"}
pre,blockquote,tr,img,object,svg{page-break-inside:avoid}
thead{display:table-header-group}
svg{max-width:100%}
p,blockquote,dt,td.content{font-size:1em;orphans:3;widows:3}
h2,h3,#toctitle,.sidebarblock>.content>.title{page-break-after:avoid}
#header,#content,#footnotes,#footer{max-width:none}
#toc,.sidebarblock,.exampleblock>.content{background:none!important}
#toc{border-bottom:1px solid #dddddf!important;padding-bottom:0!important}
body.book #header{text-align:center}
body.book #header>h1:first-child{border:0!important;margin:2.5em 0 1em}
body.book #header .details{border:0!important;display:block;padding:0!important}
body.book #header .details span:first-child{margin-left:0!important}
body.book #header .details br{display:block}
body.book #header .details br+span::before{content:none!important}
body.book #toc{border:0!important;text-align:left!important;padding:0!important;margin:0!important}
body.book #toc,body.book #preamble,body.book h1.sect0,body.book .sect1>h2{page-break-before:always}
.listingblock code[data-lang]::before{display:block}
#footer{padding:0 .9375em}
.hide-on-print{display:none!important}
.print-only{display:block!important}
.hide-for-print{display:none!important}
.show-for-print{display:inherit!important}}
@media amzn-kf8,print{#header>h1:first-child{margin-top:1.25rem}
.sect1{padding:0!important}
.sect1+.sect1{border:0}
#footer{background:none}
#footer-text{color:rgba(0,0,0,.6);font-size:.9em}}
@media amzn-kf8{#header,#content,#footnotes,#footer{padding:0}}
</style>
</head>
<body class="article">
<div id="header">
<h1>ReleaseHx</h1>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="paragraph">
<p>Generate formatted release histories from JIRA, GitHub, GitLab, YAML, or JSON sources.</p>
</div>
<div class="paragraph">
<p>CLI utility and Ruby API for generating structured release notes and changelog documents from various issue-tracking platforms or YAML definitions into plaintext drafts (<strong>AsciiDoc</strong>, <strong>Markdown</strong>, <strong>YAML</strong>) and rich-text output (<strong>HTML</strong> and <strong>PDF</strong>).</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This README serves as the both landing page and documentation for <strong>Version 0.1.0</strong>.
</td>
</tr>
</table>
</div>
<div id="toc" class="toc">
<div id="toctitle" class="title">Table of Contents</div>
<ul class="sectlevel1">
<li><a href="#_overview">Overview</a>
<ul class="sectlevel2">
<li><a href="#_features">Features</a></li>
</ul>
</li>
<li><a href="#_getting_started">Getting Started</a>
<ul class="sectlevel2">
<li><a href="#_non_ruby_users">Non-Ruby Users</a></li>
<li><a href="#_ruby_users">Ruby Users</a></li>
<li><a href="#_configuration_basics">Configuration Basics</a>
<ul class="sectlevel3">
<li><a href="#_demo_configs_and_data">Demo Configs and Data</a></li>
<li><a href="#_custom_configuration">Custom Configuration</a></li>
</ul>
</li>
<li><a href="#_recommended_strategy">Recommended Strategy</a></li>
<li><a href="#issue-sources">Establishing Issues Source</a>
<ul class="sectlevel3">
<li><a href="#jira-issues">JIRA Issues</a></li>
<li><a href="#github-issues">GitHub Issues</a></li>
<li><a href="#gitlab-issues">GitLab Issues</a></li>
<li><a href="#custom-api">Custom API</a></li>
<li><a href="#issues-commits">Issues Plus Git Commits</a></li>
<li><a href="#raw-git">Raw Git Commits</a></li>
<li><a href="#rhyml">RHYML</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#_usage">Usage</a>
<ul class="sectlevel2">
<li><a href="#_output_strategy">Output Strategy</a>
<ul class="sectlevel3">
<li><a href="#_output_configuration">Output Configuration</a></li>
<li><a href="#_output_templating">Output Templating</a></li>
</ul>
</li>
<li><a href="#_sourcing_strategy">Sourcing Strategy</a>
<ul class="sectlevel3">
<li><a href="#_task_tracking_source">Task-tracking Source</a></li>
<li><a href="#_local_flat_file_source">Local Flat-file Source</a></li>
</ul>
</li>
<li><a href="#rh-strategy">Recommended Strategy for Release Documentation</a>
<ul class="sectlevel3">
<li><a href="#_cicd_strategy_caveats">CI/CD Strategy Caveats</a></li>
</ul>
</li>
<li><a href="#rhx">The <code>rhx</code> Utility</a>
<ul class="sectlevel3">
<li><a href="#workflows">Potential Workflows</a></li>
<li><a href="#cli-options-ref">CLI Options Reference</a></li>
</ul>
</li>
<li><a href="#advanced-rhyml">Advanced RHYML</a></li>
<li><a href="#config-ref">Configuration Reference</a></li>
<li><a href="#sample-config">Sample Configurations</a>
<ul class="sectlevel3">
<li><a href="#custom-api-config">Custom API Configuration</a></li>
</ul>
</li>
<li><a href="#_rhyml_schema">RHYML Schema</a></li>
<li><a href="#_templating_guide">Templating Guide</a>
<ul class="sectlevel3">
<li><a href="#_custom_liquid_tags">Custom Liquid Tags</a></li>
<li><a href="#_custom_liquid_filters">Custom Liquid Filters</a></li>
</ul>
</li>
<li><a href="#releasehx-api">ReleaseHx API</a>
<ul class="sectlevel3">
<li><a href="#_classes">Classes</a></li>
</ul>
</li>
</ul>
</li>
<li><a href="#_development">Development</a>
<ul class="sectlevel2">
<li><a href="#_background">Background</a></li>
<li><a href="#ddd">Docs-driven Development</a></li>
<li><a href="#_issue_data_mapping">Issue-data Mapping</a></li>
<li><a href="#_codebase_structure">Codebase Structure</a></li>
<li><a href="#sourcerer">Sourcerer</a></li>
<li><a href="#_schemagraphy">SchemaGraphy</a>
<ul class="sectlevel3">
<li><a href="#_custom_yaml_tag_handling">Custom YAML Tag Handling</a></li>
<li><a href="#templated-fields">Templated Property Values in YAML</a></li>
<li><a href="#config-def">Configuration Definition (CFGYML)</a></li>
<li><a href="#sgyml-schemas">SGYML Schemas</a></li>
<li><a href="#templated-fields-handling">Dynamic Templated-field Handling</a></li>
<li><a href="#nyi">Not Yet Implemented</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_overview">Overview</h2>
<div class="sectionbody">
<div class="paragraph">
<p>Invoke simple commands with powerful results!</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p><strong>Check for issues with missing release notes</strong>.
Includes any issues marked <code>release_note_needed</code> or similar.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 2.1.0 --check
&gt; Missing required release notes:
&gt; - JIRA-3412: Fix the thing (JoePSmith)
&gt; - JIRA-3482: Add this cool thing (RaeMDoe)</pre>
</div>
</div>
</li>
<li>
<p>While you are waiting for Joe and Rae to get their outstanding notes entered into JIRA&#8230;&#8203; <strong>retrieve JIRA issues</strong> for version 2.1.0.
rhx 2.1.0 --yaml</p>
</li>
</ol>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
You can edit your Release Notes in Markdown or AsciiDoc files, or in YAML files with AsciiDoc or Markdown text formatting.
Whatever you prefer.
</td>
</tr>
</table>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>When Joe and Rae finally post their notes&#8230;&#8203; <strong>amend the YAML doc</strong>.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 2.1.0 --append</pre>
</div>
</div>
</li>
<li>
<p><strong>Generate a Markdown variant</strong> to your website source directory.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 2.1.0 --md</pre>
</div>
</div>
</li>
<li>
<p><strong>Generate a PDF</strong> to a documents archive.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 2.1.0 --pdf</pre>
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>Numerous backends enable conversions from any source format to HTML or PDF output.</p>
</div>
<div class="paragraph">
<p>ReleaseHx can convert data into content through various processes, which you can adapt to your preferred workflow.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>API  → Markdown → HTML/PDF
API  → AsciiDoc → HTML/PDF
API  → YAML     → HTML/PDF
API  → YAML     → AsciiDoc → HTML/PDF
API  → YAML     → Markdown → HTML/PDF
YAML → AsciiDoc → HTML/PDF
YAML → Markdown → HTML/PDF
API  → HTML/PDF
YAML → HTML/PDF</pre>
</div>
</div>
<div class="paragraph">
<p>OR, use your Markdown/AsciiDoc-based <strong>static-site generator (SSG)</strong> to perform the final rendering to HTML!
Save or move drafts to your SSG&#8217;s source path, commit, and publish.</p>
</div>
<div class="sect2">
<h3 id="_features">Features</h3>
<div class="paragraph">
<p>ReleaseHx is already packed with capabilities.</p>
</div>
<div class="ulist">
<ul>
<li>
<p>Generate <strong class="key">Release Notes</strong> and/or <strong class="key">Changelogs</strong> from your Issues tracker or Git.</p>
</li>
<li>
<p>Source in <strong class="buzz">JIRA</strong>, <strong class="buzz">GitHub</strong>, <strong class="buzz">GitLab</strong>, <strong class="key">Git commits</strong>, or our special YAML syntax: <strong>RHYML</strong>.</p>
</li>
<li>
<p>Readily configurable to use <strong>your Issue Management API</strong>.</p>
</li>
<li>
<p>Draft in <strong class="key">Markdown</strong>, <strong class="key">AsciiDoc</strong>, or <strong class="key">YAML</strong>.</p>
</li>
<li>
<p>Render to <strong class="key">HTML</strong> or <strong class="key">PDF</strong> with <strong>Asciidoctor</strong>, <strong>Pandoc</strong>, and other converters.</p>
</li>
<li>
<p>Customize output with <strong class="buzz">Liquid</strong> templates.</p>
</li>
<li>
<p>Ensure qualifying issues all have release notes.</p>
</li>
<li>
<p><strong>Link-back</strong> from notes or Changelog entries to issues and Git commits.</p>
</li>
<li>
<p>Use <strong class="key">Git</strong> to track changes in your release history documents.</p>
</li>
<li>
<p>Use <strong class="buzz">Docker</strong> to run ReleaseHx in any environment.</p>
</li>
<li>
<p>Auto-update drafts with late-breaking release notes (RHYML only).</p>
</li>
<li>
<p>Group and sort entries by <strong>issue type</strong>, subject <strong>component</strong>, <strong>contributor</strong>, or various <strong>tags</strong>.</p>
</li>
<li>
<p>Invoke labels/tags such as <strong>breaking</strong>, <strong>deprecation</strong>, <strong>experimental</strong>, <strong>highlight</strong>, and <strong>internal</strong>.</p>
</li>
<li>
<p>Use Issue labels or <strong>checkboxes</strong> to indicate Changelog inclusion or Release Note requirement.</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>You can begin editing in YAML and update the file without losing changes, then generate a final draft in Markdown or AsciiDoc.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --yml
rhx 2.1.0 --append
rhx 2.1.0 --adoc</pre>
</div>
</div>
<div class="paragraph">
<p>The second line adds any late-arriving issues from the cloud.
The third line uses that <code>.yml</code> file to generate a <code>.adoc</code> file.</p>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_getting_started">Getting Started</h2>
<div class="sectionbody">
<div class="paragraph">
<p>To use ReleaseHx in your own projects, you will need either Ruby or Docker.
Use either the <code>rhx</code> CLI utility or the ReleaseHx Ruby API.</p>
</div>
<div class="paragraph">
<p>The only other rerequisite is possibly Git, if you are drawing any content/metadata from Git commits.</p>
</div>
<div class="sect2">
<h3 id="_non_ruby_users">Non-Ruby Users</h3>
<div class="paragraph">
<p>If you are not already a Ruby user, the <code>rhx</code> utility may be best used from our Docker image.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
You will need <strong>Docker Desktop</strong> installed <a href="https://docs.docker.com/desktop/setup/install/mac-install/">directly on MacOS</a> or <a href="https://docs.docker.com/desktop/features/wsl/">with WSL2 backend on Windows</a>.
For Linux, use the <a href="https://docs.docker.com/engine/install/">Docker Engine install docs</a> if you&#8217;re not already using Docker.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>With Docker installed and running&#8230;&#8203;</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Pull the Docker image.</p>
<div class="literalblock prompt">
<div class="content">
<pre>docker pull docopslab/releasehx</pre>
</div>
</div>
</li>
<li>
<p>Run the <code>rhx</code> command.</p>
<div class="literalblock prompt">
<div class="content">
<pre>docker run -it --rm -v $(pwd):/workdir docopslab/releasehx rhx 2.1.0 --md</pre>
</div>
</div>
</li>
<li>
<p>Optionally alias the base Docker command.</p>
<div class="literalblock prompt">
<div class="content">
<pre>alias rhx='docker run -it --rm -v $(pwd):/workdir docopslab/releasehx rhx'</pre>
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>In this example, the <code>$(pwd)</code> part of the command mounts the current directory to the Docker container, so that the <code>rhx</code> command can read and write files in your project directory.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
Try the <a href="#demo-configs-and-data">demo configs and data</a> to get a feel for how ReleaseHx works.
</td>
</tr>
</table>
</div>
</div>
<div class="sect2">
<h3 id="_ruby_users">Ruby Users</h3>
<div class="paragraph">
<p>ReleaseHx can be installed as a Ruby gem, for either CLI or API usage.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Option 1: System-wide installation</dt>
</dl>
</div>
<div class="literalblock prompt">
<div class="content">
<pre>gem install releasehx</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Option 2: Project installation</dt>
<dd>
<div class="openblock">
<div class="content">
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Add the following line to your <code>Gemfile</code>:</p>
<div class="listingblock">
<div class="content">
<pre class="highlight"><code class="language-ruby" data-lang="ruby">gem 'releasehx', '~&gt; 0.1.0'</code></pre>
</div>
</div>
</li>
<li>
<p>Install the gem.</p>
<div class="literalblock prompt">
<div class="content">
<pre>bundle install</pre>
</div>
</div>
</li>
<li>
<p>Use <code>bundle exec rhx</code> to execute.</p>
</li>
</ol>
</div>
</div>
</div>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="_configuration_basics">Configuration Basics</h3>
<div class="paragraph">
<p>Once installed, you will need to configure ReleaseHx to connect to your issue-management service (IMS) and to define how you want to organize your release history output.</p>
</div>
<div class="paragraph">
<p>By convention, ReleaseHx is customized according to a file stored at the root of your project called <code>releasehx-config.yml</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
To use an alternate config file, provide a path at runtime using <code>--config PATH/TO/FILE</code>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>If you are ready to set up your remote issue management system&#8217;s API, skip to <a href="#issue-sources">Establishing Issues Source</a>.</p>
</div>
<div class="sect3">
<h4 id="_demo_configs_and_data">Demo Configs and Data</h4>
<div class="paragraph">
<p>If you want to play around with ReleaseHx before connecting it to your own Issues via API, use the <code><a href="releasehx_demo_repo">DocOps/releasehx-demo</a></code> repository as instructed in this section.</p>
</div>
<div class="paragraph">
<p>Alternately, skip to <a href="#issue-sources">Establishing Issues Source</a> to begin configuring your own project/environment.</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Clone the demo repository.</p>
<div class="literalblock prompt">
<div class="content">
<pre><NAME_EMAIL>:DocOps/releasehx-demo.git</pre>
</div>
</div>
</li>
<li>
<p>Run the <code>rhx</code> command with the <code>--config</code> option. For example:</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 1.1.0 --config releasehx-demo/configs/basic.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --md demo-1.1.0.md</pre>
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>In this example, the <code>rest-1.1.0.json</code> file fills in for the version 1.1.0 issue tickets in a JIRA project, drafting them as a local Markdown file.</p>
</div>
<div class="paragraph">
<p>The demo repo contains several JSON files that simulate the data returned by the JIRA, GitHub, and GitLab APIs.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 1. Demo data versions in <code>issues/</code> directory</caption>
<colgroup>
<col style="width: 57.1428%;">
<col style="width: 28.5714%;">
<col style="width: 14.2858%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">File path</th>
<th class="tableblock halign-left valign-top">Note field</th>
<th class="tableblock halign-left valign-top">Tags source</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jira-customfield-note-1.1.0.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">custom field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">labels</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jira-description-note-1.1.0.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">description field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">labels</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>github-checkbox-tags-1.1.0.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">description field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">checkboxes</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>gitlab-checkbox-tags-1.1.0.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">description field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">checkboxes</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic-1.1.1.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">description field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">labels</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic-1.1.2.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">description field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">labels</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>generic-1.2.0.json</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">description field</p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">labels</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>The demo repository also comes with a number of configuration arrangements, which you may switch to or freely edit.</p>
</div>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 2. Demo Configurations in <code>configs/</code></caption>
<colgroup>
<col style="width: 40%;">
<col style="width: 60%;">
</colgroup>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jira-customfield.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Release Notes and Changelog, default sort</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jira-customfield-resorted.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Same content as previous, differently sorted</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jira-customfield-changelog.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Changelog only</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>jira-customfield-heavy.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Lots of modifications just to show off</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>github-basic.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Release Notes and Changelog, default sort</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>gitlab-basic.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Release Notes and Changelog, default sort</p></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><p class="tableblock"><code>kitchen-sink.yml</code></p></td>
<td class="tableblock halign-left valign-top"><p class="tableblock">Fully loaded w/ default settings &amp; comments</p></td>
</tr>
</tbody>
</table>
<div class="paragraph">
<p>Try the following series of commands and steps to see ReleaseHx in action.</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Check for issues missing release notes.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --check</pre>
</div>
</div>
<div class="paragraph">
<p>Out of the box, there are two issues in the <code>jira-customfield-note-1.1.0.json</code> file that are marked <code>release_note_needed</code> yet have no release note filled out.</p>
</div>
</li>
<li>
<p>Generate a YAML draft.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --yaml</pre>
</div>
</div>
</li>
<li>
<p>Edit the YAML draft in any way, then save.</p>
</li>
<li>
<p>Add release notes to the two issues that were missing them in <code>releasehx-demo/issues/rest-1.1.0.json</code>.</p>
</li>
<li>
<p>Update the YAML draft with the new issues.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --append</pre>
</div>
</div>
</li>
<li>
<p>Generate a Markdown draft.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --md</pre>
</div>
</div>
</li>
<li>
<p>Edit the Markdown draft in any way.</p>
</li>
<li>
<p>Generate HTML and PDF output.</p>
<div class="literalblock prompt">
<div class="content">
<pre>rhx 1.1.0 --config releasehx-demo/configs/jira-customfield.yml --json releasehx-demo/issues/jira-customfield-note-1.1.0.json --html --pdf</pre>
</div>
</div>
</li>
</ol>
</div>
</div>
<div class="sect3">
<h4 id="_custom_configuration">Custom Configuration</h4>
<div class="paragraph">
<p>If any of these demo configs seems like the right arrangement for you, copy it to your project directory and modify it to further suit your needs.</p>
</div>
<div class="literalblock prompt">
<div class="title">Example</div>
<div class="content">
<pre>cp releasehx-demo/basic-config.yml releasehx-config.yml</pre>
</div>
</div>
<div class="paragraph">
<p>See the <a href="#config-ref">Configuration Reference</a> for an annotated list of available settings.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_recommended_strategy">Recommended Strategy</h3>
<div class="paragraph">
<p>If you came here without a strong opinion about how to approach managing and publishing a Release History, read our <a href="#rh-strategy">Recommended Strategy for Release Documentation</a>.</p>
</div>
<div class="paragraph">
<p>If that&#8217;s <strong>tl;dr</strong>, here are some key points:</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Recommended output:</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Publish a unified document called something like &#8220;Release History&#8221;, with entries for each sequential release of your product.</p>
</li>
<li>
<p>Publish a &#8220;Changelog&#8221; or &#8220;Change Log&#8221; containing summaries for all user-facing product changes.</p>
</li>
<li>
<p>Publish a &#8220;Release Notes&#8221; section containing an entry for any user-facing product change that requires <em>explanation</em> or deserves special <em>highlighting</em>.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Manage issues like so:</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Use the <strong><em>summary/title field</em></strong> of your issue-management system as the draft Changelog entry.</p>
</li>
<li>
<p>Use a <strong><em>custom field</em></strong> (JIRA) or the <strong>issue body</strong> to designate and draft a release note.</p>
</li>
<li>
<p>Use labels in your issue-management system to &#8220;tag&#8221; issues as <strong><em>belonging in the Changelog</em></strong> or <strong><em>needing a release note</em></strong> draft.</p>
</li>
<li>
<p>Use <code>rhx &lt;v.r.sn&gt; --check</code> to ensure all issues with a release note requirement have a release note.</p>
</li>
</ul>
</div>
</dd>
<dt class="hdlist1">Use these procedures for each version:</dt>
<dd>
<div class="ulist">
<ul>
<li>
<p>Generate and edit a <strong><em>YAML draft</em></strong> of your Release History until all release notes are drafted.</p>
</li>
<li>
<p>Generate a <strong><em>final draft</em></strong> in the format you use for the rest of yur docs.</p>
</li>
<li>
<p><strong><em>Publish</em></strong> your Release History to your website or other distribution channels.</p>
</li>
</ul>
</div>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="issue-sources">Establishing Issues Source</h3>
<div class="paragraph">
<p>ReleaseHx is source agnostic.
Your release history can start in any of the following sources:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><a href="#jira-issues">JIRA Issues</a></p>
</li>
<li>
<p><a href="#github-issues">GitHub Issues</a></p>
</li>
<li>
<p><a href="#gitlab-issues">GitLab Issues</a></p>
</li>
<li>
<p><a href="#custom-api">Any issue-tracking system with a REST API</a></p>
</li>
<li>
<p><a href="#issues-commits">Any combination of Issues and Git commits</a></p>
</li>
<li>
<p><a href="#raw-git">Git alone</a></p>
</li>
<li>
<p><a href="#rhyml">A YAML document (&#8220;RHYML&#8221;)</a></p>
</li>
</ul>
</div>
<div class="paragraph">
<p>For each instance of RelaseHX, you will need to configure one source or combination of sources from which to derive Changelog and Release Note content and metadata.</p>
</div>
<div class="paragraph">
<p>The three cloud services (JIRA, GitHub, GitLab) are configured similarly.</p>
</div>
<div class="listingblock">
<div class="title">Example API configuration in <code>./releasehx-config.yml</code></div>
<div class="content">
<pre class="highlight"><code class="language-yaml" data-lang="yaml">api:
  from: jira
  href: https://jira.example.com/rest/api/2</code></pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
If you wish to configure an API other than Jira, GitHub, or GitLab, see <a href="#custom-api">Custom API</a>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>You can store your API username and token/secret key locally, either as a shell environment variable or in text file not tracked by Git.</p>
</div>
<div class="literalblock prompt">
<div class="title">Example ENV variable storage</div>
<div class="content">
<pre>export RELEASEHX_API_KEY=mysecrettoken
export RELEASEHX_API_USER=<EMAIL></pre>
</div>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
<div class="paragraph">
<p>To make an environment variable permanent, write it to your shell profile file, then source it.</p>
</div>
<div class="listingblock">
<div class="title">Example export to Zsh config</div>
<div class="content">
<pre class="highlight"><code class="language-shell" data-lang="shell">echo "export RELEASEHX_API_KEY=mysecrettoken" &gt;&gt; ~/.zshrc
source ~/.zshrc</code></pre>
</div>
</div>
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>If you prefer not to mess around with your shell profile or environment variables, you can store your API credentials in a file.</p>
</div>
<div class="literalblock">
<div class="title">Example file storage in <code>RELEASEHX_API_CRED</code> file</div>
<div class="content">
<pre>mysecretpasswordhashjumble
<EMAIL></pre>
</div>
</div>
<div class="paragraph">
<p>The first line of the file is the token/key, and the second line is a username, if required.</p>
</div>
<div class="paragraph">
<p>If you use such a file, be sure to keep it out of Git tracking.</p>
</div>
<div class="literalblock">
<div class="title">Example <code>.gitignore</code> entry</div>
<div class="content">
<pre>RELEASEHX_API_CRED</pre>
</div>
</div>
<div class="paragraph">
<p>Note that the <em>names</em> of these files and environment variables can be modified, in case you already have these API credentials stored that way.
See the sub-properties of <strong class="ppty"><a href="#conf_ppty_api_auth">config.api.auth</a></strong>.</p>
</div>
<div class="sect3">
<h4 id="jira-issues">JIRA Issues</h4>
<div class="paragraph">
<p>ReleaseHx can connect to the JIRA REST API to fetch issues that match the release version.</p>
</div>
<div class="paragraph">
<p>The Issue <strong>summary</strong> field is used to draft the Changelog/title, and a custom field called <code>release_note</code> is used for the Release note.</p>
</div>
<div class="paragraph">
<p>In JIRA, what ReleaseHx calls &#8220;tags&#8221; can be assigned using either JIRA labels or checkbox custom fields.</p>
</div>
</div>
<div class="sect3">
<h4 id="github-issues">GitHub Issues</h4>
<div class="paragraph">
<p>ReleaseHx can connect to the GitHub Issues API to fetch issues from the release version.</p>
</div>
<div class="paragraph">
<p>The issue <strong>title</strong> field is used to draft the Changelog/title, and any text in the body that follows text like <code># Release Note</code> is used for the Release note.</p>
</div>
<div class="paragraph">
<p>In GitHub, what ReleaseHx calls &#8220;tags&#8221; can be assigned using labels or checkboxes embedded after the Release Note text.</p>
</div>
</div>
<div class="sect3">
<h4 id="gitlab-issues">GitLab Issues</h4>
<div class="paragraph">
<p>ReleaseHx can connect to the GitLab Issues API to fetch issues from the release version.</p>
</div>
<div class="paragraph">
<p>The issue <strong>title</strong> field is used to draft the Changelog/title, and any text in the body that follows text like <code># Release Note</code> is used for the Release note.</p>
</div>
<div class="paragraph">
<p>In GitLab, what ReleaseHx calls &#8220;tags&#8221; can be assigned using labels or checkboxes embedded after the Release Note text.</p>
</div>
</div>
<div class="sect3">
<h4 id="custom-api">Custom API</h4>
<div class="paragraph">
<p>ReleaseHx is extensible.
While it officially supports JIRA, GitHub, and GitLab, you can configure it to connect to any issue-tracking system with a REST API.</p>
</div>
<div class="paragraph">
<p>Use the application config file to designate API endpoint and any required authentication.
Then provide a mapping file at <code>_mappings/&lt;api_from_name&gt;.yml</code> (or the path configured at <code>path.issue_mappings_dir</code>) to define the data conversion to RHYML.</p>
</div>
<div class="paragraph">
<p>See <a href="#custom-api-config">Custom API Configuration</a> for details.</p>
</div>
</div>
<div class="sect3">
<h4 id="issues-commits">Issues Plus Git Commits</h4>
<div class="paragraph">
<p>Assuming your JIRA instance is integrated with your Git host (which is automatically the case if you use GitLab or GitHub Issues) and you are working in the related repository, you can connect issues to Git commits.</p>
</div>
<div class="paragraph">
<p>When sourcing Release Note content from Git commits, you will need a few config settings.</p>
</div>
<div class="listingblock">
<div class="title">Example <code>./releasehx-config.yml</code> properties</div>
<div class="content">
<pre class="highlight"><code class="language-yaml" data-lang="yaml">sources:
  note: commit_message
  pattern: '^RELEASE NOTE:'
  summary: commit_message</code></pre>
</div>
</div>
<div class="paragraph">
<p>If <code>note</code> is set to <code>commit_message</code>, ReleaseHx will extract the content from everything following the <code>pattern</code> text in the commit message referenced by the commit SHA/hash in the Issue metadata.
To use the entire &#8220;body&#8221; of the commit message as the note, set the <code>pattern</code> to <code>^</code>.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
ReleaseHx expects commit messages to be formatted with the summary/header on the first line, followed by a blank line, then the body.
Note content is expected to be the entire body or embedded at the end of it, delineated by the value of <strong class="ppty"><a href="#conf_ppty_sources_note_pattern">config.sources.note_pattern</a></strong>.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>If you are not using content from the Git commit as your Changelog/Release Notes entry material, the relationship is just for metadata.
Release notes and Changelog entries can reference and/or link to the Git commit when so configured.
To enable this, fill out the <strong class="ppty"><a href="#conf_ppty_links_web">config.links.web</a></strong> and <strong class="ppty"><a href="#conf_ppty_links_git">config.links.git</a></strong> properties in your config file.</p>
</div>
<div class="paragraph">
<p>During drafting, ReleaseHx will strip from the <code>note</code> property any lines that begin with <code>#</code>.</p>
</div>
</div>
<div class="sect3">
<h4 id="raw-git">Raw Git Commits</h4>
<div class="paragraph">
<p>You can source a Release History entirely inside Git commits, with largely the same capacity, as long as you are willing to format your commits precisely.</p>
</div>
<div class="paragraph">
<p>See the explanation in <a href="#issues-commits">Issues Plus Git Commits</a> for designating Release Note content in a commit-message &#8220;body&#8221;, but note you may also replace the labels/tags aspect of issue-management systms, as follows.</p>
</div>
<div class="paragraph">
<p>Use <code>#tagname</code> or <code>#tagkind:taglabel</code> strings to assign tags.
ReleaseHx can be configured to detect such tags anywhere in the commit message &#8220;body&#8221;, but will strip from the <code>note</code> property content any lines that begin with <code>#</code>.</p>
</div>
<div class="paragraph">
<p>During drafting, ReleaseHx will drop from the final note any line that begins with a <code>#</code> character from the content, including a line that might be <code>#changelog</code>.</p>
</div>
<div class="paragraph">
<p>The limitations of working this way should be obvious.
It is difficult and typically unwise to edit commits after they have been merged, so tags and such would need to be added to the merge commit itself, on schedule.</p>
</div>
<div class="literalblock">
<div class="title">Example Git commit string ReleaseHx can parse</div>
<div class="content">
<pre>commit a3b14d8e9c2f5e7b0d1a2c3d4e5f6a7b8c9d0e1
Author: John Smith &lt;<EMAIL>&gt;
Date:   Thu Mar 21 15:32:14 2024 -0400

Add new user authentication flow

Introduces a complete overhaul of the user authentication system.

RELEASE NOTE:
The login process now supports multi-factor authentication and single sign-on options. Users will need to re-authenticate on their next login to set up these new security features.

#breaking #highlight #type:improvement #component:auth #component:ui</pre>
</div>
</div>
<div class="paragraph">
<p>This method requires more discipline on the part of developers, but it is arguably the most streamlined option, especially for working without an issue-tracking system.</p>
</div>
</div>
<div class="sect3">
<h4 id="rhyml">RHYML</h4>
<div class="paragraph">
<p>Use a locally stored YAML file as the source of your Release History and Changelog content.
This is essentially the same document that is drafted from a REST API source by the <code>--yaml</code> flag for the <code>rhx</code> command.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
RHYML is referred to as an API source throughout most ReleaseHx documentation, even though it is not a remote/HTTP API.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>All issues associated with a given release are nested together under a <code>work:</code> property without further hierarchy.
Since the entries are <em>data</em>, they can be oraganized here however you wish, then re-sorted upon generating a Markdown or AsciiDoc draft, or going directly to PDF or HTML.</p>
</div>
<div class="paragraph">
<p>The advantage of this method is working without an API.
Contributors can simply add their notes to a unified file via Git, and that file can be edited in place.</p>
</div>
<div class="paragraph">
<p>The RHYML content can be connected to Issues and Git commits, but only tangentially.
The summary and note content needs to be listed in the YAML document.
Links back to the source issue or commit are possible but optional.</p>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_usage">Usage</h2>
<div class="sectionbody">
<div class="paragraph">
<p>ReleaseHx can either be used as a commandline tool or as a Ruby library.
In both cases it can be powerfully configured with regard to its source matter and its output formats.</p>
</div>
<div class="paragraph">
<p>We will first look at what ReleaseHx can <em>do</em>, then we we will explore <em>how</em> to make ReleaseHx achieve these goals.</p>
</div>
<div class="sect2">
<h3 id="_output_strategy">Output Strategy</h3>
<div class="paragraph">
<p>You can output ReleaseHx-generated histories as HTML or PDF, each with styling capabilities.</p>
</div>
<div class="paragraph">
<p>Other options include which history types to output: Changelog, Release Notes, or hybrid.
If outputting both sequentially, in which order, and what exactly to include in each resource.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
If all you ever want to report is a 1-line summary of changes, you just need a Changelog.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Some logic rules that may help you decide:</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Upstream/Source rules</dt>
<dd>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Any issue with a Release Note entry will also be included in the Changelog.</p>
</li>
<li>
<p>For issues with no Release Note entry, a <code>changelog</code> label or checkbox is needed for inclusion.</p>
</li>
</ol>
</div>
</dd>
<dt class="hdlist1">Output rules/policies</dt>
<dd>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Changelog entries will link to any corresponding release note.</p>
</li>
<li>
<p>Changelogs and Release Notes sections may be organized and ordered distinctly.</p>
</li>
<li>
<p>The entries within each section may be arranged and sorted according to different rules.</p>
</li>
</ol>
</div>
</dd>
</dl>
</div>
<div class="sect3">
<h4 id="_output_configuration">Output Configuration</h4>
<div class="paragraph">
<p>For designating <em>what</em> to output, the following blocks or properties in the config file are relevant:</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><code>history</code></dt>
<dd>
<p>This block defines the overall document and establishes defaults that apply to notes and changelog sections.</p>
</dd>
<dt class="hdlist1"><code>notes</code></dt>
<dd>
<p>This block defines the Release Notes output.</p>
</dd>
<dt class="hdlist1"><code>changelog</code></dt>
<dd>
<p>This block defines the Changelog output.</p>
</dd>
</dl>
</div>
</div>
<div class="sect3">
<h4 id="_output_templating">Output Templating</h4>
<div class="paragraph">
<p>Place templates in the directory established in <code><strong>paths.templates_dir</strong></code> in the config (defaults to <code>./_templates</code>).</p>
</div>
<div class="paragraph">
<p>Templates replace their namesakes built into the ReleaseHx application or API.</p>
</div>
<div class="paragraph">
<p>By default, ReleaseHx expects templates to be formatted in <a href="https://shopify.github.io">Liquid</a>.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
ReleaseHx uses Liquid version <code>~&gt; {liquid_version_minor}</code>, which is incompatible with Ruby apps still using Liquid 4, such as Jekyll and LiquiDoc.
For this reason, you may need to maintain separate Gemfiles in a repository where different apps use incompatible versions of Liquid.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_sourcing_strategy">Sourcing Strategy</h3>
<div class="paragraph">
<p>Where will your release history content come from, and in what format(s) will you <em>edit</em> it?</p>
</div>
<div class="sect3">
<h4 id="_task_tracking_source">Task-tracking Source</h4>
<div class="paragraph">
<p>If your team uses a supported issue-management system (JIRA, GitHub Issues, or GitLab Issues), you will almost certainly wish to integrate that platform.</p>
</div>
<div class="paragraph">
<p>Additionally, assuming your team uses Git, you <em>may</em> wish to derive content from Git commits.
This is only the case if your commit messages are suited to including drafts of release notes, which is fairly rare.</p>
</div>
<div class="paragraph">
<p>Generally, you will derive change summaries, notes, and metadata from your IMS, but hybrid sourcing is readily configurable.
Since you operate ReleaseHx in your product repository, it <em>uses Git directly on your system</em> rather than relying on your cloud-hosted Git service.</p>
</div>
</div>
<div class="sect3">
<h4 id="_local_flat_file_source">Local Flat-file Source</h4>
<div class="paragraph">
<p>Once the relevant &#8220;issues&#8221; have been derived from your API or Git, they become &#8220;changes&#8221; in ReleaseHx terminology, and they are held as a data object in RHYML format.</p>
</div>
<div class="paragraph">
<p>At this point, these changes can be converted to YAML, Markdown, or AsciiDoc so you can tinker with them.
This process is called &#8220;drafting&#8221;&#8201;&#8212;&#8201;it compiles your changes into one document.</p>
</div>
<div class="paragraph">
<p>The recommended procedure is to use YAML at this phase.
Only the YAML format maintains the changes as data and as a single source of truth.
If you make a change in the RHYML/YAML document, you can still readily convert to Markdown or AsciiDoc at any point.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Edit-at-source method</dt>
<dd>
<p>However, if you are confident in the overall shape of your issues at the source, drafting directly to Markdown or AsciiDoc, or even converting directly to HTML or PDF, are available options.</p>
<div class="paragraph">
<p>Indeed, <em>if you wish to do all of your editing in the IMS interface</em>, this is the way to go.
You can generate Markdown or HTML, review, and make further changes to the IMS issues, then regenerate.</p>
</div>
</dd>
</dl>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rh-strategy">Recommended Strategy for Release Documentation</h3>
<div class="paragraph">
<p>We stand behind the following design principles, but ReleaseHx can enable all this and more.</p>
</div>
<div class="paragraph">
<p>We highly recommend the sites <a href="https://keepachangelog.com">keep a changelog</a> and <a href="https://common-changelog.org">Common Changelog</a> for guidance.</p>
</div>
<div class="paragraph">
<p>For guidance on Release Notes authoring, check out <a href="https://www.releasepad.io/the-complete-guide-to-release-notes-what-are-they-and-what-are-they-used-for/">ReleasePad&#8217;s &#8220;Complete Guide&#8221;</a> and <a href="https://www.prodpad.com/blog/release-notes/">ProdPad&#8217;s &#8220;How-to&#8221; article</a>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">Publish a unified document called something like &#8220;Release History&#8221;, with entries for each sequential release of your product.</dt>
<dd>
<p>The constituent documents, Changelog and Release Notes, are both part of the Release History.
The recommended order is Changelog first, Release Notes second, with links from applicable Changelog entries to corresponding Release Notes.</p>
<div class="paragraph">
<p>The hybrid strategy would basically be an annotated Changelog, where every designated summary is listed with relevant metadata release note, when available.</p>
</div>
</dd>
<dt class="hdlist1">Publish a &#8220;Changelog&#8221; or &#8220;Change Log&#8221; containing summaries for all user-facing product changes.</dt>
<dd>
<p>Any and all changes that affect users <em>must</em> be listed here.</p>
<div class="paragraph">
<p>A separate, more complete changelog can be published for developers, with issues marked <code>internal</code> also displayed, possibly annotated as such.
Using <code>rhx &lt;v.r.sn&gt; --md --internal</code> will include internal issues in the (Markdown) draft.</p>
</div>
</dd>
<dt class="hdlist1">Publish a &#8220;Release Notes&#8221; section containing an entry for any user-facing product change that requires <em>explanation</em> or deserves special <em>highlighting</em>.</dt>
<dd>
<p>The general rule of thumb is that any change that is not obvious from its summary should have a release note.</p>
<div class="paragraph">
<p>Release notes can be fairly involved, including short bulleted lists or tables.
Anything longer than a few sentences or a short list or table should link to documentation or a release appendix.</p>
</div>
</dd>
<dt class="hdlist1">Use the <strong><em>summary/title field</em></strong> of your issue-management system as the draft Changelog entry.</dt>
<dd>
<p>Alternatively, use the first line of Git commits or the release-note body as the Changelog entry draft.</p>
<div class="paragraph">
<p>If original entries are in present tense or imperative, you can use the <strong class="ppty"><a href="#conf_ppty_rhyml_pasterize_summ">config.rhyml.pasterize_summ</a></strong> property to convert verbs to past tense.</p>
</div>
</dd>
<dt class="hdlist1">Use a <strong><em>custom field</em></strong> (JIRA) or the <strong>issue body</strong> to designate and draft a release note.</dt>
<dd>
<p>A distinct field is optimal, but on any platform you can demarcate release-note content with a comment like <code>&lt;!-- release note --></code> or a heading like <code>## Release note</code>.</p>
</dd>
<dt class="hdlist1">Use labels in your issue-management system to &#8220;tag&#8221; issues as <strong><em>belonging in the Changelog</em></strong> or <strong><em>needing a release note</em></strong> draft.</dt>
<dd>
<p>JIRA supports custom checkboxes, and GitHub/GitLab enable Markdown checboxes, all of which ReleaseHx can scan.</p>
<div class="paragraph">
<p>Also mark issues with tags like <code>breaking</code>, <code>deprecation</code>, <code>experimental</code>, <code>highlight</code>, and <code>internal</code>.</p>
</div>
</dd>
<dt class="hdlist1">Use <code>rhx &lt;v.r.sn&gt; --check</code> to ensure all issues with a release note requirement have a release note.</dt>
<dd>
<p>As long as you have marked relevant issues with the <code>release_note_needed</code> tag, you can use the <code>--check</code> option to ensure all issues with that tag have a release note.</p>
<div class="paragraph">
<p>The exact tag is configurable at <strong class="ppty"><a href="#conf_ppty_tags_release_note_needed">config.tags.release_note_needed</a></strong>.</p>
</div>
</dd>
<dt class="hdlist1">Generate and edit a <strong><em>YAML draft</em></strong> of your Release History until all release notes are drafted.</dt>
<dd>
<p>This means generating an RHYML document and editing it in place if release notes are still streaming in from the IMS.
Use <code>--append</code> to integrate last-minute release notes before generating a final draft.</p>
</dd>
<dt class="hdlist1">Generate a <strong><em>final draft</em></strong> in the format you use for the rest of yur docs.</dt>
<dd>
<p>When all the release notes have been added, generate a final draft in your preferred lightweight markup format.
This is the best place to perform a final once-over and see the content more or less as it will be published.</p>
<div class="paragraph">
<p>Generate rich-text drafts as needed.
These are easy to overwrite.</p>
</div>
</dd>
<dt class="hdlist1"><strong><em>Publish</em></strong> your Release History to your website or other distribution channels.</dt>
<dd>
<p>ReleaseHx makes it possible to render full web pages, but you probably want to situate the content in your static-site generator.</p>
<div class="paragraph">
<p>If you wish to edit in one markup format (Markdown or AsciiDoc) but your SSG expects the other format, you can use the flags <code>--html --no-wrap --frontmatter</code> to render the innards of a page, which most SSGs can publish wrapped in an HTML layout.</p>
</div>
</dd>
</dl>
</div>
<div class="sect3">
<h4 id="_cicd_strategy_caveats">CI/CD Strategy Caveats</h4>
<div class="paragraph">
<p>If your team works in a continuous-deployment environment, you may wish to maintain one ongoing Release History.</p>
</div>
<div class="paragraph">
<p>To do so, modify your API request template with some logical filter, and always use the <code>--append</code> option, drafting to YAML.</p>
</div>
<div class="paragraph">
<p>Continuous-deplyment environments will likely get better treatment in this application prior to the 1.0 release.
I just don&#8217;t have enough experience with them to predict the optimal workflow.</p>
</div>
</div>
</div>
<div class="sect2">
<h3 id="rhx">The <code>rhx</code> Utility</h3>
<div class="paragraph">
<p>For usage outside (or within) a Ruby development environment, ReleaseHx provides the <code>rhx</code> commandline tool.</p>
</div>
<div class="literalblock">
<div class="title">Help screen</div>
<div class="content">
<pre>Usage: rhx VERSION|FILE [options]

Options:
  --md [PATH]            Draft to Markdown
  --adoc, --ad [PATH]    Draft to AsciiDoc
  --yaml, --yml [PATH]   Draft to YAML
  --html [PATH]          Render to HTML
  --pdf [PATH]           Render to PDF
  --json PATH            Use a JSON source file instead of API

  --config PATH          Config location (default: ./releasehx-config.yml)
  --mapping PATH         Alternate API mapping location
  --fetch                Refresh data from source
  --date DATE            Use a specific date for the release
  --hash, --sha SHA256   Assign a commit hash to the release
  --append               Add any new issues to the end of local YAML source
  --over, --force        Overwrite any existing files without prompting
  --check, --scan        Find issues with missing release note
  --empty, -e [RULE]     Set/reverse policy on issues "awaiting notes"
  --internal             Include issues marked internal or likewise
  --[no-]wrap            Render HTML with/out head and body tags
  --[no-]frontmatter     Render or draft with/out frontmatter

  --manpage, --man       Show the full manpage documentation
  --verbose              Express each step to console
  --debug                Express each step and dump data to console
  --quiet                Suppress all output, including warnings</pre>
</div>
</div>
<div class="paragraph">
<p>See <a href="#cli-options-ref">CLI Options Reference</a> for detailed descriptions of each option.</p>
</div>
<div class="sect3">
<h4 id="workflows">Potential Workflows</h4>
<div class="paragraph">
<p>ReleaseHx enables several workflow combinations for drafting and rendering release histories.</p>
</div>
<div class="literalblock">
<div class="content">
<pre>API → Markdown
API → Markdown → HTML
API → Markdown → PDF
API → AsciiDoc
API → AsciiDoc → HTML
API → AsciiDoc → PDF
API → YAML     → HTML
API → YAML     → AsciiDoc → HTML
API → YAML     → AsciiDoc → PDF
API → YAML     → Markdown → HTML
API → YAML     → Markdown → PDF</pre>
</div>
</div>
<div class="paragraph">
<p>The <strong>API</strong> element of the above workflows could be a <strong>RHYML</strong> file, in which case the <strong>API → YAML</strong> conversions are unnecessary, as the YAML step in those workflows is a proper RHYML document already.</p>
</div>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
<div class="paragraph">
<p>Some reasons you might wish to use <strong>RHYML (YAML) drafts</strong> as an interim state:</p>
</div>
<div class="paragraph">
<p>If you expect late-arriving issues, but you want to get started copy editing the ones that exist.</p>
</div>
<div class="paragraph">
<p>Going directly from YAML to HTML gives more control over the final output, as Markdown and even AsciiDOc are quite limited in the semantic HTML they can produce.</p>
</div>
<div class="paragraph">
<p>You may even come to prefer editing serialized short content in YAML, as I have come to.</p>
</div>
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>Most of the workflow cases can be executed using fairly straightforward command combinations.</p>
</div>
<div class="paragraph">
<p>Take this <strong>API → Markdown → HTML/PDF</strong> workflow for instance:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Ensure all release notes are added in the IMS.</p>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --check</pre>
</div>
</div>
</li>
<li>
<p>Create a Markdown draft from API data.</p>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --md</pre>
</div>
</div>
</li>
<li>
<p>Edit and save the Markdown draft.</p>
</li>
<li>
<p>Render HTML and PDF files from the Markdown draft.</p>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --html --pdf</pre>
</div>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>But for cases where you wish to draft in YAML and then in Markdown or AsciiDoc, such as <strong>API → YAML → AsciiDoc → HTML/PDF</strong>, the following series of steps is exemplary:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Create a YAML draft from API data.
rhx 2.1.0 --yaml</p>
</li>
<li>
<p>Edit and save the YAML draft.</p>
</li>
<li>
<p>Add any newly annotated issues to the end of the YAML draft.</p>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --append</pre>
</div>
</div>
</li>
<li>
<p>Create an AsciiDoc draft from the YAML draft.</p>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --adoc</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
ReleaseHx looks for <code>2.1.0.yml</code> and creates an AsciiDoc draft like <code>2.1.0.adoc</code>.
</td>
</tr>
</table>
</div>
</li>
<li>
<p>Edit that draft as AsciiDoc and save.</p>
</li>
<li>
<p>Render HTML and PDF from the AsciiDoc draft.</p>
<div class="literalblock">
<div class="content">
<pre>rhx 2.1.0 --html --pdf</pre>
</div>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
ReleaseHx finds both <code>2.1.0.yml</code> and <code>2.1.0.adoc</code>, choosing the latter.
</td>
</tr>
</table>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>It is also possible to <strong>source directly in RHYML</strong> files and draft to Markdown or AsciiDoc or else directl to HTML/PDF.</p>
</div>
</div>
<div class="sect3">
<h4 id="cli-options-ref">CLI Options Reference</h4>
<div class="paragraph">
<p>The following options are available for the <code>releasehx</code>/<code>rhx</code> commands.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>--adoc, --ad</strong> [<em>PATH</em>]</dt>
<dd>
<p>Draft to AsciiDoc.
Outputs to <code>&lt;drafts_dir&gt;/&lt;template&gt;</code> or <code>&lt;PATH&gt;</code>.</p>
<div class="paragraph">
<p>Where <code>&lt;drafts_dir></code> is the value of <code>paths.drafts_dir</code> in the config, and <code>&lt;template></code> is the value of <code>templates.drafts_filename</code> in the config.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--append</strong></dt>
<dd>
<p>Add any new issues to the end of local YAML source.</p>
<div class="paragraph">
<p>When drafting in YAML, adds new issues to the end of the file.
Be sure to save edits before appending.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--check, --scan</strong></dt>
<dd>
<p>Find issues with missing release note.
:cli_option_message_check_long: Scans issues for those missing release notes and reports findings.
{cli_option_message_check_long}</p>
</dd>
<dt class="hdlist1"><strong>--config</strong> <em>PATH</em></dt>
<dd>
<p>Config location (default: ./releasehx-config.yml).
Use the configuration file at the specified path instead of the default location (<code>./releasehx-config.yml</code>).</p>
</dd>
<dt class="hdlist1"><strong>--debug</strong></dt>
<dd>
<p>Express each step and dump data to console.</p>
</dd>
<dt class="hdlist1"><strong>--empty, -e</strong> [<em>RULE</em>]</dt>
<dd>
<p>Set/reverse policy on issues "awaiting notes".</p>
<div class="paragraph">
<p>Argue a specific drafting policy, or argue the &#8220;opposite&#8221; policy, for handling issues that are marked as <code>release_note_needed</code> but no note is provided.
Set a specific rule (<code>skip</code>, <code>empty</code>, <code>dump</code>, or <code>ai`</code>) to have ReleaseHx include the issue when converting issues to changes, even if no expected note content has been aded.</p>
</div>
<div class="paragraph">
<p>Using <code>-e dump</code> will draft the issue with the entire issue body and commit message as the note content, for any qualifing change entry.
Whereas <code>-e ai</code> will use generative AI to draft note properties from issue body and commit message.</p>
</div>
<div class="paragraph">
<p>Otherwise use just <code>--empty</code> or <code>-e</code> to toggle between <code>skip</code> and <code>empty</code>, if either of those is your default in <strong class="ppty"><a href="#conf_ppty_rhyml_empty_notes">config.rhyml.empty_notes</a></strong>.
If your default is <code>blank</code>, <code>dump</code>, or <code>ai</code>, using <code>--empty</code> or <code>-e</code> with no argument will toggle a <code>skip</code> policy.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--fetch</strong></dt>
<dd>
<p>Refresh data from source.</p>
<div class="paragraph">
<p>Retrieves fresh data rather than using cached/draft files when converting to HTML/PDF.
Typically used like:</p>
</div>
<div class="literalblock">
<div class="content">
<pre>rhx 1.1.0 --fetch --html</pre>
</div>
</div>
<div class="paragraph">
<p>The fetch procedure does write a cached RHYML document before generating final output.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--frontmatter, --no-frontmatter</strong></dt>
<dd>
<p>Render or draft with/out frontmatter.
When generating drafts or rendering HTML output, <em>include</em> (<code>--frontmatter</code>) or <em>exclude</em> (<code>--no-frontmatter</code>) frontmatter.</p>
</dd>
<dt class="hdlist1"><strong>--html</strong> [<em>PATH</em>]</dt>
<dd>
<p>Renders to HTML from default or designated source.
Writes to <code>&lt;output_path&gt;/&lt;template&gt;</code> or <code>&lt;PATH&gt;</code>.</p>
</dd>
<dt class="hdlist1"><strong>--internal</strong></dt>
<dd>
<p>Include issues marked internal or likewise.
Include issues marked as internal or similarly restricted when drafting content.
Has no effect on render operations.</p>
</dd>
<dt class="hdlist1"><strong>--json</strong> <em>PATH</em></dt>
<dd>
<p>Use a JSON source file instead of API.</p>
<div class="paragraph">
<p>Override the configured API connection and use static JSON data from the designated file.</p>
</div>
<div class="paragraph">
<p>This file is formatted as a response from the API configured at <code>api.from</code>.
This is a pre-RHYML data form.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--md</strong> [<em>PATH</em>]</dt>
<dd>
<p>Draft to Markdown.
Outputs to <code>&lt;drafts_dir&gt;/&lt;template&gt;</code> or <code>&lt;PATH&gt;</code>.</p>
<div class="paragraph">
<p>Where <code>&lt;drafts_dir></code> is the value of <code>paths.drafts_dir</code> in the config, and <code>&lt;template></code> is the value of <code>templates.drafts_filename</code> in the config.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--manpage, --man</strong></dt>
<dd>
<p>Show the full manpage documentation.
Includes this options reference and other documentation, all in the terminal.</p>
<div class="admonitionblock tip">
<table>
<tr>
<td class="icon">
<div class="title">Tip</div>
</td>
<td class="content">
Use <code>q</code> to quit back to prompt.
</td>
</tr>
</table>
</div>
</dd>
<dt class="hdlist1"><strong>--mapping</strong></dt>
<dd>
<p>{cli_opton_message_mapping}.
File must be a valid RHYML mapping config, usually stored at <code>_mapping/&lt;apiname&gt;.yaml</code>.</p>
<div class="paragraph">
<p>The mapping base directory can be changed in <strong class="ppty"><a href="#conf_ppty_paths_custom_mappings_dir">config.paths.custom_mappings_dir</a></strong>, but this option must include a complete relative or absolute path.</p>
</div>
</dd>
<dt class="hdlist1"><strong>--over, --force</strong></dt>
<dd>
<p>Overwrite any existing files without prompting.
When writing files, overwrite existing files without prompting for confirmation.</p>
</dd>
<dt class="hdlist1"><strong>--pdf</strong> [<em>PATH</em>]</dt>
<dd>
<p>Renders to PDF from default or designated source.
Writes to <code>&lt;output_path&gt;/&lt;template&gt;</code> or <code>&lt;PATH&gt;</code>.</p>
</dd>
<dt class="hdlist1"><strong>--scan, --check</strong></dt>
<dd>
<p>Find issues with missing release note.
{cli_option_message_check_long}.</p>
</dd>
<dt class="hdlist1"><strong>--verbose</strong></dt>
<dd>
<p>Express each step to console during execution.</p>
</dd>
<dt class="hdlist1"><strong>--wrap, --no-wrap</strong></dt>
<dd>
<p>Render HTML with/out head and body tags.
When rendering HTML, <em>include</em> (<code>--wrap</code>) or <em>exclude</em> (<code>--no-wrap</code>) the <code>&lt;head&gt;</code> and <code>&lt;body&gt;</code> tags and their content.
For use when the opposite value is set in the config file (<strong class="ppty"><a href="#conf_ppty_modes_wrapped">config.modes.wrapped</a></strong>).</p>
</dd>
<dt class="hdlist1"><strong>--quiet</strong></dt>
<dd>
<p>Suppress all output, including warnings.</p>
</dd>
<dt class="hdlist1"><strong>--yaml, --yml</strong> [<em>PATH</em>]</dt>
<dd>
<p>Draft to YAML.
Outputs to <code>&lt;drafts_dir&gt;/&lt;template&gt;</code> or <code>&lt;PATH&gt;</code>.</p>
<div class="paragraph">
<p>Where <code>&lt;drafts_dir></code> is the value of <code>paths.drafts_dir</code> in the config, and <code>&lt;template></code> is the value of <code>templates.drafts_filename</code> in the config.</p>
</div>
</dd>
</dl>
</div>
</div>
</div>
<div class="sect2">
<h3 id="advanced-rhyml">Advanced RHYML</h3>
<div class="paragraph">
<p>The RHYML syntax is designed specifically for tracking product changes and collecting them as &#8220;releases&#8221;.</p>
</div>
<div class="paragraph">
<p>RHYML is also designed particularly for YAML so it can be read, edited, and even authored by humans, including non-programmers.</p>
</div>
<div class="paragraph">
<p>A complete Release History is a collection of planned product releases as, including patch releases, so RHYML has blocks for major/minor releases and for their subordinate patch releases.</p>
</div>
<div class="paragraph">
<p>The full RHYML structure is:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlight"><code class="language-yaml" data-lang="yaml">releases: # Optional key to contain multiple releases
  - # Array (sequence) of releases
    code: 1.2.1 # Required key for an individual release
    memo: |
      A note of any length, formatted as Markdown (default) or AsciiDoc (configured).
    changes:    # Required key for Array of changes
      -         # Array (seqence) of changes
        chid: 1234
        tick: 5678
        hash: abcdef0123456789abcdef0123456789abcdef01
        type: feature
        part: auth
        summ: "Add new user authentication flow"
        note: |
          The login process now supports multi-factor authentication and single sign-on options.
          Users will need to re-authenticate on their next login to set up these new security features.
        tags:
          - breaking
          - highlight
          - component:ui</code></pre>
</div>
</div>
<div class="paragraph">
<p>However, here we will focus on the matter that ReleaseHx deals with: individual, sequential <em>releases</em>, which are identically structured whether for major/minor or patch releases.</p>
</div>
<div class="paragraph">
<p>The two required properties for a release are <code>code</code> and <code>changes</code>, but a release can also have a <code>date</code> and a <code>memo</code>.</p>
</div>
<div class="paragraph">
<p>Memos can be formatted with Markdown or AsciiDoc.
This format must be set either in an individual RHYML file or configured at <strong class="ppty"><a href="#conf_ppty_rhyml_markup">config.rhyml.markup</a></strong>.</p>
</div>
</div>
<div class="sect2">
<h3 id="config-ref">Configuration Reference</h3>
<div id="conf_ppty__meta" class="dlist">
<dl>
<dt class="hdlist1"><strong>_meta</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The metadata settings for the configuration file.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty__meta">config._meta</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty__meta_slug_type" class="dlist">
<dl>
<dt class="hdlist1"><strong>_meta.slug_type</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The format of slugs used in your application, for use with <code>slugify</code> Liquid filter.</p>
</div>
<div class="paragraph">
<p>Must be 'kebab' or 'snake'.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>kebab</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty__meta_slug_type">config._meta.slug_type</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty__meta_tplt_lang" class="dlist">
<dl>
<dt class="hdlist1"><strong>_meta.tplt_lang</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The default format used in fields of <code>Template</code> type.
Must be 'liquid' or 'erb'.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>liquid</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty__meta_tplt_lang">config._meta.tplt_lang</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api" class="dlist">
<dl>
<dt class="hdlist1"><strong>api</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The API or file source for the issues.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api">config.api</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_api_from" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.from</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The type of API or file to use for the issues source.
May be <code>jira</code>, <code>github</code>, <code>gitlab</code>, <code>rhyml</code>, or <code>git</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>json</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_from">config.api.from</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api_href" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.href</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The base URL for the API or JSON file.
Only required if for remote APIs (not RHYML).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>URL</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_href">config.api.href</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api_auth" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.auth</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Properties related to API authentication.</p>
</div>
<div class="paragraph">
<p>This block should be unnecessary if you use a supported API (JIRA, GitHub, GitLab), unless you wish to use differently named environment variables for API credentials.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_auth">config.api.auth</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_api_auth_mode" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.auth.mode</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The type of authentication to use.</p>
</div>
<div class="paragraph">
<p>Options are: <code>basic</code>, <code>token</code>, <code>bearer</code>, <code>header</code>, <code>query</code>, <code>none</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_auth_mode">config.api.auth.mode</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api_auth_user_env" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.auth.user_env</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Name of the environment variable containing the API username.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>RELEASEHX_API_USER</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_auth_user_env">config.api.auth.user_env</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api_auth_key_env" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.auth.key_env</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Name of the environment variable containing the API key or token.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>RELEASEHX_API_KEY</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_auth_key_env">config.api.auth.key_env</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api_auth_org_env" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.auth.org_env</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Name of the environment variable containing the organization credential.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>RELEASEHX_API_ORG</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_auth_org_env">config.api.auth.org_env</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_api_auth_header" class="dlist">
<dl>
<dt class="hdlist1"><strong>api.auth.header</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header to use for authentication.
Only used if <code>api.auth.mode</code> is <code>header</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_api_auth_header">config.api.auth.header</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Details about content origination, as well as markup sources and conversion.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources">config.sources</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_sources_summ" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.summ</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The source of the summary (Changelog) content.
Must be <code>issue_heading</code>, <code>custom_field</code>, or <code>commit_message</code>.</p>
</div>
<div class="paragraph">
<p>If <code>issue_heading</code>, the summary or title field will be used.
If <code>commit_message</code>, the first line of the Git commit will be used.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>issue</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources_summ">config.sources.summ</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The source of release-note headlines, when it is not the same as the summary.</p>
</div>
<div class="paragraph">
<p>Unless a <code>head</code> is available in the RHYML source, the <code>summ</code> will be used.
By default, ReleaseHx does not generate a <code>head</code> property for work items.</p>
</div>
<div class="paragraph">
<p>Potential values: <code>issue_heading</code>, <code>release_note_heading</code>, or <code>commit_message_heading</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources_head">config.sources.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_note" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.note</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The source of the release notes content.
Must be <code>issue_body</code>, <code>custom_field</code>, or <code>commit_message</code>.</p>
</div>
<div class="paragraph">
<p>Defaults to <code>issue_body</code> for GitHub and GitLab, but to <code>custom_field</code> for JIRA.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources_note">config.sources.note</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_note_custom_field" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.note_custom_field</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The name of the custom field to use for the release notes content.
Only used if <code>sources.note</code> is <code>custom_field</code>.</p>
</div>
<div class="paragraph">
<p>This purposely has no default, as you will probably have to look up the actual field ID, which will be something like <code>customfield_10010</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources_note_custom_field">config.sources.note_custom_field</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_note_pattern" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.note_pattern</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The Regular Expressions pattern to match in the body of an issue or commit message, after which all content is considered the release <code>note</code> matter.</p>
</div>
<div class="paragraph">
<p>Defaults to a Markdown or AsciiDoc header or HTML comment with the case-insensitive string <code>release note</code> in it.</p>
</div>
<div class="paragraph">
<p>Uses Capture group <code>note</code> in the Regular Expression to establsh the entire note content.</p>
</div>
<div class="paragraph">
<p>See the <code>sources.head_pattern</code> property for details on extracting a heading (<code>head</code> in RHYML) from the <code>note</code> content.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>RegExp</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>/^((#|=)+ (Draft )?Release Note.*)|(\&lt;!-- (draft )?release note --\&gt;)\n+(?&lt;note&gt;\w(.|\n)+)/gmi</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_sources_note_pattern">config.sources.note_pattern</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_head_pattern" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.head_pattern</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The Regular Expressions pattern to match in the <code>note</code> text to be used to establish a heading for the note (<code>head</code>).
This text is removed from the <code>note</code> value during a draft operaton, if the pattern matches.</p>
</div>
<div class="paragraph">
<p>Defaults to a Markdown or AsciiDoc header or HTML comment with the case-insensitive string <code>release note</code> in it.</p>
</div>
<div class="paragraph">
<p>The <code>head</code> capture group is snipped from text matching this pattern.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>RegExp</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>/^(?&lt;head&gt;[A-Z].*[^.!])\n\n[A-Z].*/gm</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_sources_head_pattern">config.sources.head_pattern</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_markup" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.markup</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The origin markup format for notes.
May be <code>markdown</code> or <code>asciidoc</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>markdown</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources_markup">config.sources.markup</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_sources_engine" class="dlist">
<dl>
<dt class="hdlist1"><strong>sources.engine</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The markup converter to use for the issues.
Defaults to <code>asciidoctor</code> for AsciiDoc and <code>redcarpet</code> for Markdown.
Options include <code>asciidoctor</code>, <code>redcarpet</code>, <code>commonmarker</code>, <code>kramdown</code>, or <code>pandoc</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_sources_engine">config.sources.engine</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_extensions" class="dlist">
<dl>
<dt class="hdlist1"><strong>extensions</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Default file extensions.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_extensions">config.extensions</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_extensions_markdown" class="dlist">
<dl>
<dt class="hdlist1"><strong>extensions.markdown</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>File extension for Markdown drafts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>md</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_extensions_markdown">config.extensions.markdown</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_extensions_asciidoc" class="dlist">
<dl>
<dt class="hdlist1"><strong>extensions.asciidoc</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>File extension for AsciiDoc drafts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>adoc</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_extensions_asciidoc">config.extensions.asciidoc</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_extensions_yaml" class="dlist">
<dl>
<dt class="hdlist1"><strong>extensions.yaml</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>File extension for YAML drafts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>yml</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_extensions_yaml">config.extensions.yaml</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types" class="dlist">
<dl>
<dt class="hdlist1"><strong>types</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Issue types to include in the release history, in the order of display.</p>
</div>
<div class="paragraph">
<p>List as many as you wish to match up with corresponding metadata at the source.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Map</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types">config.types</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_types_feature" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.feature</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>A new capability, functionality, or interface element.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_feature">config.types.feature</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_types_feature_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.feature.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>feature</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_feature_slug">config.types.feature.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_feature_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.feature.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The display label for the type in the release history output.
Defaults to the capitalized key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>New feature</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_feature_text">config.types.feature.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_feature_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.feature.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the type in the release history output.
Defaults in templates to the <code>text</code> property pluralized.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>New features</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_feature_head">config.types.feature.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_feature_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.feature.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues of this type.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>plus-square-o</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_feature_icon">config.types.feature.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_bug" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.bug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>A fix for a previously reported issue.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_bug">config.types.bug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_types_bug_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.bug.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>bug</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_bug_slug">config.types.bug.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_bug_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.bug.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The display label for the type in the release history output.
Defaults to the capitalized key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Bug fix</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_bug_text">config.types.bug.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_bug_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.bug.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the type in the release history output.
Defaults in templates to the <code>text</code> property pluralized.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Bug fixes</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_bug_head">config.types.bug.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_bug_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.bug.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues of this type.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>bug</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_bug_icon">config.types.bug.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_improvement" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.improvement</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>An enhancement to an existing capability, functionality, or interface element.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_improvement">config.types.improvement</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_types_improvement_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.improvement.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>improvement</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_improvement_slug">config.types.improvement.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_improvement_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.improvement.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The display label for the type in the release history output.
Defaults to the capitalized key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Improvement</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_improvement_text">config.types.improvement.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_improvement_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.improvement.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the type in the release history output.
Defaults in templates to the <code>text</code> property pluralized.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Improvements</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_improvement_head">config.types.improvement.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_improvement_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.improvement.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues of this type.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>wrench</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_improvement_icon">config.types.improvement.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_documentation" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.documentation</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>An update to the documentation.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_documentation">config.types.documentation</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_types_documentation_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.documentation.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>documentation</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_documentation_slug">config.types.documentation.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_documentation_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.documentation.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The display label for the type in the release history output.
Defaults to the capitalized key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Documentation</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_documentation_text">config.types.documentation.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_documentation_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.documentation.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the type in the release history output.
Defaults in templates to the <code>text</code> property pluralized.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Docs Changes</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_documentation_head">config.types.documentation.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types_documentation_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.documentation.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues of this type.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>book</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types_documentation_icon">config.types.documentation.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types__type_name_" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.&lt;type_name&gt;</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The corresponding issue type.</p>
</div>
<div class="paragraph">
<p>The key should be a simple string for referencing the slug in RHYML and ReleaseHx templates.
This is what will be entered in a change&#8217;s <code>type</code> property in RHYML.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types__type_name_">config.types.&lt;type_name&gt;</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_types__type_name__slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.&lt;type_name&gt;.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types<em>type_name</em>slug">config.types.&lt;type_name&gt;.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types__type_name__text" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.&lt;type_name&gt;.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The display label for the type in the release history output.
Defaults to the capitalized key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types<em>type_name</em>text">config.types.&lt;type_name&gt;.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types__type_name__head" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.&lt;type_name&gt;.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the type in the release history output.
Defaults in templates to the <code>text</code> property pluralized.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types<em>type_name</em>head">config.types.&lt;type_name&gt;.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_types__type_name__icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>types.&lt;type_name&gt;.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues of this type.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_types<em>type_name</em>icon">config.types.&lt;type_name&gt;.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_parts" class="dlist">
<dl>
<dt class="hdlist1"><strong>parts</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The map of product components to include in the release history, in the order of display.</p>
</div>
<div class="paragraph">
<p>List as many as you wish to match up with corresponding metadata at the source.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Map</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_parts">config.parts</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_parts__part_name_" class="dlist">
<dl>
<dt class="hdlist1"><strong>parts.&lt;part_name&gt;</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The corresponding product component.</p>
</div>
<div class="paragraph">
<p>The key should be a simple string for referencing the slug in RHYML and ReleaseHx templates.
This is what will be entered in a change&#8217;s <code>part</code> property in RHYML.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Map</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_parts__part_name_">config.parts.&lt;part_name&gt;</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_parts__part_name__slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>parts.&lt;part_name&gt;.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_parts<em>part_name</em>slug">config.parts.&lt;part_name&gt;.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_parts__part_name__text" class="dlist">
<dl>
<dt class="hdlist1"><strong>parts.&lt;part_name&gt;.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The display text for the component in the release history output.
Defaults to the capitalized key name.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_parts<em>part_name</em>text">config.parts.&lt;part_name&gt;.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_parts__part_name__head" class="dlist">
<dl>
<dt class="hdlist1"><strong>parts.&lt;part_name&gt;.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the component in the release history output.
Defaults in templates to the <code>text</code> property pluralized.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_parts<em>part_name</em>head">config.parts.&lt;part_name&gt;.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_parts__part_name__icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>parts.&lt;part_name&gt;.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues that affect this component.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_parts<em>part_name</em>icon">config.parts.&lt;part_name&gt;.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Handling for tags, labels, or toggles associated with source Issues.</p>
</div>
<div class="paragraph">
<p>Subordinate property keys (other than <code>include</code> and <code>exclude</code>) represent individual tag names, with the default set documented here.
The property <code>&lt;your_tag_name&gt;</code> represents arbitrarily named tags, any number of which you are welcome to add.</p>
</div>
<div class="paragraph">
<p>This block serves to filter out any unrelated labels/tags ingested from APIs during the conversion from payload to RHYML draft.
Only tags with their own property here will be ported from the issue source to the RHYML change record&#8217;s <code>tags</code> Array.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Array</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags">config.tags</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags__include" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags._include</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tags, labels, or toggles that trigger inclusion in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Array</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>- highlight
- deprecation
- removal
- breaking
- experimental
- changelog</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_tags__include">config.tags._include</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags__exclude" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags._exclude</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The list of tags that cause a change/issue to be excluded from the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Array</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code></code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags__exclude">config.tags._exclude</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_highlight" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.highlight</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates an issue is to be highlighted or prioritized in sorts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_highlight">config.tags.highlight</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_highlight_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.highlight.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Highlights</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_highlight_head">config.tags.highlight.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_highlight_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.highlight.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>highlight</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_highlight_text">config.tags.highlight.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_highlight_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.highlight.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>highlighted</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_highlight_slug">config.tags.highlight.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_highlight_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.highlight.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>star</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_highlight_icon">config.tags.highlight.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_highlight_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.highlight.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_highlight_groupable">config.tags.highlight.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_deprecation" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.deprecation</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates an issue includes a feature discontinuation announcement.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_deprecation">config.tags.deprecation</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_deprecation_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.deprecation.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Deprecations</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_deprecation_head">config.tags.deprecation.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_deprecation_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.deprecation.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Deprecated</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_deprecation_text">config.tags.deprecation.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_deprecation_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.deprecation.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>deprecation</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_deprecation_slug">config.tags.deprecation.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_deprecation_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.deprecation.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>exclamation-triangle</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_deprecation_icon">config.tags.deprecation.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_deprecation_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.deprecation.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_deprecation_groupable">config.tags.deprecation.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_breaking" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.breaking</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates a potentially disruptive change.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_breaking">config.tags.breaking</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_breaking_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.breaking.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Breaking Changes</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_breaking_head">config.tags.breaking.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_breaking_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.breaking.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Breaking</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_breaking_text">config.tags.breaking.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_breaking_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.breaking.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>breaking</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_breaking_slug">config.tags.breaking.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_breaking_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.breaking.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>exclamation-triangle</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_breaking_icon">config.tags.breaking.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_breaking_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.breaking.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_breaking_groupable">config.tags.breaking.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_removal" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.removal</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates a change includes a feature removal.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_removal">config.tags.removal</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_removal_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.removal.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Feature Removals</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_removal_head">config.tags.removal.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_removal_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.removal.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Removed</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_removal_text">config.tags.removal.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_removal_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.removal.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>removal</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_removal_slug">config.tags.removal.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_removal_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.removal.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>sign-out</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_removal_icon">config.tags.removal.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_removal_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.removal.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_removal_groupable">config.tags.removal.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_security" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.security</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates a change includes a security-related alteration.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_security">config.tags.security</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_security_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.security.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Security Fixes</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_security_head">config.tags.security.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_security_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.security.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Security</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_security_text">config.tags.security.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_security_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.security.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>security</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_security_slug">config.tags.security.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_security_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.security.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>shield</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_security_icon">config.tags.security.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_security_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.security.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_security_groupable">config.tags.security.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_experimental" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.experimental</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates a feature that is not yet stable or may not be permanent.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_experimental">config.tags.experimental</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_experimental_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.experimental.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>Experimental Features</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_tags_experimental_head">config.tags.experimental.head</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_experimental_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.experimental.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Experimental</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_experimental_text">config.tags.experimental.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_experimental_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.experimental.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>experimental</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_experimental_slug">config.tags.experimental.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_experimental_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.experimental.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>flask</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_experimental_icon">config.tags.experimental.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_experimental_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.experimental.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_experimental_groupable">config.tags.experimental.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_internal" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.internal</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates an issue documents a feature that is not intended for &#8220;public&#8221; use or visibility.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_internal">config.tags.internal</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_internal_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.internal.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Internal Changes</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_internal_head">config.tags.internal.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_internal_text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.internal.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Internal</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_internal_text">config.tags.internal.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_internal_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.internal.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>internal</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_internal_slug">config.tags.internal.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_internal_icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.internal.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>lock</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_internal_icon">config.tags.internal.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_internal_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.internal.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_internal_groupable">config.tags.internal.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_changelog" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.changelog</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates an issue should appear in the Changelog even if it does not have a Release Note.</p>
</div>
<div class="paragraph">
<p>There is no icon associated with changelog-tagged issues, as it is only declaring that an issue belongs in the Changelog.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_changelog">config.tags.changelog</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_changelog_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.changelog.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>changelog</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_changelog_slug">config.tags.changelog.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_changelog_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.changelog.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_changelog_groupable">config.tags.changelog.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_release_note_needed" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.release_note_needed</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The tag, label, or toggle that indicates an issue requires a release note to be written. This is used by the <code>rhx</code> CLI to determine which issues to fetch for release notes.
This tag can be automatically removed, and it does not show up in published Changelog or Release Notes (unless you add it).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_release_note_needed">config.tags.release_note_needed</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags_release_note_needed_slug" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.release_note_needed.slug</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The literal string used in the Issues system for tagging or labeling an issue to be handled, if different than the key name.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This technically does not have to be a &#8220;Slug&#8221; String, if the system permits spaces.
It just needs to exactly match whatever String the remote API returns to represent the label/tag.
</td>
</tr>
</table>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>release_note_needed</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_release_note_needed_slug">config.tags.release_note_needed.slug</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags_release_note_needed_groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.release_note_needed.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags_release_note_needed_groupable">config.tags.release_note_needed.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags__your_tag_name_" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.&lt;your_tag_name&gt;</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Unlimited custom tags of your choosing, associated with a tag, label, or toggle in the Issues system <em>or</em> manually assignable in RHYML.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags__your_tag_name_">config.tags.&lt;your_tag_name&gt;</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_tags__your_tag_name__head" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.&lt;your_tag_name&gt;.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a grouping title.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags<em>your_tag_name</em>head">config.tags.&lt;your_tag_name&gt;.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags__your_tag_name__text" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.&lt;your_tag_name&gt;.text</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>How this tag will display as a label.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags<em>your_tag_name</em>text">config.tags.&lt;your_tag_name&gt;.text</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags__your_tag_name__icon" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.&lt;your_tag_name&gt;.icon</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The icon to use for issues so-tagged.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags<em>your_tag_name</em>icon">config.tags.&lt;your_tag_name&gt;.icon</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_tags__your_tag_name__groupable" class="dlist">
<dl>
<dt class="hdlist1"><strong>tags.&lt;your_tag_name&gt;.groupable</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether this tag can be used to group issues in the release history.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_tags<em>your_tag_name</em>groupable">config.tags.&lt;your_tag_name&gt;.groupable</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Base paths for building links to online references like issues (web) and commits (git).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_links">config.links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_links_web" class="dlist">
<dl>
<dt class="hdlist1"><strong>links.web</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The URL template for the web links in the release history listings.
May include <code>{{ ticketid }}</code> as placeholders.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_links_web">config.links.web</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_links_git" class="dlist">
<dl>
<dt class="hdlist1"><strong>links.git</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The URL template for the git links in the release history listings.
May include <code>{{ githash }}</code> as placeholders.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_links_git">config.links.git</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_links_usr" class="dlist">
<dl>
<dt class="hdlist1"><strong>links.usr</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The URL template for the contributor homepages in the release history listings.
May include <code>{{ username }}</code> as placeholders.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_links_usr">config.links.usr</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_paths" class="dlist">
<dl>
<dt class="hdlist1"><strong>paths</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The configuration for the paths to include in the release history listings.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_paths">config.paths</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_paths_templates_dir" class="dlist">
<dl>
<dt class="hdlist1"><strong>paths.templates_dir</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The path to the templates directory.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>_templates</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_paths_templates_dir">config.paths.templates_dir</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_paths_drafts_dir" class="dlist">
<dl>
<dt class="hdlist1"><strong>paths.drafts_dir</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The path to the output directory for generated drafts (YAML, Markdown, AsciiDoc).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>_output</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_paths_drafts_dir">config.paths.drafts_dir</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_paths_publish_dir" class="dlist">
<dl>
<dt class="hdlist1"><strong>paths.publish_dir</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The path to the output directory for rendered files (HTML, PDF).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>_publish</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_paths_publish_dir">config.paths.publish_dir</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_paths_custom_mappings_dir" class="dlist">
<dl>
<dt class="hdlist1"><strong>paths.custom_mappings_dir</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The path to the directory containing user-defined API mappings.</p>
</div>
<div class="paragraph">
<p>ReleaseHx checks here first for a file named <code>&lt;api_from_name&gt;.yml</code>, where <code>&lt;api_from_name&gt;</code> is set in the <code>api.from</code> property.
If no file is found, the mapping is expected to be supplied by the gem (see <code>&lt;GEM_ROOT&gt;/mappings/</code>).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>_mappings</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_paths_custom_mappings_dir">config.paths.custom_mappings_dir</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Patterns for dynamically forming strings.</p>
</div>
<div class="paragraph">
<p>By default, these use the template engine defined in <code>_meta.tplt_lang</code>, which defaults to <code>liquid</code>.</p>
</div>
<div class="paragraph">
<p>You may override the stated or implied <code>_meta.tplt_lang</code> setting by prepending the relevant node value with <code>!erb</code> or <code>!liquid</code>, as needed.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_templates">config.templates</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_templates_draft_filename" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.draft_filename</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The filename template for the draft files.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>{{ version }}.{{ format_ext }}</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_templates_draft_filename">config.templates.draft_filename</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_publish_filename" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.publish_filename</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The filename template for the published files.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>release-history-{{ version }}.{{ format_ext }}</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_templates_publish_filename">config.templates.publish_filename</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_chid" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.chid</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The template for automatic change ID/slug construction, if available at draft-time.
(These settings may be argued in the <code>rhx</code> CLI using <code>--date DATE</code> and <code>--hash SHA256</code>.)</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Slug</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>{{- this.tick }}-{{ this.summ | truncate: 20 | slugify }}</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_templates_chid">config.templates.chid</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_markdown_frontmatter" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.markdown_frontmatter</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Designates the content inserted at the top of Markdown files as document-level metadata.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>---
title: Release History for {{ release.code }}
version: {{ release.code }}
date: {{ release.date }}
---</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_templates_markdown_frontmatter">config.templates.markdown_frontmatter</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_asciidoc_frontmatter" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.asciidoc_frontmatter</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Designates the way front-matter is inserted at the top of AsciiDoc files.
Several variables are available to templates.</p>
</div>
<div class="paragraph">
<p>AsciiDoc frontmatter templates may also contain AsciiDoc attribute placeholders.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>:page-title: Release History for {{ release.code }}
:page-version: {{ release.code }}
:page-date: {{ release.date }}</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_templates_asciidoc_frontmatter">config.templates.asciidoc_frontmatter</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_html_frontmatter" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.html_frontmatter</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Designates the way front-matter is inserted at the top of <em>unwrapped</em> rendered HTML.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>---
title: Release History for {{ release.code }}
version: {{ release.code }}
date: {{ release.date }}
---</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_templates_html_frontmatter">config.templates.html_frontmatter</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_git_version_tag" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.git_version_tag</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>For &#8220;raw Git&#8221; setups, this is the Git-tag label format for the version.
Uses the variable <code>version</code> as the <em>argued version</em>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>v{{ version }}</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_templates_git_version_tag">config.templates.git_version_tag</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_git_version_tag_pattern" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.git_version_tag_pattern</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The Regular Expressions pattern to count as a previous version in the Git history, for &#8220;raw Git&#8221; setups.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>RegExp</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>/^v\d+\.\d+\.\d+$/</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_templates_git_version_tag_pattern">config.templates.git_version_tag_pattern</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_templates_git_branch_name" class="dlist">
<dl>
<dt class="hdlist1"><strong>templates.git_branch_name</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>For &#8220;raw Git&#8221; setups, this is the Git-branch name format for the determining version.
All commits on this branch will be considered potential entries for Changelog/Release Notes.</p>
</div>
<div class="paragraph">
<p>When using Git tags to determine version, this property should be set to a branch like <code>main</code> or <code>trunk</code> <code>release</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Liquid</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>v{{ version }}</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_templates_git_branch_name">config.templates.git_branch_name</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_modes" class="dlist">
<dl>
<dt class="hdlist1"><strong>modes</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Default settings for <code>rhx</code> command executions.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_modes">config.modes</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_modes_wrapped" class="dlist">
<dl>
<dt class="hdlist1"><strong>modes.wrapped</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Include (or exclude) head, header, and footer elements when rendering to HTML.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_modes_wrapped">config.modes.wrapped</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_modes_html_frontmatter" class="dlist">
<dl>
<dt class="hdlist1"><strong>modes.html_frontmatter</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Include frontmatter in the rendered HTML.</p>
</div>
<div class="paragraph">
<p>See the <code>templates.page_frontmatter</code> property for details.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>true</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_modes_html_frontmatter">config.modes.html_frontmatter</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_modes_markdown_frontmatter" class="dlist">
<dl>
<dt class="hdlist1"><strong>modes.markdown_frontmatter</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Include frontmatter in Markdown drafts.</p>
</div>
<div class="paragraph">
<p>Uses the <code>templates.markdown_frontmatter</code> template.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_modes_markdown_frontmatter">config.modes.markdown_frontmatter</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_modes_asciidoc_frontmatter" class="dlist">
<dl>
<dt class="hdlist1"><strong>modes.asciidoc_frontmatter</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Include frontmatter in AsciiDoc drafts.</p>
</div>
<div class="paragraph">
<p>Uses the <code>templates.asciidoc_frontmatter</code> template.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_modes_asciidoc_frontmatter">config.modes.asciidoc_frontmatter</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_modes_fetch" class="dlist">
<dl>
<dt class="hdlist1"><strong>modes.fetch</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>What to fetch when gathering issues from API.</p>
</div>
<div class="paragraph">
<p>Valid entries:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>all-tagged</code>&#8201;&#8212;&#8201;fetches issues with <code>release_note_needed</code> tag.</p>
</li>
</ul>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>notes-only</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_modes_fetch">config.modes.fetch</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_rhyml" class="dlist">
<dl>
<dt class="hdlist1"><strong>rhyml</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Settings related to RHYML data objects and documents.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_rhyml">config.rhyml</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_rhyml_markup" class="dlist">
<dl>
<dt class="hdlist1"><strong>rhyml.markup</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The markup format for the <code>note</code> or <code>memo</code> properties of RHYML objects.</p>
</div>
<div class="paragraph">
<p>Change to <code>asciidoc</code> to convert upstream Markdown to AsciiDoc.</p>
</div>
<div class="paragraph">
<p>This setting can be set (and overridden) with the <code>_config.markup</code> property in any given RHYML document.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>markdown</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_rhyml_markup">config.rhyml.markup</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_rhyml_empty_notes" class="dlist">
<dl>
<dt class="hdlist1"><strong>rhyml.empty_notes</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>What to do for issues that lack a release note but have the <code>release_note_needed</code> tag.</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>skip</code> the issue when drafting notes (can update with <code>--amend</code>)</p>
</li>
<li>
<p><code>empty</code> include the issue with an empty note</p>
</li>
<li>
<p><code>dump</code> the complete issue body/description and commit message as the <code>note</code> property</p>
</li>
<li>
<p><code>ai</code> generate a note using generative AI</p>
</li>
</ul>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>skip</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_rhyml_empty_notes">config.rhyml.empty_notes</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_rhyml_max_parts" class="dlist">
<dl>
<dt class="hdlist1"><strong>rhyml.max_parts</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The maximum number of affected <em>part</em> categories that can be recorded for a single change.</p>
</div>
<div class="paragraph">
<p>When <code>0</code>, <em>part</em> records are disables for all changes.
When <code>1</code>, only one <em>part</em> is allowed per change (String).
When <code>2</code> or more, a single affiliated <em>part</em> category may be recorded using the <code>part</code> property, but more than one must be recorded using the <code>parts</code> property (Array).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Integer</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>1</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_rhyml_max_parts">config.rhyml.max_parts</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_rhyml_pasterize_summ" class="dlist">
<dl>
<dt class="hdlist1"><strong>rhyml.pasterize_summ</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to convert verbs in the <code>summ</code> property to past tense when drafting.
Replaces common words like <code>adds</code> with <code>added</code>, <code>fix</code> with <code>fixed</code>, <code>builds</code> with <code>built</code>, etc.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_rhyml_pasterize_summ">config.rhyml.pasterize_summ</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_rhyml_pasterize_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>rhyml.pasterize_head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to convert verbs in the <code>head</code> property to past tense when drafting.
Replaces common words like <code>adds</code> with <code>added</code>, <code>fix</code> with <code>fixed</code>, <code>builds</code> with <code>built</code>, etc.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_rhyml_pasterize_head">config.rhyml.pasterize_head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history" class="dlist">
<dl>
<dt class="hdlist1"><strong>history</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Configurations for the overall document, when applicable.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history">config.history</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_history_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the release history output.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>templating</strong>
</td>
<td class="hdlist2">
<p>liquid,  delayed rendering</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>Release History -- {{ release.code }} - {{ release.date }}</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_history_head">config.history.head</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history_htag" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.htag</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The heading level (H1, H2, etc) for the release history header.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>h1</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_htag">config.history.htag</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history_items" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.items</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Settings pertaining to displayed items across Changelog and Release Notes sections.</p>
</div>
<div class="paragraph">
<p>Most of these settings can be defined separately for each section under <a href="#conf_ppty_changelog">config.changelog</a> and <a href="#conf_ppty_notes">config.notes</a>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_items">config.history.items</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_history_items_allow_redundant" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.items.allow_redundant</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to allow duplicate entries in a given section, for instance across groups for <code>part:group</code> sorts where a change affects multiple parts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_items_allow_redundant">config.history.items.allow_redundant</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history_items_issue_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.items.issue_links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include web links in item metadata.</p>
</div>
<div class="paragraph">
<p>Requires <code>links.web</code> to be defined.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_items_issue_links">config.history.items.issue_links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history_items_git_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.items.git_links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include git links in item metadata.</p>
</div>
<div class="paragraph">
<p>Requires <code>links.git</code> to be defined.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_items_git_links">config.history.items.git_links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history_items_metadata_labels" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.items.metadata_labels</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>If and where to display icons in relation to labels in item metadata.</p>
</div>
<div class="paragraph">
<p>Use <code>before</code> or <code>after</code> to choose a spot, <code>none</code> or <code>'$nil'</code> to disable.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>before</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_items_metadata_labels">config.history.items.metadata_labels</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_history_items_metadata_icons" class="dlist">
<dl>
<dt class="hdlist1"><strong>history.items.metadata_icons</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include icons for metadata in item metadata.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>before</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_history_items_metadata_icons">config.history.items.metadata_icons</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The configuration for the changelog output.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog">config.changelog</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_changelog_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the changelog output.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Changelog</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_head">config.changelog.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_htag" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.htag</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The heading level (H1, H2, etc) for the changelog section header.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>h2</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_htag">config.changelog.htag</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_spot" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.spot</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Where in the document to place the changelog (<code>1</code> = top, <code>2</code> = bottom).</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Integer</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>2</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_spot">config.changelog.spot</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_sort" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.sort</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The sort order for the changelog output.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Array</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>- part:grouping1</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_changelog_sort">config.changelog.sort</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_items" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Settings that affect the frame/shape and arrangement of individual changelog entries.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items">config.changelog.items</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_changelog_items_frame" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items.frame</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The layout for the changelog entry display.</p>
</div>
<div class="paragraph">
<p>Can be <code>ordered</code>, <code>unordered</code>, or <code>paragraph</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>table-cols-1</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items_frame">config.changelog.items.frame</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_items_allow_redundant" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items.allow_redundant</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to allow duplicate entries in a given section, for instance across groups for <code>part:group</code> sorts where a change affects multiple parts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items_allow_redundant">config.changelog.items.allow_redundant</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_items_git_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items.git_links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include git links in item metadata.</p>
</div>
<div class="paragraph">
<p>Requires <code>links.git</code> to be defined.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items_git_links">config.changelog.items.git_links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_items_issue_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items.issue_links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include web links in item metadata.</p>
</div>
<div class="paragraph">
<p>Requires <code>links.web</code> to be defined.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items_issue_links">config.changelog.items.issue_links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_items_metadata_labels" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items.metadata_labels</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>If and where to display icons in relation to labels in item metadata.</p>
</div>
<div class="paragraph">
<p>Use <code>before</code> or <code>after</code> to choose a spot, <code>none</code> or <code>'$nil'</code> to disable.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>before</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items_metadata_labels">config.changelog.items.metadata_labels</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_changelog_items_metadata_icons" class="dlist">
<dl>
<dt class="hdlist1"><strong>changelog.items.metadata_icons</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include icons for metadata in item metadata.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>before</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_changelog_items_metadata_icons">config.changelog.items.metadata_icons</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The configuration for the Release Notes listing section.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes">config.notes</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_notes_head" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.head</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The header for the notes output.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>Release Notes</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_head">config.notes.head</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_htag" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.htag</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The heading level (H1, H2, etc) for the release notes section header.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>h2</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_htag">config.notes.htag</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_spot" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.spot</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Where in the document to place the Release Notes relative to the Changelog.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Integer</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>1</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_spot">config.notes.spot</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_sort" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.sort</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The sort <strong>order</strong> for the release notes output.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Array</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
</td>
</tr>
</table>
</div>
<div class="literalblock">
<div class="content">
<pre>- highlight:grouping1
- deprecation:grouping1
- removal:grouping1
- breaking:grouping1
- type:grouping1
- part:grouping2</pre>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1"><strong>path</strong></dt>
<dd>
<p><code><a href="#conf_ppty_notes_sort">config.notes.sort</a></code></p>
</dd>
</dl>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_items" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Settings that affect the frame/shape and arrangement of individual release-note item displays.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items">config.notes.items</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
<div id="conf_ppty_notes_items_frame" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items.frame</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>The layout for the release-note item display.</p>
</div>
<div class="paragraph">
<p>Can be <code>table-cols-1</code>, <code>table-cols-2</code>, <code>desc-list</code>, or <code>admonition</code>.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>table-cols-1</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items_frame">config.notes.items.frame</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_items_allow_redundant" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items.allow_redundant</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to allow duplicate entries in a given section, for instance across groups for <code>part:group</code> sorts where a change affects multiple parts.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items_allow_redundant">config.notes.items.allow_redundant</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_items_git_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items.git_links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include git links in item metadata.</p>
</div>
<div class="paragraph">
<p>Requires <code>links.git</code> to be defined.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items_git_links">config.notes.items.git_links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_items_issue_links" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items.issue_links</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include web links in item metadata.</p>
</div>
<div class="paragraph">
<p>Requires <code>links.web</code> to be defined.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items_issue_links">config.notes.items.issue_links</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_items_metadata_labels" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items.metadata_labels</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>If and where to display icons in relation to labels in item metadata.</p>
</div>
<div class="paragraph">
<p>Use <code>before</code> or <code>after</code> to choose a spot, <code>none</code> or <code>'$nil'</code> to disable.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>String</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>before</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items_metadata_labels">config.notes.items.metadata_labels</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
<div id="conf_ppty_notes_items_metadata_icons" class="dlist">
<dl>
<dt class="hdlist1"><strong>notes.items.metadata_icons</strong></dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Whether to include icons for metadata in item metadata.</p>
</div>
<div class="hdlist">
<table>
<tr>
<td class="hdlist1">
<strong>type</strong>
</td>
<td class="hdlist2">
<p>Boolean</p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>default</strong>
</td>
<td class="hdlist2">
<p><code>before</code></p>
</td>
</tr>
<tr>
<td class="hdlist1">
<strong>path</strong>
</td>
<td class="hdlist2">
<p><code><a href="#conf_ppty_notes_items_metadata_icons">config.notes.items.metadata_icons</a></code></p>
</td>
</tr>
</table>
</div>
</div>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</div>
<div class="sect2">
<h3 id="sample-config">Sample Configurations</h3>
<div class="listingblock">
<div class="content">
<pre class="highlight"><code class="language-yaml" data-lang="yaml">_meta: # The metadata settings for the configuration file.
  slug_type: kebab # The format of slugs used in your application, f...
  tplt_lang: liquid # The default format used in fields of Template t...
api: # The API or file source for the issues.
  from: json # Issue source API type (jira, github, gitlab, rh...
  # href: # (URL) # The base URL for the API or JSON file.
  auth: # Properties related to API authentication.
    # mode: # # The type of authentication to use.
    user_env: RELEASEHX_API_USER # Name of the environment variable containing the...
    key_env: RELEASEHX_API_KEY # Name of the environment variable containing the...
    org_env: RELEASEHX_API_ORG # Name of the environment variable containing the...
    # header: # (String) # The header to use for authentication.
sources: # Details about content origination, as well as m...
  summ: issue # The source of the summary (Changelog) content.
  # head: # (String) # The source of release-note headlines, when it i...
  # note: # (String) # The source of the release notes content.
  # note_custom_field: # (String) # The name of the custom field to use for the rel...
  note_pattern: | # The Regular Expressions pattern to match in the...
    /^((#|=)+ (Draft )?Release Note.*)|(\&lt;!-- (draft )?release note --\&gt;)\n+(?&lt;note&gt;\w(.|\n)+)/gmi
  head_pattern: | # The Regular Expressions pattern to match in the...
    /^(?&lt;head&gt;[A-Z].*[^.!])\n\n[A-Z].*/gm
  markup: markdown # The origin markup format for notes.
  # engine: # (String) # The markup converter to use for the issues.
extensions: # Default file extensions.
  markdown: md # File extension for Markdown drafts.
  asciidoc: adoc # File extension for AsciiDoc drafts.
  yaml: yml # File extension for YAML drafts.
types: # Issue types to include in the release history, ...
  feature: # A new capability, functionality, or interface e...
    slug: feature # The literal string used in the Issues system fo...
    text: New feature # The display label for the type in the release h...
    head: New features # The header for the type in the release history ...
    icon: plus-square-o # The icon to use for issues of this type.
  bug: # A fix for a previously reported issue.
    slug: bug # The literal string used in the Issues system fo...
    text: Bug fix # The display label for the type in the release h...
    head: Bug fixes # The header for the type in the release history ...
    icon: bug # The icon to use for issues of this type.
  improvement: # An enhancement to an existing capability, funct...
    slug: improvement # The literal string used in the Issues system fo...
    text: Improvement # The display label for the type in the release h...
    head: Improvements # The header for the type in the release history ...
    icon: wrench # The icon to use for issues of this type.
  documentation: # An update to the documentation.
    slug: documentation # The literal string used in the Issues system fo...
    text: Documentation # The display label for the type in the release h...
    head: Docs Changes # The header for the type in the release history ...
    icon: book # The icon to use for issues of this type.
  # &lt;type_name&gt;: # # The corresponding issue type.
    # slug: # (String) # The literal string used in the Issues system fo...
    # text: # (String) # The display label for the type in the release h...
    # head: # (String) # The header for the type in the release history ...
    # icon: # (String) # The icon to use for issues of this type.
parts: # The map of product components to include in the...
  # &lt;part_name&gt;: # (Map) # The corresponding product component.
    # slug: # (String) # The literal string used in the Issues system fo...
    # text: # (String) # The display text for the component in the relea...
    # head: # (String) # The header for the component in the release his...
    # icon: # (String) # The icon to use for issues that affect this com...
tags: # Handling for tags, labels, or toggles associate...
  _include: # The tags, labels, or toggles that trigger inclu...
    - highlight
    - deprecation
    - removal
    - breaking
    - experimental
    - changelog
  _exclude: # The list of tags that cause a change/issue to b...
  highlight: # The tag, label, or toggle that indicates an iss...
    head: Highlights # How this tag will display as a grouping title.
    text: highlight # How this tag will display as a label.
    slug: highlighted # The literal string used in the Issues system fo...
    icon: star # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  deprecation: # The tag, label, or toggle that indicates an iss...
    head: Deprecations # How this tag will display as a grouping title.
    text: Deprecated # How this tag will display as a label.
    slug: deprecation # The literal string used in the Issues system fo...
    icon: exclamation-triangle # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  breaking: # The tag, label, or toggle that indicates a pote...
    head: Breaking Changes # How this tag will display as a grouping title.
    text: Breaking # How this tag will display as a label.
    slug: breaking # The literal string used in the Issues system fo...
    icon: exclamation-triangle # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  removal: # The tag, label, or toggle that indicates a chan...
    head: Feature Removals # How this tag will display as a grouping title.
    text: Removed # How this tag will display as a label.
    slug: removal # The literal string used in the Issues system fo...
    icon: sign-out # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  security: # The tag, label, or toggle that indicates a chan...
    head: Security Fixes # How this tag will display as a grouping title.
    text: Security # How this tag will display as a label.
    slug: security # The literal string used in the Issues system fo...
    icon: shield # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  experimental: # The tag, label, or toggle that indicates a feat...
    head: Experimental Features # How this tag will display as a grouping title.
    text: Experimental # How this tag will display as a label.
    slug: experimental # The literal string used in the Issues system fo...
    icon: flask # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  internal: # The tag, label, or toggle that indicates an iss...
    head: Internal Changes # How this tag will display as a grouping title.
    text: Internal # How this tag will display as a label.
    slug: internal # The literal string used in the Issues system fo...
    icon: lock # The icon to use for issues so-tagged.
    groupable: true # Whether this tag can be used to group issues in...
  changelog: # The tag, label, or toggle that indicates an iss...
    slug: changelog # The literal string used in the Issues system fo...
    groupable: false # Whether this tag can be used to group issues in...
  release_note_needed: # The tag, label, or toggle that indicates an iss...
    slug: release_note_needed # The literal string used in the Issues system fo...
    groupable: false # Whether this tag can be used to group issues in...
  # &lt;your_tag_name&gt;: # # Unlimited custom tags of your choosing, associa...
    # head: # (String) # How this tag will display as a grouping title.
    # text: # (String) # How this tag will display as a label.
    # icon: # (String) # The icon to use for issues so-tagged.
    # groupable: # (Boolean) # Whether this tag can be used to group issues in...
links: # Base paths for building links to online referen...
  # web: # (Liquid) # The URL template for the web links in the relea...
  # git: # (Liquid) # The URL template for the git links in the relea...
  # usr: # (Liquid) # The URL template for the contributor homepages ...
paths: # The configuration for the paths to include in t...
  templates_dir: _templates # The path to the templates directory.
  drafts_dir: _output # The path to the output directory for generated ...
  publish_dir: _publish # The path to the output directory for rendered f...
  custom_mappings_dir: _mappings # The path to the directory containing user-defin...
templates: # Patterns for dynamically forming strings.
  draft_filename: | # The filename template for the draft files.
    {{ version }}.{{ format_ext }}
  publish_filename: | # The filename template for the published files.
    release-history-{{ version }}.{{ format_ext }}
  chid: {{- this.tick }}-{{ this.summ | truncate: 20 | slugify }}
 # The template for automatic change ID/slug const...
  markdown_frontmatter: | # Designates the content inserted at the top of M...
    ---
    title: Release History for {{ release.code }}
    version: {{ release.code }}
    date: {{ release.date }}
    ---
  asciidoc_frontmatter: | # Designates the way front-matter is inserted at ...
    :page-title: Release History for {{ release.code }}
    :page-version: {{ release.code }}
    :page-date: {{ release.date }}
  html_frontmatter: | # Designates the way front-matter is inserted at ...
    ---
    title: Release History for {{ release.code }}
    version: {{ release.code }}
    date: {{ release.date }}
    ---
  git_version_tag: | # For "raw Git" setups, this is the Git-tag label...
    v{{ version }}
  git_version_tag_pattern: | # The Regular Expressions pattern to count as a p...
    /^v\d+\.\d+\.\d+$/
  git_branch_name: | # For "raw Git" setups, this is the Git-branch na...
    v{{ version }}
modes: # Default settings for rhx command executions.
  wrapped: false # Include (or exclude) head, header, and footer e...
  html_frontmatter: true # Include frontmatter in the rendered HTML.
  markdown_frontmatter: false # Include frontmatter in Markdown drafts.
  # asciidoc_frontmatter: # (Boolean) # Include frontmatter in AsciiDoc drafts.
  fetch: notes-only # What to fetch when gathering issues from API.
rhyml: # Settings related to RHYML data objects and docu...
  markup: markdown # The markup format for the note or memo properti...
  empty_notes: skip # What to do for issues that lack a release note ...
  max_parts: 1 # The maximum number of affected part categories ...
  pasterize_summ: false # Whether to convert verbs in the summ property t...
  pasterize_head: false # Whether to convert verbs in the head property t...
history: # Configurations for the overall document, when a...
  head: | # The header for the release history output.
    Release History -- {{ release.code }} - {{ release.date }}
  htag: h1 # The heading level (H1, H2, etc) for the release...
  items: # Settings pertaining to displayed items across C...
    allow_redundant: false # Whether to allow duplicate entries in a given s...
    issue_links: false # Whether to include web links in item metadata.
    git_links: false # Whether to include git links in item metadata.
    metadata_labels: before # If and where to display icons in relation to la...
    metadata_icons: before # Whether to include icons for metadata in item m...
changelog: # The configuration for the changelog output.
  head: Changelog # The header for the changelog output.
  htag: h2 # The heading level (H1, H2, etc) for the changel...
  spot: 2 # Where in the document to place the changelog (1...
  sort: # The sort order for the changelog output.
    - part:grouping1
  items: # Settings that affect the frame/shape and arrang...
    frame: table-cols-1 # The layout for the changelog entry display.
    allow_redundant: false # Whether to allow duplicate entries in a given s...
    git_links: false # Whether to include git links in item metadata.
    issue_links: false # Whether to include web links in item metadata.
    metadata_labels: before # If and where to display icons in relation to la...
    metadata_icons: before # Whether to include icons for metadata in item m...
notes: # The configuration for the Release Notes listing...
  head: Release Notes # The header for the notes output.
  htag: h2 # The heading level (H1, H2, etc) for the release...
  spot: 1 # Where in the document to place the Release Note...
  sort: # The sort order for the release notes output.
    - highlight:grouping1
    - deprecation:grouping1
    - removal:grouping1
    - breaking:grouping1
    - type:grouping1
    - part:grouping2
  items: # Settings that affect the frame/shape and arrang...
    frame: table-cols-1 # The layout for the release-note item display.
    allow_redundant: false # Whether to allow duplicate entries in a given s...
    git_links: false # Whether to include git links in item metadata.
    issue_links: false # Whether to include web links in item metadata.
    metadata_labels: before # If and where to display icons in relation to la...
    metadata_icons: before # Whether to include icons for metadata in item m...</code></pre>
</div>
</div>
<div class="sect3">
<h4 id="custom-api-config">Custom API Configuration</h4>
<div class="paragraph">
<p>ReleaseHx&#8217;s API connections are extensible.
As long as you can map the JSON payload returned by your preferred issue-management system provider, you are welcome to add it.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
If you add a non-standard API, please consider contributing it upstream to the ReleaseHx project.
</td>
</tr>
</table>
</div>
</div>
</div>
<div class="sect2">
<h3 id="_rhyml_schema">RHYML Schema</h3>

</div>
<div class="sect2">
<h3 id="_templating_guide">Templating Guide</h3>
<div class="paragraph">
<p>ReleaseHx generally uses enhanced <strong>Liquid 4 templates</strong> to generate new files and content from RHYML and configuration data.</p>
</div>
<div class="paragraph">
<p>Notably, it employs <strong><a href="https://jekyllrb.com/docs/liquid/">Jekyll&#8217;s extended tags and filters</a></strong>, as well as some additional tag and several filters provided by Sourcerer.</p>
</div>
<div class="paragraph">
<p>Here we document the custom filters added by the Sourcerer module and ReleaseHx itself.</p>
</div>
<div class="sect3">
<h4 id="_custom_liquid_tags">Custom Liquid Tags</h4>
<div class="paragraph">
<p>ReleaseHx uses Jekyll&#8217;s version of the <code>include</code> tag, rather than Liquid&#8217;s.
It also supports Jekyll&#8217;s <code>include_relative</code> tag.</p>
</div>
<div class="paragraph">
<p><a href="https://jekyllrb.com/docs/includes/">See Jekyll&#8217;s docs for more information.</a></p>
</div>
<div class="paragraph">
<p>This works essentially like Liquid 5&#8217;s <code>render</code> tag, which is not available in ReleaseHx.</p>
</div>
<div class="paragraph">
<p>ReleaseHx supports a tag called <code>embed</code> which takes no arguments and works exactlly like Liquid&#8217;s <code>include</code> tag.</p>
</div>
<div class="paragraph">
<p>The included file has access to all the variables in the parent template and passes any newly created or modified variables back to affect any subsequent content in the parent template.</p>
</div>
</div>
<div class="sect3">
<h4 id="_custom_liquid_filters">Custom Liquid Filters</h4>
<div class="paragraph">
<p>These filters can be added to <a href="https://shopify.github.io/liquid/">Liquid&#8217;s master list of filters</a> and <a href="https://jekyllrb.com/docs/liquid/filters">Jekyll&#8217;s extended filters</a>.
Jekyll filters always supercede same-named Liquid filters, including <code>where</code>.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">plusify</dt>
<dd>
<p>Replace double line breaks (<code>\n\n</code>) with <code>\n+\n</code>.</p>
<div class="dlist">
<dl>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ note | plusify }}</code></p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">md_to_asciidoc</dt>
<dd>
<p>Uses Kramdown-AsciiDoc (Kramdoc) to convert Markdown to AsciiDoc.</p>
<div class="dlist">
<dl>
<dt class="hdlist1">arguments</dt>
<dd>
<div class="dlist">
<dl>
<dt class="hdlist1">wrap</dt>
<dd>
<p>How to handle line wrapping.
Can be 'preserve', 'ventilate', or 'none'.
The <code>ventilate</code> option presents places all sentences on their own lines.</p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ note | md_to_asciidoc: "ventilate" }}</code></p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">render</dt>
<dd>
<p>Renders a string as a Liquid template with the provided variables.</p>
<div class="dlist">
<dl>
<dt class="hdlist1">arguments</dt>
<dd>
<div class="dlist">
<dl>
<dt class="hdlist1">vars</dt>
<dd>
<p>A hash of variables to pass to the template.</p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ note | render: vars }}</code></p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">indent</dt>
<dd>
<p>Indents each line of the input by the specified number of spaces.</p>
<div class="dlist">
<dl>
<dt class="hdlist1">arguments</dt>
<dd>
<div class="dlist">
<dl>
<dt class="hdlist1">spaces</dt>
<dd>
<p>The number of spaces to indent by.</p>
</dd>
<dt class="hdlist1">line1</dt>
<dd>
<p>If true, also indents the first line.</p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ note | indent: 2, line1: true }}</code></p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">sgyml_type</dt>
<dd>
<div class="openblock">
<div class="content">
<div class="paragraph">
<p>Returns a string representing the SGYML <em>kind</em> and <em>class</em> of the input, separated by a colon (<code>:</code>).</p>
</div>
<div class="paragraph">
<p>Response will be one of the following:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>Null:nil</code></p>
</li>
<li>
<p><code>Scalar:String</code></p>
</li>
<li>
<p><code>Scalar:Number</code></p>
</li>
<li>
<p><code>Scalar:DateTime</code></p>
</li>
<li>
<p><code>Scalar:Boolean</code></p>
</li>
<li>
<p><code>Enumerable:Array</code></p>
</li>
<li>
<p><code>Enumerable:ArrayList</code></p>
</li>
<li>
<p><code>Enumerable:Map</code></p>
</li>
<li>
<p><code>unknown:unknown</code></p>
</li>
</ul>
</div>
</div>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ id | type_check }}</code></p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">ruby_class</dt>
<dd>
<p>Returns the Ruby class name of the input.</p>
<div class="dlist">
<dl>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ id | ruby_class }}</code></p>
</dd>
</dl>
</div>
</dd>
<dt class="hdlist1">demarkupify</dt>
<dd>
<p>Simplifies any Markdown and AsciiDoc syntax in the inline input.</p>
<div class="paragraph">
<p>Strips <code>*</code> and <code>_</code> quotes, simplifies <code>"`</code> and <code>'`</code> quotes and UTF-8 curly quotes, and removes all backticks.
example:::
<code>{{ note | demarkupify }}</code></p>
</div>
</dd>
<dt class="hdlist1">pasterize</dt>
<dd>
<p>Converts select verbs in the input from present/imperative to past tense.</p>
<div class="paragraph">
<p>Replaces common terms like <code>add</code>/<code>adds</code> with <code>added</code>, <code>fix</code>/<code>fixes</code> with <code>fixed</code>, <code>build</code>/<code>builds</code> with <code>built</code>, etc.
example:::
<code>{{ summary | pasterize }}</code></p>
</div>
</dd>
<dt class="hdlist1">inspect_yaml</dt>
<dd>
<p>Returns a YAML representation of the input for debugging purposes.</p>
<div class="dlist">
<dl>
<dt class="hdlist1">example</dt>
<dd>
<p><code>{{ changes | inspect_yaml }}</code></p>
</dd>
</dl>
</div>
</dd>
</dl>
</div>
</div>
</div>
<div class="sect2">
<h3 id="releasehx-api">ReleaseHx API</h3>
<div class="paragraph">
<p>Full documentation is coming, but for now just an overview of the classes and methods introduced by this gem.</p>
</div>
<div class="sect3">
<h4 id="_classes">Classes</h4>
<div class="dlist">
<dl>
<dt class="hdlist1">Release</dt>
<dd>
<p>A collection of changes made to the product since the last release.
Includes release metadata and an Array of Change objects.</p>
</dd>
<dt class="hdlist1">Change</dt>
<dd>
<p>The record of a single user-facing change made to the product.
Includes metadata about the Release to which it belongs.</p>
</dd>
</dl>
</div>
</div>
</div>
</div>
</div>
<div class="sect1">
<h2 id="_development">Development</h2>
<div class="sectionbody">
<div class="paragraph">
<p>ReleaseHx is free, open source, and <strong>open for contributions</strong>.
Get in touch or open an issue to get involved.</p>
</div>
<div class="sect2">
<h3 id="_background">Background</h3>
<div class="paragraph">
<p>ReleaseHx is my fourth or fifth time tackling this problem, though in previous cases I had the misfortune (luxury?) of solving it for one company at a time, and never in such a robust way that the results would be worth open sourcing.</p>
</div>
<div class="paragraph">
<p>All of my employers and clients in the past 10 years needed a system like this, and some of them paid me to make them.
But before all that, I inherited a Python script that converted JIRA issue fields into AsciiDoc, for rendering to PDF and HTML.</p>
</div>
<div class="paragraph">
<p>Using and hacking at that script for years of documenting GA releases for an entire enterprise software product made me appreciate the need for cloud sourcing and automation, for tne benefit of all stakeholders.</p>
</div>
<div class="paragraph">
<p>I also spent years hanging in the #release-notes channel of the <a href="https:writethedocs.org/slack">Write the Docs Slack</a>, over and over again advising technical writers on this broad problem space.
&#8220;How do you convert JIRA tickets to release notes?&#8221; or &#8220;How do you automate a changelog from Git or GitHub Issues?&#8221; are typical inquiries there.</p>
</div>
<div class="paragraph">
<p>The short answer is:</p>
</div>
<div class="olist arabic">
<ol class="arabic">
<li>
<p>Use your team&#8217;s preferred scripting language to:</p>
<div class="olist lowerroman">
<ol class="lowerroman" type="i">
<li>
<p>connect to the REST API, then</p>
</li>
<li>
<p>download issues for the upcoming release.</p>
</li>
</ol>
</div>
</li>
<li>
<p>Template notes or log entries into your preferred lightweight markup.</p>
<div class="olist loweralpha">
<ol class="loweralpha" type="a">
<li>
<p>Markdowwn <strong>or</strong></p>
</li>
<li>
<p>AsciiDoc</p>
</li>
</ol>
</div>
</li>
<li>
<p>Edit the content manually in Markdown/AsciiDoc, then:</p>
<div class="olist loweralpha">
<ol class="loweralpha" type="a">
<li>
<p>commit it to your existing docs SSG <strong>or</strong></p>
</li>
<li>
<p>push it to Confluence or the product&#8217;s deployment pipeline</p>
</li>
</ol>
</div>
</li>
</ol>
</div>
<div class="paragraph">
<p>Easier said than done, especially for TWs working with scarce developer resources.</p>
</div>
</div>
<div class="sect2">
<h3 id="ddd">Docs-driven Development</h3>
<div class="paragraph">
<p>The ReleaseHx gem is an example of README-first development.
Not only is the README written in AsciiDoc, and not only is documentation done first in the README during early development, but much of the documentation and product data is single-sourced in the README.</p>
</div>
<div class="paragraph">
<p>This unorthodox approach requires some explanation.</p>
</div>
<div class="paragraph">
<p>At the very beginning of the build procedure for the application&#8217;s Ruby gem, (1) data is ingested and (2) snippets are harvested from the <code>README.adoc</code> file for internal or user-facing purposes in the application.</p>
</div>
<div class="paragraph">
<p>For instance, the application version number is derived from the attribute <code>:this_prod_vrsn:</code> in the README.
See, I can even place it here without fear that it will ever fall out of step:
<strong>0.1.0</strong>.</p>
</div>
<div class="paragraph">
<p>Additionally, the help screen itself is sourced here in this README and included verbatim&#8201;&#8212;&#8201;after rendering with Asciidoctor&#8201;&#8212;&#8201;in the CLI&#8217;s output.</p>
</div>
<div class="paragraph">
<p>The manpage documentation is also sourced in this way.</p>
</div>
<div class="paragraph">
<p>This capability is provided by the Sourcerer module introduced in this gem but intended to be spun off into it own gem for use in all my (and any of your) Ruby projects in the future.</p>
</div>
</div>
<div class="sect2">
<h3 id="_issue_data_mapping">Issue-data Mapping</h3>
<table class="tableblock frame-all grid-all stretch">
<caption class="title">Table 3. Upstream/source issue data mapping table</caption>
<colgroup>
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
<col style="width: 20%;">
</colgroup>
<thead>
<tr>
<th class="tableblock halign-left valign-top">JIRA</th>
<th class="tableblock halign-left valign-top">GitHub</th>
<th class="tableblock halign-left valign-top">GitLab</th>
<th class="tableblock halign-left valign-top">RHYML</th>
<th class="tableblock halign-left valign-top">Ruby/Liquid</th>
</tr>
</thead>
<tbody>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>chid</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>changeid</code> / <code>chid</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>key</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>number</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>iid</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tick</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>ticketid</code> / <code>tick</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>commit</code> (custom field, if available)</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>associated commit hash (via PR merge or commit references)</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>commit hash from merge request or commit ref</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>hash</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>githash</code> / <code>hash</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>issuetype</code> or label matching slug in <code>types</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>type</code> or label matching slug in <code>types</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>issue_type</code> or label matching slug in <code>types</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>type</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>type</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>component</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>component:&lt;component&gt;</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>component::&lt;component&gt;</code> or scoped labels</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>part</code> or <code>component</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>part</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>summary</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>title</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>title</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>summ</code> | <code>summary</code> | <code>title</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>summary</code> | <code>summ</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>head</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>headline</code> / <code>head</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><em>custom field</em></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>## Release Note</code> in body</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>## Release Note</code> in body</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>note</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>note</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: `deprecation `</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: `deprecation `</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>deprecation</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags: [deprecation]</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags['deprecation']</code> / <code>deprecation</code> Boolean</p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>breaking</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>breaking</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>breaking</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>`tags: [breaking] `</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags['breaking']</code> / <code>breaking</code> Boolean</p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>experimental</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>experimental</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>experimental</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags: [experimental]</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags['experimental']</code> / <code>experimental</code> Boolean</p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>highlight</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>highlight</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>highlight</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags: [highlight]</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>tags['highlight']</code> / <code>highlight</code> Boolean</p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>release_note_needed</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>release_note_needed</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>release_note_needed</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>changelog</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>changelog</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>label: <code>changelog</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>N/A</p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>fixVersions</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>milestone</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>milestone</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>nested context</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>revision</code> / <code>version</code></p>
</div></div></td>
</tr>
<tr>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>assignee</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>assignee</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>assignee</code></p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p>`lead `</p>
</div></div></td>
<td class="tableblock halign-left valign-top"><div class="content"><div class="paragraph">
<p><code>contributor</code></p>
</div></div></td>
</tr>
</tbody>
</table>
</div>
<div class="sect2">
<h3 id="_codebase_structure">Codebase Structure</h3>
<div class="paragraph">
<p>Here is a model of the ReleaseHx gem&#8217;s codebase.</p>
</div>
<div class="listingblock">
<div class="title">File tree of key files</div>
<div class="content">
<pre class="highlight"><code class="language-text" data-lang="text">build/                           # Untracked, ephemeral path for generated assets
docs/
└── manpage.adoc                 # &lt;1&gt; Source for CLI manual page
lib/
├── releasehx.rb                 # Application core
├── schemagraphy.rb              # Special YAML handling
├── sourcerer.rb                 # &lt;2&gt; Single-sourcing tool
├── liquid.rb                    # &lt;3&gt; Liquid customizations
├── releasehx/
│   ├── cli.rb
│   ├── configuration.rb         # &lt;4&gt; CFGYML parsing
│   ├── rhyml/                   # module RHYML
│   │   ├── change.rb            # class Change
│   │   ├── release.rb           # class Release
│   │   ├── adapter.rb           # maps from JSON using a mapping file
│   │   ├── loader.rb            # loads RHYML YAML or JSON from disk
│   │   ├── validator.rb         # validates against schema
│   │   └── renderer.rb          # to_h, to_yaml, to_liquid, etc.
│   ├── sgyml/helpers.rb         # module SgymlHelpers
│   ├── generators/
│   │   ├── parsers.rb           # &lt;5&gt; Draft processes
│   │   └── renderers.rb         # &lt;6&gt; Rich-text conversion processes
│   ├── templates/               # Docs-generating templates
│   │   ├── cfgyml/              # Config-definition parsing
│   │   ├── api-model/           # Docs for API -&gt; RHYML mapping config
│   │   └── rhyml/               # Docs for RHYML object
│   └── utilities/
│       ├── git.rb               # Git integration (Rugged)
│       ├── asciidocify-md.rb    # Markdown to AsciiDoc
│       └── validators.rb        # Check special YAML files (RHYML, CFGYML)
├── schemagraphy/
│   ├── loader.rb                # `SchemaGraphy::Loader`
│   ├── tag_utils.rb             # `detag`, `tag_of`, etc
│   ├── templating.rb            # Definess handling of parsable YAML nodes
│   └── schema_utils.rb          # `get_schema_defaults`, etc
└── sourcerer/
    ├── builder.rb               # Writes snippets to files at build time
    └── plaintext-converter.rb   # Pre-processes AsciiDoc source files
specs/
└──  config-def.yml              # &lt;7&gt; Configuration definition
└──  rhyml-schema.yaml           # &lt;8&gt; RHYML schema definition
_templates/                      # &lt;9&gt; User-overridable templates
Dockerfile                       # Docker image definition
releasehx.gemspec                # Gem definition
README.adoc                      # &lt;10&gt; Single source of truth</code></pre>
</div>
</div>
<div class="olist arabic callouts">
<ol class="arabic">
<li>
<p>Used to generate a terminal manual page.</p>
</li>
<li>
<p>See <a href="#sourcerer">Sourcerer</a>.</p>
</li>
<li>
<p>See <a href="#config-def">Configuration Definition (CFGYML)</a>.</p>
</li>
<li>
<p>See <a href="#issue-sources">Establishing Issues Source</a>.</p>
</li>
<li>
<p>See <a href="#rhyml">RHYML</a>.</p>
</li>
<li>
<p>Parsers use Liquid to generate YAML, Markdown, and AsciiDoc drafts.</p>
</li>
<li>
<p>Renderers use Asciidoctor, Pandoc, or other converters to generate HTML and PDF.</p>
</li>
<li>
<p>See <a href="#config-def">Configuration Definition (CFGYML)</a>.</p>
</li>
<li>
<p>See <a href="#rhyml">RHYML</a>.</p>
</li>
<li>
<p>Templates for draft and output markup, which users can override.</p>
</li>
<li>
<p>See <a href="#ddd">Docs-driven Development</a>.</p>
</li>
</ol>
</div>
</div>
<div class="sect2">
<h3 id="sourcerer">Sourcerer</h3>
<div class="paragraph">
<p>This gem introduces a module called Sourcerer, by which AsciiDoc files can be parsed and their contents harvested for use in the application build.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Sourcerer is intended to be spun off as its own gem once it successfully proves the concept in this gem.
It will probably be called AsciiSourcerer and may replace LiquiDoc.
</td>
</tr>
</table>
</div>
<div class="paragraph">
<p>It is invoked in the <code>releasehx.gemspec</code> file, establishing global namespaces:</p>
</div>
<div class="ulist">
<ul>
<li>
<p><code>ReleaseHx::ATTRIBUTES[:globals]</code> (derived from this <code>README.adoc</code> file)</p>
</li>
<li>
<p><code>ReleaseHx.read_built_snippet()</code> (such as <code>:helpscreen</code>)</p>
</li>
</ul>
</div>
<div class="paragraph">
<p>The Sourcerer module also generates files like <code>build/docs/manpage.adoc</code>, which generates the formatted terminal manual, using content from <code>build/docs/config-reference.adoc</code> and this README (<code>tags="cli_options"</code>, for instance).</p>
</div>
<div class="paragraph">
<p>It also generates an AsciiDoc-formatted configuration reference and a sample config using the <code>config-def.yml</code> file.</p>
</div>
<div class="paragraph">
<p>This is mostly just showing off what Sourcerer can do, and hopefully setting into habit some best practices for my more complicated apps.</p>
</div>
</div>
<div class="sect2">
<h3 id="_schemagraphy">SchemaGraphy</h3>
<div class="paragraph">
<p>This gem also introduces a module that derives from an unreleased gem I have been working on for some years: SchemaGraphy.</p>
</div>
<div class="paragraph">
<p>SchemaGraphy is basically an extension of YAML, enabling Ruby developers to powerfully interpret and schematize YAML-based data.
Most relevant to our case, as enabled by the <code>SchemaGraphy</code> module in this gem, is its handling of <strong>YAML custom tags</strong> as well as what I am calling <strong>&#8220;templated fields&#8221;</strong>, where the value of a YAML node is a String that is intended to be further processed by a templating engine like Liquid or ERB.</p>
</div>
<div class="paragraph">
<p>SchemaGraphy facilitates handling these and other quirky power-ups we use with our fully valid YAML files, so low-code users can pass some dynamism along in their YAML configs and so forth.</p>
</div>
<div class="sect3">
<h4 id="_custom_yaml_tag_handling">Custom YAML Tag Handling</h4>
<div class="paragraph">
<p>To enable end users to pass meta-instructions along with their data, wherever it will make sense to do so, SchemaGraphy offers a straightforward handling system.</p>
</div>
<div class="paragraph">
<p>Wherever you parse YAML-formatted data using <code>.load_yaml_with_tags</code>, custom-tagged Scalar nodes are converted into Maps like so:</p>
</div>
<div class="listingblock">
<div class="title">User&#8217;s YAML</div>
<div class="content">
<pre class="highlight"><code class="language-yaml" data-lang="yaml">some_property: !liquid "{{ some_value }}"</code></pre>
</div>
</div>
<div class="listingblock">
<div class="title">Converted data TagMap</div>
<div class="content">
<pre class="highlight"><code class="language-yaml" data-lang="yaml">some_property:
  __tag__: liquid
  value: "{{ some_value }}"</code></pre>
</div>
</div>
<div class="paragraph">
<p>Developers may therefore conditionally interpret ingested data based on user-defined classifications, wherever the developer supports such things.</p>
</div>
<div class="paragraph">
<p>Whether a Scalar has been transformed into a TagMap, you can resolve it using:</p>
</div>
<div class="listingblock">
<div class="content">
<pre class="highlight"><code class="language-ruby" data-lang="ruby">SchemaGraphy::TagUtils.detag(some_property)
# Or, with a local alias
detag = -&gt;(val) { SchemaGraphy::TagUtils.detag(val) }
detag(some_property)</code></pre>
</div>
</div>
<div class="paragraph">
<p>When tags are used this way, to convey a syntax/engine for processing a template or other dynamic content, SchemaGraphy can even help us handle the content in the manner designated by the tag.
This will come up again in <a href="#templated-fields">the next section</a>.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
This capability is only available on Scalar node values.
For now, tags applied to other compound node types (Arrays/sequences, Maps/mappings) will be ignored by SchemaGraphy interpreters.
</td>
</tr>
</table>
</div>
<div class="admonitionblock warning">
<table>
<tr>
<td class="icon">
<div class="title">Warning</div>
</td>
<td class="content">
When you use <code>load_yaml_with_tags</code>, you will will errors downstream if a user places a tag on a node where you do not expect it.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="templated-fields">Templated Property Values in YAML</h4>
<div class="paragraph">
<p>We are calling these &#8220;templated fields&#8221; to specify that we are talking about enabling end users to use Liquid, ERB, or eventually other templating syntaxes in YAML node values.</p>
</div>
<div class="paragraph">
<p>In so doing, developer are able to designate that the value of certain YAML nodes should be handled by a templating engine, as well as when and how.</p>
</div>
<div class="paragraph">
<p>We&#8217;ll look at how this is done in <a href="#templated-fields-handling">Dynamic Templated-field Handling</a>.
For now, the point is that sometimes files like <code>config-def.yml</code> or an API-mapping file call for a little more runtime dynamism than a low-code solution like pure YAML can support.</p>
</div>
<div class="paragraph">
<p>Therefore, when the value of a user-configurable or environment-deterimined &#8220;setting&#8221; is a string that must be generated from data defined outside that field, we parse and render the template at runtime, using data from the environment or elsewhere.
For now, it is up to our calling code to provide the appropriate variables to the template depending on the context.</p>
</div>
</div>
<div class="sect3">
<h4 id="config-def">Configuration Definition (CFGYML)</h4>
<div class="paragraph">
<p>All user-configurable settings have a definition, if not also a default value.
For single-sourcing purposes, these are defined in a YAML format called CFGYML&#8201;&#8212;&#8201;a configuration-file modeling language.</p>
</div>
<div class="paragraph">
<p>The file is at <code>./specs/config-def.yml</code>.
It is used to establish the literal default settings carried by the applicaton, and also to document those settings for the user.</p>
</div>
<div class="paragraph">
<p>This practice lets developers give end users extremely detailed configurations, always well documented.</p>
</div>
<div class="paragraph">
<p>The basic schema is somewhat straightforward.
Essentially, you&#8217;re nesting Map objects within a YAML key <code>properties</code>, and each property (setting) of the defined config file can be described and constrained.</p>
</div>
<div class="paragraph">
<p>Each setting can have a type, description (<code>desc</code>), default (<code>dflt</code>), and templated-field instructions (<code>templating</code>).
If the setting is itself a of type <code>Map</code> (YAML &#8220;mapping&#8221;, JSON &#8220;object&#8221;), its own nested parameters can be estalished with a <code>properties:</code> block.</p>
</div>
<div class="paragraph">
<p>For now, you can designate the type, which you will have to enforce in your code, as well as a default value.</p>
</div>
</div>
<div class="sect3">
<h4 id="sgyml-schemas">SGYML Schemas</h4>
<div class="paragraph">
<p>Similar to but more complicated than CFGYML definition files are SchemaGraphy schema files.
This is a partially specified, partially developed, and as-yet-incomplete syntax for designating and constraining YAML documents.</p>
</div>
<div class="paragraph">
<p>ReleaseHx at this time makes active use of only minimal aspects of these schemas, all of whcih are contained in the <code>spects/</code> directory at the root of the gem source.</p>
</div>
<div class="paragraph">
<p>Each of the YAML formats used by ReleaseHx has its own schema in the repo.
The cfgyml-schema.yaml file will eventually be spun off, but the <code>rhyml-schema.yaml</code> and <code>rhyml-mapping-schema.yaml</code> files will stay here, defining valid formts for the types of files they apply to.</p>
</div>
<div class="paragraph">
<p>Since SchemaGraphy itself is still unreleased, CFGYML as introduced in this gem offers only a subset of what it will enable down the road.</p>
</div>
<div class="paragraph">
<p>Once SchemaGraphy is generally available, this gem will call it as a dependency.
At that point, a file like <code>config-def.yml</code> (CFGYML) will be able to impose a more detailed <code>$schema</code> for any property.</p>
</div>
</div>
<div class="sect3">
<h4 id="templated-fields-handling">Dynamic Templated-field Handling</h4>
<div class="paragraph">
<p>The most powerful function of SchemaGraphy schemas that is now available in ReleaseHx is the ability to instruct how temlated fields should be processed at different stages, and also to parse and render them as needed.</p>
</div>
<div class="paragraph">
<p>Templated-field handling can be established between a combination of (1) CFGYML definition files or SGYML schema files and (2) configuration files to be applied at runtime.</p>
</div>
<div class="paragraph">
<p>Developers can designate a given property to be <code>type: Template</code> in a schema or definition.
This &#8220;typing&#8221; can be a trigger for downstream parsing/rendering of the template.</p>
</div>
<div class="admonitionblock note">
<table>
<tr>
<td class="icon">
<div class="title">Note</div>
</td>
<td class="content">
Liquid uses these two stages.
The <em>parse</em> operation copiles a template into a <code>Liquid::Template</code> object.
The <em>render</em> operation applies a dataset to the loaded template, generating a String with Liquid tags resolved.
</td>
</tr>
</table>
</div>
</div>
<div class="sect3">
<h4 id="nyi">Not Yet Implemented</h4>
<div class="paragraph">
<p>Most aspcts of SchemaGraphy/SGYML are not yet available in ReleaseHx, but some are worth pointing out.</p>
</div>
<div class="dlist">
<dl>
<dt class="hdlist1">data types</dt>
<dd>
<p>As of now, the <code>type</code> node of any property in <code>config-def.yml</code> is not particularly functional.
I do have a whole table of &#8220;data types&#8221; in SGYML, most of which are extremely self-explanatory and drawn from fairly platonic, cross-language terms.</p>
<div class="paragraph">
<p>However, these are entirely unenforced in ReleaseHx&#8201;&#8212;&#8201;for now, data still has to be type checked explicitly in the Ruby code, and user configs are not validated against any kind of typing system.</p>
</div>
</dd>
<dt class="hdlist1">schema docs</dt>
<dd>
<p>The schema files do not yet generate complete reference docs for the subject files that they govern.
So for instance, you&#8217;ll have to read files like <code>rhyml-schema.yaml</code> and <code>rhyml-mapping-schema.yaml</code> directly to understand the format of RHYML files.</p>
</dd>
</dl>
</div>
</div>
</div>
</div>
</div>
</div>
<div id="footer">
<div id="footer-text">
Last updated 2025-05-17 19:32:03 -0400
</div>
</div>
</body>
</html>