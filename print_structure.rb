require 'asciidoctor'
require 'yaml'

def node_to_hash(node)
  return {} unless node.respond_to?(:node_name) # Ensure node responds to node_name
  
  result = { type: node.node_name }

  result[:id] = node.id if node.respond_to?(:id) && node.id
  result[:title] = node.title if node.respond_to?(:title) && node.title
  result[:content] = node.content.split[0..4].join(' ') + '...' if node.content && node.node_name == 'paragraph'
  result[:language] = node.attributes['language'] if node.node_name == 'code' && node.attributes['language']
  result[:level] = node.level if node.respond_to?(:level) && !node.level.nil?
  result[:format] = node.style if node.respond_to?(:style) && node.style
  result[:roles] = node.roles.join(' ') if node.respond_to?(:roles) && !node.roles.empty?
  result[:header] = true if node.node_name == 'table' && node.attributes.include?('header_option')
  result[:nesting] = node.items.size if ['ulist', 'olist', 'dlist'].include?(node.node_name) && node.items
  
  if node.respond_to?(:blocks) && !node.blocks.empty?
    result[:blocks] = node.blocks.map { |child| node_to_hash(child) }.compact
  end

  result.compact
end

def document_to_yaml(file_path)
  doc = Asciidoctor.load_file(file_path, safe: :safe)
  doc_hash = {
    type: 'document',
    title: doc.doctitle,
    author: doc.author,
    sections: doc.blocks.map { |block| node_to_hash(block) }
  }

  doc_hash[:id] = doc.id if doc.id

  YAML.dump(doc_hash)
end

file_path = 'asciidoc-sample.adoc'  # Replace with the path to your AsciiDoc document
yaml_output = document_to_yaml(file_path)
puts yaml_output
